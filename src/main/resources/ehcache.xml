<config xmlns="http://www.ehcache.org/v3">
    <cache-template name="default">
        <expiry>
            <ttl unit="minutes">30</ttl>
        </expiry>
        <resources>
            <heap unit="entries">1000</heap>
            <offheap unit="MB">20</offheap>
        </resources>
    </cache-template>

    <!-- 更精细的缓存区域配置 -->
<!--    <cache alias="com.fund.domain.StockPriceInfo" uses-template="default">-->
<!--        <expiry>-->
<!--            <ttl unit="minutes">15</ttl>-->
<!--        </expiry>-->
<!--    </cache>-->

    <!-- 查询缓存 -->
    <cache alias="org.hibernate.cache.internal.StandardQueryCache">
        <expiry>
            <ttl unit="minutes">10</ttl>
        </expiry>
        <resources>
            <heap unit="entries">2000</heap>
        </resources>
    </cache>

    <!-- 更新时间戳缓存 -->
    <cache alias="org.hibernate.cache.spi.UpdateTimestampsCache">
        <expiry>
            <none/>
        </expiry>
        <resources>
            <heap unit="entries">5000</heap>
        </resources>
    </cache>
</config>
