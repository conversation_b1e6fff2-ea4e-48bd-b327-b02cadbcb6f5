server:
  tomcat:
    threads:
      max: 1000
    max-connections: 10000
    accept-count: 1000
  #添加接口响应压缩：
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/plain
    min-response-size: 2048
  #配置静态资源缓存：
  resources:
    cache:
      period: 3600
      cachecontrol:
        max-age: 3600
#用户必须要有权限
binlog:
  # 监听数据库,如填写监听库下面的所有表,如只是单独的表只填写dbTable,不需要监听则需要ignoreTable,synData开启则同步到从库中
  #系统默认同步s_document表下面的文件，enable是开启binlog服务,如不需要则不用
  host: **************
  port: 33063
  username: boot
  password: cisdiboot
  # 监听数据库格式[库,库]
  database: test
  # 监听数据库与表,隔开，格式[库.表,,,]
  dbTable: test.s_document
  ignoreTable: s_user_log,s_logs
  serverId: 1
  enable: false
  synData: true
debug: false
logging:
  config: classpath:logback-spring.xml
# Actuator 配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,loggers
      base-path: /actuator
      cors:
        allowed-origins: "*"
        allowed-methods: GET
  endpoint:
    health:
      show-details: always
#Ocr识别库
tess4j:
  datapath: /usr/local/share/tessdata/
spring:
  main:
    allow-circular-references: true

  security:
    user:
      name: admin
      password: admin123
  data:
    redis:
      host: **************
      port: 6379
      password: redis123!
      database: 0
      # 添加连接池配置
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 2
          max-wait: 1000ms
      # 添加超时配置
      timeout: 5000ms
  profiles:
    active: prod
  jpa:
    show-sql: true
    properties:
      hibernate:
        session_factory:
          statement_inspector: com.sys.core.config.sharding.TableNameStatementInspector
        jdbc:
          batch_size: 500
          order_inserts: true
          order_updates: true
          flush_mode: COMMIT
          show_sql: true
          format_sql: false
          use_sql_comments: true
          flush_generate_statistics: true
        cache:
          use_second_level_cache: false
          use_query_cache: false
          region:
            factory_class: org.hibernate.cache.jcache.JCacheRegionFactory
      jakarta:
        cache:
          provider: org.ehcache.jsr107.EhcacheCachingProvider
          uri: classpath:ehcache.xml
        persistence:
          query:
            cacheable: true
            timeout: 30000
        generate_statistics: true
    open-in-view: false
  jackson:
    deserialization:
      fail-on-unknown-properties: false
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: UTC
  freemarker:
    charset: utf-8
    check-template-location: false
  task:
    execution:
      pool:
        core-size: 8
        max-size: 16
        queue-capacity: 100
#####基本配置######
app:
  # 缓存配置
  cache:
    names: appPageCache,btnPageCache,tableShardCache,mainCache,countCache,sumCache,entityCache,pageCache,tokenCache,sessionCache,tempCache
#    mode: local   # none, redis, local
    mode: local   # none, redis, local
    redis:
      ttl: 60
    local:
      ttl: 60
      max-size: 10000
  # IP限制配置
  ip-limit:
    # 是否启用IP限制
    enabled: false
    # IP白名单，多个IP用逗号分隔
    whitelist: 127.0.0.1,***********,************,************
    # IP黑名单，多个IP用逗号分隔（新增）
    blacklist: *********,*************
    # 忽略的路径，支持通配符 * 和 **
    ignore-paths: /static/**,/assets/**,*.js,*.css,*.jpg,*.png,*.gif,*.ico,/favicon.ico
    window-size-seconds: 1
    window-count: 10
    request-limit: 5
    lock-time-seconds: 60
  # 跨域配置
  cors:
    # 允许的源，多个源使用逗号分隔
    allowed-origins: http://localhost:8080,http://localhost:3000,http://localhost
    # 允许的HTTP方法
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    # 允许的请求头
    allowed-headers: '*'
    # 是否允许发送Cookie
    allow-credentials: true
    # 预检请求的有效期，单位秒
    max-age: 3600
  #Session验证
  check-urls: /static/flow/config/,/files/report
  login-url: /login
  #分片缓存配置
  sharding:
    performance:
      enable-cache: true        # 是否启用缓存
      cache-max-size: 1000     # 缓存最大大小
      cache-expire-time: 300000 # 缓存过期时间（毫秒）
    strategy: default # 分片策略,enhanced:增强分片策略;default:默认分片策略
  #分片缓存配置
  jwtAuth:
    path: /api/data/,/api/admin/
    #DeepSeek配置
  deepseek:
    api:
      key: ***********************************
      base-url: https://api.deepseek.com/v1
      model: deepseek-chat #deepseek-reasoner,deepseek-chat
# 应用安全配置
  security:
    frame-options:
      # 允许的非同源域名列表（同源访问始终允许）
      allowed-origins:
        - "http://192.168.100.117:8080"

p6spy:
  ignore_table: s_document,s_logs,s_table_sql_record,s_wf_work_item,s_wf_process_ins,s_wf_suggest,s_user_log,s_analysis
#系统基本配置
base:
  path:
  showCfg: true
  userLog: false
  dialect: MySQL5Dialec
  upload: upload
  platform: ydzj
  sm4Key: OhepchW0EWcH3iet0wHbcg==
  platformName: 病案质控DRG管理平台
  reportToken: http://10.186.247.102:10004/system/jwtLogin
#  chromedriver: /Users/<USER>/yzjw/chromedriver_mac64/chromedriver
  chromedriver: C:/yzjw/chromedriver/chromedriver.exe

sys:
  sharding:
    enabled: true



