# OpenDRG的目标是成为国家医保局CHS-DRG的开源实现，就像OpenJDK是Java SE的开源实现一样

## 版本更新
### v1.0 
* 输入病案信息，显示分组过程，结果以及医保支付信息
* 按“测试数据集”标准格式，导入CSV文件，可以下载批量分组结果

### v1.1
* 页面默认使用国临码，如需直接使用医保码可勾上单选框
* 病案分组时自动从国临码转成医保码，转码记录在分组过程记录种显示
* 在支持CSV文件导入的基础上，增加对EXCEL文件格式的支持，系统自动判断文件类型
* 增加支持国考数据文件（HQMS）、卫统数据文件（N041）这两种格式的文件导入

### v1.2
* 分组结果下载文件中，增加"RW"列，自动关联该统筹区的DRG分组权重
* 文件导入分组完成后，显示入组率、DRG组数、CMI这三个关键指标

### v1.3
* 诊断名称提示CC、MCC
* 增加时间消耗指数、费用消耗指数等指标统计
* 增加低风险病种指标统计
* 增加院内科室DRG指标统计

### v1.4
* 增加中医病案N042格式的导入
* 增加多种特殊病案格式导入，自动判断文件中诊断、手术编码字段数量
* 增加DRG费用计算功能，以及专用的测试数据集格式
* 增加DRG例均结算、DRG例均盈亏指标统计

### v1.5
* 增加费用计算功能，点数法、费率法
* 已支持地区：云南曲靖、广西玉林、甘肃庆阳、黑龙江哈尔滨、江西南昌、河南周口

### v1.6
* 支持诊断、手术操作按名称、ICD编码查找输入
* 准确提示诊断MCC、CC，主诊断显示对应的排除表
* 提示诊断、手术操作医保灰码
* 按照基金结算清单质控规则，对诊断、手术操作进行质控提醒

### v1.7
* 增加模糊分组、参考分组、推荐分组等多种智能分组方法
* 从剪贴板批量复制诊断、手术操作ICD编码
* 诊断、手术操作编码输入优化

**为了医院数据安全，分组规则要下载到本地，计算过程中不发送任何网络数据；opendrg.github.io网站带宽有限，页面加载时间可能长达30秒左右，请耐心等待**

## 联系开发团队
请发邮件至*******************
