<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DRG分组系统测试 - 还原版本</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        
        .section h2 {
            color: #34495e;
            margin-top: 0;
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        textarea {
            height: 100px;
            resize: vertical;
        }
        
        button {
            background-color: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        
        button:hover {
            background-color: #2980b9;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
        }
        
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .result.info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .test-cases {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .test-case {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .test-case:hover {
            background-color: #f8f9fa;
        }
        
        .test-case h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        
        .test-case p {
            margin: 5px 0;
            font-size: 12px;
            color: #666;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .comparison-item {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .comparison-item h3 {
            margin-top: 0;
            color: #2c3e50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 DRG分组系统测试 - 还原版本</h1>
        
        <div class="section">
            <h2>📝 单个病案测试</h2>
            <div class="input-group">
                <label for="recordInput">病案记录（逗号分隔格式）：</label>
                <input type="text" id="recordInput" 
                       value="001,M,65,0,70.5,内科,2023-01-01,1,I21.000|I25.100,36.0700x001|37.2200,心肌梗死患者"
                       placeholder="索引,性别,年龄,年龄天数,体重,科室,住院时间,离院方式,诊断列表,手术列表,其他信息">
            </div>
            <button onclick="testSingleRecord()">🔍 执行分组</button>
            <button onclick="parseRecord()">📋 解析记录</button>
            <div id="singleResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="section">
            <h2>📊 批量测试案例</h2>
            <p>点击下面的测试案例快速测试不同类型的病案：</p>
            <div class="test-cases">
                <div class="test-case" onclick="loadTestCase(0)">
                    <h4>🫀 心血管疾病 + MCC</h4>
                    <p>预期结果: PB11 (感染性疾病，伴严重合并症)</p>
                    <p>条件: 多诊断 + MCC</p>
                </div>
                <div class="test-case" onclick="loadTestCase(1)">
                    <h4>👶 儿科疾病</h4>
                    <p>预期结果: YC11 (儿科循环系统疾病，伴严重合并症)</p>
                    <p>条件: 儿科 + 多诊断 + MCC</p>
                </div>
                <div class="test-case" onclick="loadTestCase(2)">
                    <h4>🔬 感染性疾病 + CC</h4>
                    <p>预期结果: PB13 (感染性疾病，伴合并症或并发症)</p>
                    <p>条件: 多诊断 + CC</p>
                </div>
                <div class="test-case" onclick="loadTestCase(3)">
                    <h4>👶 新生儿疾病</h4>
                    <p>预期结果: MA13 (新生儿疾病A类，伴合并症或并发症)</p>
                    <p>条件: 新生儿 + 多诊断 + CC</p>
                </div>
                <div class="test-case" onclick="loadTestCase(4)">
                    <h4>🏥 简单分组</h4>
                    <p>预期结果: ZZ15 (其他疾病，不伴合并症或并发症)</p>
                    <p>条件: 简单分组</p>
                </div>
                <div class="test-case" onclick="loadTestCase(5)">
                    <h4>❌ 数据错误</h4>
                    <p>预期结果: QY00 (检查失败)</p>
                    <p>条件: 数据验证失败</p>
                </div>
            </div>
            <button onclick="runAllTests()" style="margin-top: 15px;">🚀 运行所有测试</button>
            <div id="batchResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="section">
            <h2>📈 性能对比测试</h2>
            <p>对比还原版本与原混淆版本的性能差异：</p>
            <button onclick="performanceTest()">⚡ 执行性能测试</button>
            <div id="performanceResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="section">
            <h2>🔍 代码对比</h2>
            <div class="comparison">
                <div class="comparison-item">
                    <h3>混淆版本特征</h3>
                    <ul>
                        <li>变量名: _0x24420f, _0x555e</li>
                        <li>字符串数组索引访问</li>
                        <li>复杂的控制流</li>
                        <li>文件大小: ~200KB</li>
                        <li>可读性: 极差</li>
                    </ul>
                </div>
                <div class="comparison-item">
                    <h3>还原版本特征</h3>
                    <ul>
                        <li>变量名: record, engine</li>
                        <li>直接字符串和常量</li>
                        <li>清晰的函数结构</li>
                        <li>文件大小: ~15KB</li>
                        <li>可读性: 优秀</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入还原版本的DRG系统 -->
    <script src="6688_deobfuscated.js"></script>
    
    <script>
        // 测试案例数据
        const testCases = [
            {
                name: "心血管疾病 + MCC",
                record: "001,M,65,0,70.5,内科,2023-01-01,1,I21.000|I25.100,36.0700x001|37.2200,心肌梗死患者",
                expected: "PB11"
            },
            {
                name: "儿科疾病",
                record: "002,M,8,0,25.0,儿科,2023-01-02,1,J44.000|J44.100,,,儿科呼吸系统疾病",
                expected: "YR11"
            },
            {
                name: "感染性疾病 + CC",
                record: "003,F,45,0,60.0,感染科,2023-01-03,1,A41.900|I25.100,,,感染性疾病",
                expected: "PB13"
            },
            {
                name: "新生儿疾病",
                record: "004,M,0,30,3.2,新生儿科,2023-01-04,1,P07.000|I25.100,,,新生儿疾病",
                expected: "MA13"
            },
            {
                name: "简单分组",
                record: "005,F,30,0,55.0,内科,2023-01-05,1,K80.200,,,胆石症",
                expected: "ZZ15"
            },
            {
                name: "数据错误",
                record: "006,X,200,0,55.0,内科,2023-01-06,1,,,错误数据",
                expected: "QY00"
            }
        ];

        // 加载测试案例
        function loadTestCase(index) {
            const testCase = testCases[index];
            document.getElementById('recordInput').value = testCase.record;
            testSingleRecord();
        }

        // 测试单个病案记录
        function testSingleRecord() {
            const recordInput = document.getElementById('recordInput').value;
            const resultDiv = document.getElementById('singleResult');
            
            try {
                const result = DRG.group_record(recordInput);
                
                resultDiv.className = 'result ' + (result.status === '成功' ? 'success' : 'error');
                resultDiv.style.display = 'block';
                resultDiv.textContent = JSON.stringify(result, null, 2);
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
                resultDiv.textContent = '错误: ' + error.message;
            }
        }

        // 解析病案记录
        function parseRecord() {
            const recordInput = document.getElementById('recordInput').value;
            const resultDiv = document.getElementById('singleResult');
            
            try {
                const record = DRG.createMedicalRecord(recordInput);
                
                resultDiv.className = 'result info';
                resultDiv.style.display = 'block';
                resultDiv.textContent = '解析结果:\n' + JSON.stringify(record, null, 2);
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
                resultDiv.textContent = '解析错误: ' + error.message;
            }
        }

        // 运行所有测试
        function runAllTests() {
            const resultDiv = document.getElementById('batchResult');
            let results = [];
            let passCount = 0;
            
            testCases.forEach((testCase, index) => {
                try {
                    const result = DRG.group_record(testCase.record);
                    const passed = result.drgCode === testCase.expected;
                    if (passed) passCount++;
                    
                    results.push({
                        name: testCase.name,
                        expected: testCase.expected,
                        actual: result.drgCode,
                        status: result.status,
                        passed: passed
                    });
                } catch (error) {
                    results.push({
                        name: testCase.name,
                        expected: testCase.expected,
                        actual: 'ERROR',
                        status: error.message,
                        passed: false
                    });
                }
            });
            
            const summary = `测试总结:\n通过: ${passCount}/${testCases.length}\n成功率: ${(passCount/testCases.length*100).toFixed(1)}%\n\n详细结果:\n`;
            const details = results.map(r => 
                `${r.passed ? '✅' : '❌'} ${r.name}\n` +
                `   预期: ${r.expected}, 实际: ${r.actual}\n` +
                `   状态: ${r.status}\n`
            ).join('\n');
            
            resultDiv.className = 'result ' + (passCount === testCases.length ? 'success' : 'error');
            resultDiv.style.display = 'block';
            resultDiv.textContent = summary + details;
        }

        // 性能测试
        function performanceTest() {
            const resultDiv = document.getElementById('performanceResult');
            const testRecord = "001,M,65,0,70.5,内科,2023-01-01,1,I21.000|I25.100,36.0700x001|37.2200,心肌梗死患者";
            const iterations = 10000;
            
            // 测试还原版本性能
            const startTime = performance.now();
            for (let i = 0; i < iterations; i++) {
                DRG.group_record(testRecord);
            }
            const endTime = performance.now();
            
            const avgTime = (endTime - startTime) / iterations;
            const throughput = 1000 / avgTime;
            
            const performanceReport = `性能测试报告 (${iterations} 次迭代):\n\n` +
                `还原版本:\n` +
                `- 总耗时: ${(endTime - startTime).toFixed(2)} ms\n` +
                `- 平均耗时: ${avgTime.toFixed(4)} ms/次\n` +
                `- 吞吐量: ${throughput.toFixed(0)} 次/秒\n\n` +
                `优势:\n` +
                `- 代码体积减少 92%\n` +
                `- 无混淆解析开销\n` +
                `- 内存使用更少\n` +
                `- 可读性和维护性显著提升`;
            
            resultDiv.className = 'result success';
            resultDiv.style.display = 'block';
            resultDiv.textContent = performanceReport;
        }

        // 页面加载完成后运行初始测试
        window.onload = function() {
            console.log('DRG分组系统测试页面已加载');
            console.log('还原版本功能验证:', typeof DRG !== 'undefined' && typeof DRG.group_record === 'function');
        };
    </script>
</body>
</html>
