(self.webpackChunkdrg_group = self.webpackChunkdrg_group || []).push([[4826], {
    9481: (e, t, i) => {
        var n = {
            "./SS_INFO.csv": [7621, 7621],
            "./SS_MAP.csv": [4633, 4633],
            "./YB_SS_INFO.csv": [1487, 1487],
            "./YB_ZD_INFO.csv": [7504, 7504],
            "./ZD_ADRG_RULE_WK_10.csv": [6976, 6976],
            "./ZD_ADRG_RULE_WK_10P.csv": [8289, 8289],
            "./ZD_ADRG_RULE_WK_11.csv": [9050, 9050],
            "./ZD_ADRG_RULE_WK_11P.csv": [873, 873],
            "./ZD_ADRG_RULE_WK_12.csv": [4711, 4711],
            "./ZD_ADRG_RULE_WK_20.csv": [5022, 5022],
            "./ZD_ADRG_RULE_WK_BJ.csv": [232, 232],
            "./ZD_ADRG_RULE_WK_SC.csv": [7967, 7967],
            "./ZD_ADRG_RULE_WK_ZJ.csv": [7853, 7853],
            "./ZD_INFO.csv": [7792, 7792],
            "./ZD_MAP.csv": [7439, 7439],
            "./ZD_MDC_RULE_WK_10P.csv": [3562, 3562],
            "./ZD_MDC_RULE_WK_11.csv": [8482, 8482],
            "./ZD_MDC_RULE_WK_11P.csv": [8569, 8569],
            "./ZD_MDC_RULE_WK_12.csv": [1200, 1200],
            "./ZD_MDC_RULE_WK_20.csv": [5351, 5351],
            "./ZD_MDC_RULE_WK_BJ.csv": [4416, 4416],
            "./ZD_MDC_RULE_WK_SC.csv": [122, 122],
            "./ZD_MDC_RULE_WK_ZJ.csv": [3641, 3641]
        };

        function o(e) {
            if (!i.o(n, e)) return Promise.resolve().then((() => {
                var t = new Error("Cannot find module '" + e + "'");
                throw t.code = "MODULE_NOT_FOUND", t
            }));
            var t = n[e], o = t[0];
            return i.e(t[1]).then((() => i.t(o, 23)))
        }

        o.keys = () => Object.keys(n), o.id = 9481, e.exports = o
    }, 3270: (e, t, i) => {
        var n = {
            "./ZD_ADRG_RULE_WK_10.csv": [6976, 6976],
            "./ZD_ADRG_RULE_WK_10P.csv": [8289, 8289],
            "./ZD_ADRG_RULE_WK_11.csv": [9050, 9050],
            "./ZD_ADRG_RULE_WK_11P.csv": [873, 873],
            "./ZD_ADRG_RULE_WK_12.csv": [4711, 4711],
            "./ZD_ADRG_RULE_WK_20.csv": [5022, 5022],
            "./ZD_ADRG_RULE_WK_BJ.csv": [232, 232],
            "./ZD_ADRG_RULE_WK_SC.csv": [7967, 7967],
            "./ZD_ADRG_RULE_WK_ZJ.csv": [7853, 7853]
        };

        function o(e) {
            if (!i.o(n, e)) return Promise.resolve().then((() => {
                var t = new Error("Cannot find module '" + e + "'");
                throw t.code = "MODULE_NOT_FOUND", t
            }));
            var t = n[e], o = t[0];
            return i.e(t[1]).then((() => i.t(o, 23)))
        }

        o.keys = () => Object.keys(n), o.id = 3270, e.exports = o
    }, 1884: (e, t, i) => {
        var n = {
            "./ZD_MDC_RULE_WK_10P.csv": [3562, 3562],
            "./ZD_MDC_RULE_WK_11.csv": [8482, 8482],
            "./ZD_MDC_RULE_WK_11P.csv": [8569, 8569],
            "./ZD_MDC_RULE_WK_12.csv": [1200, 1200],
            "./ZD_MDC_RULE_WK_20.csv": [5351, 5351],
            "./ZD_MDC_RULE_WK_BJ.csv": [4416, 4416],
            "./ZD_MDC_RULE_WK_SC.csv": [122, 122],
            "./ZD_MDC_RULE_WK_ZJ.csv": [3641, 3641]
        };

        function o(e) {
            if (!i.o(n, e)) return Promise.resolve().then((() => {
                var t = new Error("Cannot find module '" + e + "'");
                throw t.code = "MODULE_NOT_FOUND", t
            }));
            var t = n[e], o = t[0];
            return i.e(t[1]).then((() => i.t(o, 23)))
        }

        o.keys = () => Object.keys(n), o.id = 1884, e.exports = o
    }, 5364: (e, t, i) => {
        var n = {
            "./aba_2023.js": [4003, 4003],
            "./aletai_2023.js": [5640, 5640],
            "./ankang_2023.js": [4347, 4347],
            "./anshan_2024.js": [1516, 1516],
            "./anyang_2021.js": [3149, 3149],
            "./bazhong_2024.js": [5104, 5104],
            "./beijing_2022.js": [7369, 7369],
            "./bengbu_2023.js": [4769, 4769],
            "./changchun_2024.js": [3971, 3971],
            "./changsha_2023.js": [6937, 6937],
            "./changzhi_2022.js": [4622, 4622],
            "./changzhou_2023.js": [5636, 5636],
            "./chenzhou_2024.js": [7763, 7763],
            "./chongqing_2024.js": [6276, 6276],
            "./chongqing_2025.js": [6688, 6688],
            "./chs_drg_10.js": [7071, 7071],
            "./chs_drg_11.js": [6917, 6917],
            "./chs_drg_12.js": [6330, 6330],
            "./chs_drg_20.js": [2088, 2088],
            "./chuzhou_2024.js": [7424, 7424],
            "./dalian_2022.js": [2251, 2251],
            "./foshan_2022.js": [1584, 1584],
            "./fuzhou_2024.js": [7016, 7016],
            "./guangan_2023.js": [3475, 3475],
            "./guangan_2024.js": [4294, 4294],
            "./guangxi_2022.js": [2994, 2994],
            "./handan_2024.js": [8649, 8649],
            "./hefei_2023.js": [3145, 3145],
            "./henan_2024.js": [7323, 7323],
            "./jilinsheng_2024.js": [5545, 5545],
            "./jiyuan_2023.js": [4967, 4967],
            "./kashi_2023.js": [4267, 4267],
            "./kezhou_2023.js": [691, 691],
            "./leshan_2023.js": [8651, 8651],
            "./liaocheng_2022.js": [2067, 2067],
            "./linfen_2022.js": [9687, 9687],
            "./liuan_2023.js": [8312, 8312],
            "./luohe_2023.js": [9918, 9918],
            "./maanshan_2023.js": [7325, 7325],
            "./meishan_2023.js": [4566, 4566],
            "./meishan_2024.js": [3794, 3794],
            "./mudanjiang_2023.js": [1375, 1375],
            "./nanchang_2023.js": [6308, 6308],
            "./nanjing_2022.js": [5650, 5650],
            "./nanping_2023.js": [9444, 9444],
            "./nantong_2024.js": [1850, 1850],
            "./qinghai_2024.js": [238, 238],
            "./qingyang_2023.js": [2311, 2311],
            "./quanzhou_2023.js": [8936, 8936],
            "./shanghaiDRG_2023.js": [7468, 7468],
            "./shenyang_2024.js": [35, 35],
            "./sichuan_2022.js": [5524, 5524],
            "./sichuan_2024.js": [6830, 6830],
            "./suining_2023.js": [7669, 7669],
            "./suzhou_2024.js": [7192, 7192],
            "./taizhou_2023.js": [5190, 5190],
            "./tianjinDRG_2024.js": [730, 730],
            "./tongren_2024.js": [5683, 5683],
            "./wlmq_2022.js": [2369, 2369],
            "./wuhan_2024.js": [7590, 7590],
            "./wuxi_2023.js": [7598, 7598],
            "./xian_2024.js": [3211, 3211],
            "./xianyang_2024.js": [3406, 3406],
            "./xining_2023.js": [7245, 7245],
            "./xjbt_2022.js": [7581, 7581],
            "./xuzhou_2023.js": [7073, 7073],
            "./yaan_2024.js": [7258, 7258],
            "./yancheng_2023.js": [9095, 9095],
            "./yantai_2023.js": [409, 409],
            "./yinchuan_2023.js": [6722, 6722],
            "./yunnan_2023.js": [7570, 7570],
            "./zhejiang_2022.js": [2049, 2049],
            "./zhejiang_2024.js": [7093, 7093],
            "./zhumadian_2024.js": [9115, 9115],
            "./ziyang_2023.js": [553, 553]
        };

        function o(e) {
            if (!i.o(n, e)) return Promise.resolve().then((() => {
                var t = new Error("Cannot find module '" + e + "'");
                throw t.code = "MODULE_NOT_FOUND", t
            }));
            var t = n[e], o = t[0];
            return i.e(t[1]).then((() => i.t(o, 23)))
        }

        o.keys = () => Object.keys(n), o.id = 5364, e.exports = o
    }, 437: (e, t, i) => {
        "use strict";
        var n = i(3379), o = i.n(n), d = i(7795), a = i.n(d), r = i(569), s = i.n(r), l = i(3565), x = i.n(l),
            c = i(9216), F = i.n(c), u = i(4589), D = i.n(u), R = i(5902), h = {};
        h.styleTagTransform = D(), h.setAttributes = x(), h.insert = s().bind(null, "head"), h.domAPI = a(), h.insertStyleElement = F(), o()(R.Z, h), R.Z && R.Z.locals && R.Z.locals;
        const G = {1: "是", "": "否"}, p = {1: "男", 2: "女"},
            f = {1: "医嘱离院", 2: "医嘱转院", 3: "医嘱转社区···", 4: "非医嘱离院", 5: "死亡", 9: "其他"},
            _ = ["A18", "B00", "B02", "B08", "B18", "C73", "D13", "D16", "D17", "D18", "D24", "D25", "D27", "D36", "D50", "D69", "E05", "E07", "E10", "E11", "G40", "G45", "H02", "H1l", "H25", "H26", "H33", "H40", "H81", "H91", "I10", "I20", "I47", "I70", "I80", "I83", "I84", "I86", "I87", "J04", "J06", "J20", "J21", "J32", "J33", "J34", "J35", "J38", "J40", "J45", "K11", "K12", "K21", "K22", "K25", "K29", "K31", "K35", "K40", "K52", "K60", "K61", "K63", "K80", "L08", "M06", "M13", "M17", "M47", "M48", "M51", "M79", "M87", "N02", "N04", "N10", "N13", "N20", "N40", "N43", "N45", "N80", "N83", "O00", "O02", "O20", "O26", "O35", "O36", "O42", "O47", "O60", "O69", "O80", "O82", "O99", "P23", "P59", "P91", "Q35", "R42", "R56", "S00", "S22", "S42", "S52", "S62", "S82", "S83", "T14", "T18", "Z34", "Z47", "Z48", "Z51"];
        String.prototype.replaceAll = function (e, t) {
            return this.replace(new RegExp(e, "g"), t)
        }, String.prototype.replaceCsv = function () {
            let e, t = this, i = new RegExp('"(.*?)"', "g");
            for (; e = i.exec(this);) t = t.replace(e[0], e[1].replaceAll(",", "|"));
            return t
        };
        const v = {
            // chs_drg_20: "CHS-DRG 2.0正式发布版",
            // chs_drg_12: "CHS-DRG 1.2征求意见稿",
            // chs_drg_11: "CHS-DRG 1.1标准版",
            // chs_drg_10: "CHS-DRG 1.0修订版",
            // beijing_2022: "北京2022",
            // shanghaiDRG_2023: "上海2023",
            // tianjinDRG_2024: "天津2024",
            chongqing_2025: "重庆2025(CHS-DRG 2.0)",
            // zhejiang_2024: "浙江2024(CHS-DRG 2.0)",
            // zhejiang_2022: "浙江2022(ZJ-DRG 1.1)",
            // guangxi_2022: "广西2022",
            // yunnan_2023: "云南2023",
            // nanjing_2022: "江苏南京2022",
            // yancheng_2023: "江苏盐城2023",
            // suzhou_2024: "江苏苏州2024",
            // taizhou_2023: "江苏泰州2023",
            // wuxi_2023: "江苏无锡2023",
            // changzhou_2023: "江苏常州2023",
            // nantong_2024: "江苏南通2024",
            // xuzhou_2023: "江苏徐州2023",
            // jinan_2024: "山东济南2024",
            // linyi_2024: "山东临沂2024",
            // qingdao_2024: "山东青岛2024(CHS-DRG 2.0)",
            // liaocheng_2022: "山东聊城2022",
            // yantai_2023: "山东烟台2023",
            // fuzhou_2024: "福建福州2024",
            // nanping_2023: "福建南平2023",
            // quanzhou_2023: "福建泉州2023",
            // xian_2024: "陕西西安2024",
            // tongchuan_2023: "陕西铜川2023",
            // xianyang_2024: "陕西咸阳2024",
            // ankang_2024: "陕西安康2024",
            // sichuan_2024: "四川全省统一2024(CHS-DRG 2.0)",
            // sichuan_2022: "四川省本级2022(CHS-DRG 1.0)",
            // chengdu_2024: "四川成都2024",
            // yibin_2023: "四川宜宾2023",
            // ziyang_2023: "四川资阳2023",
            // leshan_2023: "四川乐山2023",
            // neijiang_2023: "四川内江2023",
            // dazhou_2024: "四川达州2024",
            // bazhong_2024: "四川巴中2024",
            // yaan_2024: "四川雅安2024",
            // zigong_2022: "四川自贡2022",
            // mianyang_2023: "四川绵阳2023",
            // suining_2023: "四川遂宁2023",
            // guangan_2023: "四川广安2023",
            // guangan_2024: "四川广安扩面2024",
            // meishan_2023: "四川眉山2023(CHS-DRG 1.0)",
            // meishan_2024: "四川眉山2024",
            // aba_2023: "四川阿坝2023",
            // wuhan_2024: "湖北武汉2024(CHS-DRG 1.0)",
            // changsha_2023: "湖南长株潭衡2023",
            // chenzhou_2024: "湖南郴州2024",
            // shanxi_2024: "山西全省统一2024(CHS-DRG 2.0)",
            // changzhi_2022: "山西长治2022",
            // linfen_2022: "山西临汾2022",
            // datong_2023: "山西大同2023",
            // lanzhou_2024: "甘肃兰州2024",
            // qingyang_2023: "甘肃庆阳2023",
            // yinchuan_2023: "宁夏银川2023",
            // wlmq_2022: "新疆乌鲁木齐2022",
            // xjbt_2022: "新疆兵团2022",
            // kashi_2023: "新疆喀什2023",
            // kezhou_2023: "新疆克州2023",
            // aletai_2023: "新疆阿勒泰2023",
            // yili_2023: "新疆伊犁2023",
            // changji_2024: "新疆昌吉2024",
            // haerbin_2024: "黑龙江哈尔滨2024",
            // mudanjiang_2023: "黑龙江牡丹江2023",
            // jilin_2024: "吉林全省统一2024(CHS-DRG 2.0)",
            // shenyang_2024: "辽宁沈阳2024(CHS-DRG 2.0)",
            // dalian_2022: "辽宁大连2022",
            // dandong_2024: "辽宁丹东2024",
            // anshan_2024: "辽宁鞍山2024",
            // foshan_2022: "广东佛山2022",
            // hefei_2023: "安徽合肥2023",
            // liuan_2023: "安徽六安2023",
            // maanshan_2023: "安徽马鞍山2023",
            // bengbu_2023: "安徽蚌埠2023",
            // chuzhou_2024: "安徽滁州2024",
            // chizhou_2024: "安徽池州2024",
            // handan_2024: "河北邯郸2024",
            // dingzhou_2023: "河北定州2023",
            // nanchang_2023: "江西南昌2023",
            // henan_2024: "河南全省统一2024(CHS-DRG 2.0)",
            // anyang_2021: "河南安阳2021",
            // luohe_2023: "河南漯河2023",
            // zhoukou_2023: "河南周口2023",
            // kaifeng_2024: "河南开封2024",
            // zhumadian_2024: "河南驻马店2024(CHS-DRG 1.0)",
            // jiyuan_2023: "河南济源2023",
            // tongren_2024: "贵州铜仁2024(CHS-DRG 2.0)",
            // xining_2023: "青海西宁2023(CHS-DRG 1.0)",
            // qinghai_2024: "青海全省统一2024(CHS-DRG 2.0)"
        }, g = {
            ankang_2024: "chs_drg_11",
            yili_2023: "chs_drg_11",
            tongchuan_2023: "chs_drg_11",
            xianyang_2024: "chs_drg_11",
            chizhou_2024: "chs_drg_11",
            changji_2024: "chs_drg_11",
            tongren_2024: "chs_drg_20",
            chengdu_2024: "sichuan_2024"
        }, b = {
            zhejiang_2022: ["仅分组", "杭州", "嘉兴", "金华", "宁波", "丽水", "台州", "衢州"],
            guangxi_2022: ["玉林", "防城港", "来宾", "桂林", "贵港", "南宁", "崇左", "河池", "北海", "百色"],
            yunnan_2023: ["昆明", "楚雄", "曲靖", "大理", "红河", "西双版纳", "迪庆", "普洱", "保山", "临沧"],
            shanxi_2024: ["仅分组", "太原", "晋城", "运城"],
            jilin_2024: ["仅分组", "长春", "吉林市"],
            qingyang_2023: ["庆阳"],
            haerbin_2024: ["哈尔滨"],
            nanchang_2023: ["南昌"],
            linyi_2024: ["临沂"],
            zhoukou_2023: ["周口"],
            jinan_2024: ["济南"],
            suzhou_2024: ["苏州"],
            hefei_2023: ["合肥"],
            quanzhou_2023: ["泉州"],
            kaifeng_2024: ["开封"],
            ankang_2024: ["安康"],
            leshan_2023: ["乐山"],
            liuan_2023: ["六安"],
            yinchuan_2023: ["银川"],
            neijiang_2023: ["内江"],
            xuzhou_2023: ["徐州"],
            dazhou_2024: ["达州"],
            nantong_2024: ["南通"],
            yibin_2023: ["宜宾"],
            bazhong_2024: ["巴中"],
            lanzhou_2024: ["兰州"],
            luohe_2023: ["漯河"],
            kezhou_2023: ["克州"],
            xianyang_2024: ["咸阳"],
            yancheng_2023: ["盐城"],
            dandong_2024: ["丹东"],
            yaan_2024: ["雅安"],
            xian_2024: ["西安"],
            zigong_2022: ["自贡"],
            mianyang_2023: ["绵阳"],
            nanjing_2022: ["南京"],
            anshan_2024: ["鞍山"],
            jiyuan_2023: ["济源"],
            tongchuan_2023: ["铜川"],
            wuxi_2023: ["无锡"],
            kashi_2023: ["喀什"],
            tianjinDRG_2024: ["天津"],
            datong_2023: ["大同"],
            mudanjiang_2023: ["牡丹江"],
            bengbu_2023: ["蚌埠"],
            shenyang_2024: ["沈阳"],
            handan_2024: ["邯郸"],
            wuhan_2024: ["武汉"],
            chenzhou_2024: ["郴州"],
            changsha_2023: ["长沙", "衡阳"],
            suining_2023: ["遂宁"],
            chongqing_2025: ["重庆"],
            ziyang_2023: ["资阳"],
            xining_2023: ["西宁"],
            taizhou_2023: ["泰州"],
            sichuan_2022: ["四川省本级"],
            guangan_2023: ["广安"],
            guangan_2024: ["广安扩面"],
            fuzhou_2024: ["福州"],
            zhumadian_2024: ["驻马店"],
            chuzhou_2024: ["滁州"],
            meishan_2023: ["眉山2023"],
            meishan_2024: ["眉山"],
            tongren_2024: ["铜仁"],
            dingzhou_2023: ["定州"],
            chizhou_2023: ["池州"],
            maanshan_2023: ["马鞍山"],
            aba_2023: ["阿坝"],
            aletai_2023: ["阿勒泰"],
            yili_2023: ["伊犁"],
            changji_2024: ["昌吉"],
            chengdu_2024: ["成都"],
            qingdao_2024: ["青岛"]
        }, y = {
            玉林: 7990.2388,
            防城港: 7990.2388,
            来宾: 7990.2388,
            桂林: 7990.2388,
            贵港: 7990.2388,
            南宁: 7990.2388,
            崇左: 7990.2388,
            河池: 7990.2388,
            北海: 7990.2388,
            百色: 7990.2388,
            嘉兴: 9059.308,
            金华: 9427,
            宁波: 10234.16,
            丽水: 9238,
            台州: 9040.7059,
            杭州: 13892.24,
            衢州: 8e3,
            南昌: 12839.66,
            周口: 7231.74,
            合肥: 9322.55,
            开封: 6862.75,
            宜宾: 4724.7,
            巴中: 4340.28,
            安康: 6195.334,
            乐山: 5292.16,
            内江: 5781.89,
            徐州: 11700,
            达州: 4472.789,
            雅安: 5384,
            南通: 11542.11,
            漯河: 8147,
            克州: 5582.01,
            盐城: 7726.16,
            丹东: 7150,
            四川省本级: 11766.64,
            自贡: 6505.832,
            绵阳: 5957.1,
            南京: 14e3,
            济源: 6990.98,
            天津: 16137.65,
            牡丹江: 10924.48809,
            蚌埠: 7042.2566,
            遂宁: 5813.1568,
            泰州: 8681.12834,
            广安: 6798.8672,
            广安扩面: 3085.3077,
            滁州: 6172.5656,
            眉山2023: 3e3,
            阿坝: 4505.59,
            成都: 15e3
        }, w = {
            默认: ["一级", "二级", "三级"],
            周口: ["乡级", "一级", "县二级", "市二级", "三级", "三甲"],
            济南: ["D类", "C类", "B类", "A类"],
            铜川: ["二级", "三级"],
            玉林: ["一级", "二级", "三级"],
            防城港: ["乡镇", "乡镇中心", "三级"],
            来宾: ["一级", "二级", "三级"],
            桂林: ["二级", "三级"],
            贵港: ["一级", "二级", "三级"],
            南宁: ["一级", "二级", "三级"],
            崇左: ["一级", "二级", "三级", "三甲"],
            河池: ["二级", "三级中医", "三级"],
            北海: ["二级", "三级", "三甲中医", "三甲"],
            百色: ["一级", "二级", "三级"],
            庆阳: ["一级", "二级", "三级"],
            临沂: ["一级", "二级", "三级"],
            苏州: ["一级", "二级", "三级"],
            泉州: ["C类", "B类", "A类"],
            徐州: ["二级", "三级"],
            安康: ["二级"],
            兰州: ["一级", "二级", "三级", "三甲", "省三甲"],
            甘肃省直: ["二级", "三甲", "省三乙", "省三甲"],
            克州: ["一级", "二级", "三级"],
            丹东: ["一级", "二级", "三级"],
            鞍山: ["一级", "二级", "三级", "三甲"],
            开封: ["一级", "二级", "县三级", "市三级", "三甲"],
            长春: ["Ⅳ级", "Ⅲ级", "Ⅱ级", "Ⅰ级"],
            六安: ["二级", "三级"],
            喀什: ["一级", "二级", "三级"],
            银川: ["一级", "二级", "三乙", "三甲"],
            内江: ["一级", "二乙", "二甲", "三乙", "三甲"],
            运城: ["一级达到推荐标准", "一级未达到推荐标准", "县二级90%未定级", "县二级90%有级别", "县二级", "市二级", "三级"],
            大同: ["一级", "县二级", "市二级", "三级"],
            太原: ["一级", "县二级", "省市二级", "市三级", "省三级"],
            达州: ["一级", "二乙", "二甲", "三乙", "三甲"],
            哈尔滨: ["Ⅲ级", "Ⅱ级", "Ⅰ级"],
            牡丹江: ["二级"],
            蚌埠: ["二级"],
            邯郸: ["一级", "二级"],
            漯河: ["二级"],
            咸阳: ["一级", "二级", "三级"],
            西安: ["一级", "二级", "三级"],
            盐城: ["一级", "二级中医", "二级", "三级"],
            雅安: ["卫生院", "一级", "二乙", "二甲", "三乙", "三甲"],
            自贡: ["一级", "二乙", "二甲", "三乙", "三甲"],
            绵阳: ["一级", "二乙", "二甲", "三乙", "三甲"],
            南京: ["三甲", "三级", "二甲", "二级", "一级"],
            曲靖: ["一级", "二级", "三级"],
            楚雄: ["六类", "五类", "四类", "三类", "二类", "一类"],
            昆明: ["一档", "二档", "三档", "四档"],
            红河: ["F级", "E级", "D级", "C级", "B级", "A级"],
            西双版纳: ["Ⅳ类", "Ⅲ类", "Ⅱ类", "Ⅰ类"],
            迪庆: ["一级", "二级", "三级"],
            大理: ["一类", "二类", "三类", "四类", "五类"],
            普洱: ["D档", "C档", "B档", "A档"],
            临沧: ["D档", "C档", "B档", "A档"],
            保山: ["一档", "二档", "三档", "四档", "五档"],
            无锡: ["一档", "二档", "三档"],
            济源: ["一级", "二级", "三级"],
            巴中: ["一级", "二级", "三级"],
            沈阳: ["Ⅲ级", "Ⅱ级", "Ⅰ级"],
            武汉: ["一级", "二级", "三级"],
            郴州: ["一级", "二级"],
            长沙: ["基层", "三类", "二类", "一类"],
            衡阳: ["G类", "F类", "E类", "D类", "C类", "B类", "A类"],
            遂宁: ["一级"],
            重庆: ["一级", "二级", "三级"],
            西宁: ["二级", "三级"],
            泰州: ["一级", "二级", "二乙", "二甲", "三级", "市三甲"],
            广安: ["二级"],
            广安扩面: ["二级"],
            福州: ["二级", "三乙", "三级"],
            驻马店: ["一级", "二级", "三级"],
            眉山2023: ["一级", "二级"],
            眉山: ["一级", "二级", "三级"],
            铜仁: ["Ⅴ类", "Ⅳ类", "Ⅲ类", "Ⅱ类", "Ⅰ类", "Ⅵ类"],
            定州: ["四档", "三档", "二档", "一档"],
            池州: ["一级", "二级", "三级"],
            晋城: ["一档", "二档", "三档"],
            马鞍山: ["二级"],
            阿坝: ["一级"],
            阿勒泰: ["一级", "二级", "三级"],
            伊犁: ["二级", "三级"],
            昌吉: ["一级", "二级", "三级"],
            吉林市: ["三级", "外县二级", "二级", "一级"],
            成都: ["二级"],
            青岛: ["二级", "三级", "三甲"]
        }, E = {
            南昌: ["赣医"],
            嘉兴: ["A医院"],
            金华: ["横店"],
            宁波: ["宁大一院", "北仑大港"],
            丽水: ["A医院"],
            台州: ["A医院"],
            杭州: ["德嘉"],
            衢州: ["侨爱"],
            合肥: ["C医院", "B医院", "A医院"],
            乐山: ["五通桥中医"],
            宜宾: ["宜宾中医院", "宜宾二院", "长宁", "卫生院", "卫兴医院"],
            四川省本级: ["八里", "省三院", "新都人民", "大邑人民", "温江人民", "龙泉驿中医", "锦欣中医"],
            资阳: ["高新口腔"],
            滁州: ["来安家宁"],
            南通: ["启东四院", "启东五院", "启东七院", "一级"]
        }, m = {
            玉林: {一级: .7, 二级: .9, 三级: 1.04},
            防城港: {乡镇: .5531, 乡镇中心: .58, 三级: 1.11},
            来宾: {一级: .68, 二级: .93, 三级: 1.13},
            桂林: {二级: .88, 三级: 1.07},
            贵港: {一级: .59, 二级: .9, 三级: 1.07},
            南宁: {三级: 1.2453, 二级: 1, 一级: .7314},
            崇左: {一级: .73, 二级: .97, 三级: 1.12, 三甲: 1.25},
            河池: {二级: .93, 三级中医: 1.08, 三级: 1.15},
            北海: {二级: .86, 三级: 1.04, 三甲中医: 1.06, 三甲: 1.08},
            百色: {一级: .61, 二级: .94, 三级: 1.34},
            庆阳: {一级: .7826, 二级: 1.126, 三级: 1.4},
            临沂: {一级: .7846, 二级: 1.1168, 三级: 1.2783},
            苏州: {一级: .75, 二级: 1.02, 三级: 1.2},
            泉州: {C类: .75, B类: .93, A类: 1.11},
            徐州: {二级: .8744, 三级: 1.0939},
            兰州: {一级: .3366, 二级: .6971, 三级: .8283, 三甲: .9818, 省三甲: 1.1228},
            甘肃省直: {一级: .3317, 二级: .660208, 三甲: .9554, 省三乙: .9554, 省三甲: 1.0228},
            克州: {一级: .7, 二级: .9, 三级: 1},
            丹东: {三级: 1, 二级: .85, 一级: .7},
            南京: {三甲: 1.05, 三级: 1.03, 二甲: 1.01, 二级: 1, 一级: 1},
            鞍山: {一级: .7, 二级: .8, 三级: 1, 三甲: 1.05},
            喀什: {一级: 1, 二级: 1, 三级: 1},
            牡丹江: {二级: .81100539},
            长沙: {基层: .376, 三类: .5002, 二类: .6682, 一类: .9886},
            衡阳: {A类: 1.1227, B类: 1, C类: .8213, D类: .8004, E类: .6181, F类: .3416, G类: .3038},
            重庆: {一级: .73, 二级: .91, 三级: 1.13},
            泰州: {一级: .5, 二级: .8, 二乙: 1, 二甲: 1, 三级: 1, 市三甲: 1},
            广安: {二级: .9},
            广安扩面: {二级: 1.0847},
            福州: {二级: .72, 三乙: .85, 三级: 1},
            驻马店: {一级: .5823, 二级: .9913, 三级: 1.28},
            眉山2023: {一级: 1, 二级: 1},
            定州: {四档: .51, 三档: .6, 二档: .96, 一档: 1.01},
            马鞍山: {二级: 1},
            阿坝: {一级: .8},
            阿勒泰: {一级: .19, 二级: .8459, 三级: 1},
            成都: {二级: 1},
            青岛: {二级: .8, 三级: 1, 三甲: 1.1}
        }, j = {
            玉林: {居民: 58.3818, 职工: 60.7447},
            防城港: {居民: 53.5025, 职工: 50.4901},
            来宾: {居民: 60, 职工: 60},
            桂林: {居民: 56, 职工: 74},
            贵港: {居民: 54.8444, 职工: 55.5259},
            南宁: {居民: 58.0687, 职工: 77.8191},
            崇左: {居民: 64.15, 职工: 82.14},
            河池: {居民: 54.6255, 职工: 41.5653},
            北海: {居民: 69.9141, 职工: 76.288},
            百色: {居民: 54.6777, 职工: 57.6847},
            嘉兴: {居民: 99.2987, 职工: 98.414},
            金华: 84.71,
            宁波: 93,
            丽水: 82.3,
            台州: 90,
            杭州: 123,
            衢州: {居民: 70.02, 职工: 76.27},
            南昌: {居民: 13.22, 职工: 13.28},
            合肥: {居民: 72, 职工: 85},
            周口: {居民: 57.44, 职工: 61.54},
            开封: {居民: 60.64, 职工: 64.49},
            安康: {居民: 52, 职工: 52},
            宜宾: {居民: 38.5352, 职工: 39.8518},
            乐山: {居民: 47.169, 职工: 47.9428},
            内江: {居民: 48, 职工: 60},
            雅安: {居民: 53.84, 职工: 53.84},
            遂宁: {居民: 45.43, 职工: 49.99},
            徐州: {居民: 100, 职工: 100},
            达州: {居民: 35.07, 职工: 43.38},
            南通: {居民: 100, 职工: 100},
            巴中: {居民: 43.4028, 职工: 43.4028},
            漯河: {居民: 51, 职工: 51},
            盐城: {居民: 58.8498, 职工: 59.2876},
            四川省本级: {职工: 115.2, 居民: 115.2},
            自贡: {居民: 62.1, 职工: 65.46},
            绵阳: {居民: 44.485793, 职工: 47.538659},
            南京: {居民: 118, 职工: 128},
            济源: {居民: 52, 职工: 60},
            天津: 1,
            牡丹江: {居民: 11029.81, 职工: 12019.16},
            蚌埠: {居民: 62.5815, 职工: 61.2525},
            资阳: {居民: 39.3935, 职工: 42.7286},
            泰州: {居民: 36, 职工: 68},
            广安: {居民: 62, 职工: 64},
            广安扩面: {居民: 30.5569, 职工: 32.1156},
            滁州: {居民: 45.57, 职工: 52.95},
            眉山2023: 32.91,
            眉山: 35.73,
            阿坝: 45.0559,
            成都: {居民: 150, 职工: 150}
        }, O = {
            曲靖: {一级: 5100, 二级: 7500, 三级: 10630},
            楚雄: {一类: 9800, 二类: 9e3, 三类: 7300, 四类: 7100, 五类: 4400, 六类: 4300},
            西双版纳: {Ⅳ类: 4700, Ⅲ类: 7680, Ⅱ类: 8180, Ⅰ类: 10062},
            昆明: {
                一档: {居民: 11690, 职工: 12430},
                二档: {居民: 8910, 职工: 10870},
                三档: {居民: 7760, 职工: 8410},
                四档: {居民: 5470, 职工: 7120}
            },
            庆阳: {职工: 3399.6038, 居民: 3275.7669},
            红河: {
                F级: {居民: 4350, 职工: 4895},
                E级: {居民: 5775, 职工: 6052},
                D级: {居民: 6525, 职工: 7565},
                C级: {居民: 7500, 职工: 8722},
                B级: {居民: 8250, 职工: 9256},
                A级: {居民: 9075, 职工: 10235}
            },
            迪庆: {一级: 7700, 二级: 8800, 三级: 1e4},
            大理: {
                一类: {居民: 10300, 职工: 11900},
                二类: {居民: 8600, 职工: 9800},
                三类: {居民: 7800, 职工: 8400},
                四类: {居民: 7400, 职工: 8e3},
                五类: {居民: 5500, 职工: 6100}
            },
            普洱: {
                D档: {居民: 5100, 职工: 5800},
                C档: {居民: 6500, 职工: 6600},
                B档: {居民: 7200, 职工: 8200},
                A档: {居民: 8700, 职工: 9700}
            },
            保山: {一档: 11050, 二档: 8620, 三档: 8215, 四档: 7290, 五档: 5860},
            临沧: {
                D档: {居民: 6e3, 职工: 6300},
                C档: {居民: 7400, 职工: 7700},
                B档: {居民: 7800, 职工: 8300},
                A档: {居民: 8500, 职工: 9e3}
            },
            哈尔滨: {
                Ⅲ级: {居民: 6858.98, 职工: 8709.6},
                Ⅱ级: {居民: 8782.04, 职工: 11151.51},
                Ⅰ级: {居民: 9649.24, 职工: 12252.7}
            },
            临沂: {居民: 4214.38, 职工: 6867.22},
            邯郸: {一级: 6368, 二级: 7638},
            济南: {
                A类: {居民: 9653.26, 职工: 12944.22},
                B类: {居民: 9033.45, 职工: 11746.74},
                C类: {居民: 7561.91, 职工: 10279.45},
                D类: {居民: 4745.14, 职工: 8218.11}
            },
            苏州: {居民: 7384.39, 职工: 8507.82},
            泉州: 8e3,
            长春: {Ⅰ级: 14e3, Ⅱ级: 12e3, Ⅲ级: 1e4, Ⅳ级: 8e3},
            吉林市: {
                一级: {居民: 3887.05, 职工: 4404.68},
                二级: {居民: 4997.68, 职工: 5557.14},
                外县二级: {居民: 6536.44, 职工: 7431.74},
                三级: {居民: 7311.98, 职工: 8731.86}
            },
            六安: {二级: {居民: 5526.34, 职工: 6239.05}, 三级: {居民: 6931.47, 职工: 7580.79}},
            银川: {职工: 2.82, 居民: 3.29},
            运城: {
                一级未达到推荐标准: 2483.36,
                一级达到推荐标准: 2675.36,
                "县二级90%未定级": 3523.66,
                "县二级90%有级别": 4091.14,
                县二级: 4996.17,
                市二级: 5617.94,
                三级: 5790.47
            },
            大同: {一级: 6550.99, 县二级: 7486.85, 市二级: 8422.7, 三级: 9358.56},
            太原: {一级: 7586.86, 县二级: 9361.19, 县市二级: 11401.01, 市三级: 13140.31, 省三级: 14078.12},
            兰州: {居民: 8536.1203, 职工: 9400.9168},
            甘肃省直: {职工: 11325.55},
            克州: {居民: 5621.4517, 职工: 6243.8718},
            铜川: {二级: {居民: 5904.13, 职工: 6581.59}, 三级: {居民: 7452.2, 职工: 7776.87}},
            咸阳: {居民: 8206.14, 职工: 8206.14},
            丹东: {居民: 6917.37, 职工: 7339.85},
            西安: {一级: 4770.71, 二级: 7788.25, 三级: 11762.43},
            鞍山: {居民: 5610, 职工: 6110},
            无锡: {一档: 14936, 二档: 10959, 三档: 6911},
            喀什: {居民: 8448.36, 职工: 8846.35},
            沈阳: {Ⅲ级: {居民: 8e3, 职工: 8500}, Ⅱ级: {居民: 10800, 职工: 12200}, Ⅰ级: {居民: 11500, 职工: 12500}},
            武汉: {一级: {居民: 6162, 职工: 7818}, 二级: {居民: 8648, 职工: 9295}, 三级: {居民: 10741, 职工: 10420}},
            郴州: {一级: 2800, 二级: 4700},
            长沙: {居民: 10672, 职工: 11452},
            衡阳: {居民: 7744.23, 职工: 8466.66},
            重庆: 9575.25,
            西宁: {二级: 6337.79, 三级: 11060.37},
            福州: 8400,
            驻马店: {居民: 5275.55, 职工: 7557.56},
            定州: 5245.39,
            铜仁: {
                Ⅴ类: {居民: 3040, 职工: 4240},
                Ⅳ类: {居民: 3450, 职工: 3680},
                Ⅲ类: {居民: 4230, 职工: 4640},
                Ⅱ类: {居民: 4300, 职工: 4700},
                Ⅰ类: {居民: 5200, 职工: 6300},
                Ⅵ类: {居民: 6570, 职工: 7750}
            },
            池州: {
                一级: {居民: 3484.06, 职工: 4415.38},
                二级: {居民: 5078.82, 职工: 6156.64},
                三级: {居民: 6226.42, 职工: 6843.44}
            },
            晋城: {居民: 5705.14, 职工: 7481.66},
            马鞍山: {居民: 5126, 职工: 5586},
            阿勒泰: {居民: 3790.57, 职工: 4177.47},
            伊犁: {居民: 6136.19, 职工: 6520.73},
            昌吉: {居民: 5203.03, 职工: 6970.83},
            青岛: {居民: 12e3, 职工: 14e3}
        };

        function L(e) {
            let t;
            if ("T93.102" == e && (t = "选择陈旧性股骨颈骨折作为主诊断，将分入MDCX组，低分值；诊断：S72.000股骨预骨折，进入IC2组，分值较高"), t) return t
        }

        function C(e) {
            if (_.includes(e.substring(0, 3))) return "主诊断属于115个低风险病种"
        }

        function z(e) {
            let t;
            if ("/" == e[5] && e.substring(0, 5) >= "M0000" && e.substring(0, 5) <= "M9989" ? t = "肿瘤形态学编码M0000/0-M9989/1" : e.substring(0, 3) >= "V01" && e.substring(0, 3) <= "Y98" ? t = "疾病外因编码V01-Y98不能作为主诊断" : e.substring(0, 3) >= "B95" && e.substring(0, 3) <= "B98" ? t = "细菌、病毒和其他传染性病原体B95-B98" : "R65" == e.substring(0, 3) ? t = "全身炎症反应综合征R65" : e.substring(0, 3) >= "U82" && e.substring(0, 3) <= "U85" ? t = "U82-U85耐药菌" : "O26.9" == e.substring(0, 5) ? t = "孕周O26.9" : e.substring(0, 3) >= "Z80" && e.substring(0, 3) <= "Z99" ? t = "家族史、个人史Z80-Z99" : e.substring(0, 3) >= "P00" && e.substring(0, 3) <= "P04" && "P00.8" == !e.substring(0, 5) ? t = "胎儿和新生儿受母体因素及妊娠产程和分娩并发症的影响P00-P04（P00.8-新生儿红斑狼疮除外）" : e.substring(0, 3) >= "T31" && e.substring(0, 5) <= "T32" ? t = "根据体表累及范围分类的烧伤/腐蚀伤T31-T32" : "I25.2" == e.substring(0, 5) ? t = "陈旧性心肌梗死I25.2" : ["I50.900x007", "I50.900x008", "I50.900x009", "I50.900x010", "I51.900x002"].includes(e) ? t = "心功能NYHA分级（I51.9、I50.9）" : ["I50.900x014", "I50.900x015", "I50.900x016", "I51.900x003"].includes(e) ? t = "急性心肌梗死的Killip分级（I51.9、I50.9）" : "Z33" == e.substring(0, 3) ? t = "附带妊娠状态Z33" : "Z37" == e.substring(0, 3) ? t = "分娩结局Z37" : "Z53" == e.substring(0, 3) && (t = "为特殊操作而与保健机构接触的人，而操作未进行Z53"), t) return t + "不能作为主诊断"
        }

        function k(e) {
            let t = e.filter((e => ["K62.3", "K65.0", "N32.8", "N36.3", "B26.0", "D07.4", "D07.5", "D07.6", "D17.6", "E89.5", "F52.4", "I86.1", "L29.1", "S31.2", "S31.3", "Z12.5"].includes(e.substring(0, 5)) || ["C60", "C61", "C62", "C63", "D29", "D40", "E29", "Q53", "Q54", "Q55", "R86"].includes(e.substring(0, 3)) || e.substring(0, 3) >= "N40" && e.substring(0, 3) <= "N51"));
            if (t.length > 0) return t.join(" ") + " 不能作为女性诊断"
        }

        function T(e) {
            let t = e.filter((e => ["N81.0", "N81.1", "N81.6", "D28.2", "D28.3", "D28.4", "D28.5", "D28.6", "D28.7", "D28.8", "D28.9", "E89.4", "S31.4", "Z12.4"].includes(e.substring(0, 5)) || ["C52", "C53", "C54", "C55", "C56", "C57", "C58", "D39", "E28", "F53", "N70", "N71", "N72", "N73", "N74", "Q50", "Q51", "Q52", "R87"].includes(e.substring(0, 3))));
            if (t.length > 0) return t.join(" ") + " 不能作为男性诊断"
        }

        function S(e) {
            let t;
            if ("/" == e[5] && e.substring(0, 5) >= "M0000" && e.substring(0, 5) <= "M9989" ? t = "肿瘤形态学编码M0000/0-M9989/1" : e.substring(0, 3) >= "V01" && e.substring(0, 3) <= "Y98" && (t = "疾病外因编码V01-Y98"), t) return t + "不能作为其他诊断"
        }

        function A(e) {
            let t;
            if ("00.3" == e.substring(0, 4) ? t = "计算机辅助外科手术[CAS]00.3" : "00.4" == e.substring(0, 4) ? t = "附属血管系统操作00.4" : e.substring(0, 5) >= "00.74" && e.substring(0, 5) <= "00.78" ? t = "髋关节釉面类型00.74-00.78" : "00.9" == e.substring(0, 4) ? t = "其他操作与介入00.9" : "17.4" == e.substring(0, 4) ? t = "机器辅助手术17.4" : "17.8" == e.substring(0, 4) ? t = "其他附属性操作17.8" : "39.61" == e.substring(0, 5) ? t = "体外循环辅助开放性心脏手术39.61" : "39.62" == e.substring(0, 5) ? t = "低温(全身性)辅助开放性心脏手术39.62" : "39.63" == e.substring(0, 5) ? t = "心麻痹39.63" : "39.64" == e.substring(0, 5) ? t = "手术中心脏起搏器39.64" : "70.94" == e.substring(0, 5) ? t = "生物移植物的置入术70.94" : "70.95" == e.substring(0, 5) ? t = "人造移植物或假体的置入术70.95" : "84.51" == e.substring(0, 5) ? t = "椎体脊柱融合装置的置入84.51" : "84.52" == e.substring(0, 5) ? t = "重组骨形态形成蛋白的置入84.52" : "84.7" == e.substring(0, 4) ? t = "外部固定装置的附加编码84.7" : "81.62" == e.substring(0, 5) ? t = "2-3个椎骨融合或再融合81.62- " : "81.63" == e.substring(0, 5) ? t = "4-8个椎骨融合或再融合81.63- " : "81.64" == e.substring(0, 5) && (t = "9个或更多椎骨融合或再融合81.64"), t) return t + "不能作为主要手术编码"
        }

        function M(e) {
            if (e.some((e => e.substring(0, 3) >= "O80" && e.substring(0, 3) <= "O84")) && !e.some((e => e.substring(0, 3) >= "O00" && e.substring(0, 3) <= "O08")) && !e.some((e => "Z37" == e.substring(0, 3)))) return "当主要诊断或者其他诊断编码出现O80-O84编码，同时无O00-O08编码时，其他诊断编码必须有分娩结局编码Z37"
        }

        function P(e) {
            let t = e.filter((e => ["V", "W", "X", "Y"].includes(e[0])));
            if (t.length > 0) return t.join(" ") + " 不包括字母V、W、X、Y开头的编码"
        }

        function q(e) {
            let t = e.filter((e => e.substring(0, 3) >= "P10" && e.substring(0, 3) <= "P15"));
            if (t.length > 0) return t.join(" ") + " 非婴儿不能出现P10-P15的编码"
        }

        function Z(e, t, i) {
            let n = i(t);
            n && (e.qcMessages.push(t + " " + n), e.qcZdList.splice(e.qcZdList.indexOf(t), 1))
        }

        function N(e, t) {
            let i = t(e.zdList);
            i && e.qcMessages.push(i)
        }

        function H(e) {
            return e.qcMessages = [], e.qcZdList = e.zdList.concat(), e.qcSsList = e.ssList.concat(), e.zdList.length > 0 && (Z(e, e.zdList[0], L), Z(e, e.zdList[0], z), "5" == e.leavingType && Z(e, e.zdList[0], C), e.zdList[0] && e.zdList.length > 1 && e.zdList.slice(1).forEach((t => Z(e, t, S))), N(e, P), N(e, M), e.age > 0 && N(e, q), "1" == e.gender && N(e, T), "2" == e.gender && N(e, k)), e.ssList.length > 0 && e.ssList[0] && e.ssList.length > 0 && function (e, t, i) {
                let n = i(t);
                n && (e.qcMessages.push(t + " " + n), e.qcSsList.splice(e.qcSsList.indexOf(t), 1))
            }(e, e.ssList[0], A), e
        }

        function I(e, t, i, n, o, d) {
            let a, r = parseFloat(t["权重"]), s = parseFloat(t["系数-" + n]), l = parseFloat(t["平均住院费用"]),
                x = d / l, c = $(r, x);
            "低倍率" == c ? a = d / meanAmount * i : ("高倍率" == c || "正常倍率" == c) && (a = r * i * s);
            let F = a * o;
            return e ? {
                DRG名称: t["DRG名称"],
                权重: r.toFixed(4),
                系数: s,
                支付标准: l.toFixed(2),
                倍率: x.toFixed(2),
                类型: c,
                总点数: a.toFixed(4),
                点值: o,
                DRG费用: F.toFixed(2),
                DRG差额: (F - d).toFixed(2),
                低倍临界值: (.6 * l).toFixed(2),
                高倍临界值: (l * X(r)).toFixed(2)
            } : {
                DRG名称: t["DRG名称"],
                权重: r.toFixed(4),
                系数: s,
                支付标准: l.toFixed(2),
                倍率: x.toFixed(2),
                类型: c,
                总点数: a.toFixed(4),
                点值: o,
                DRG费用: F.toFixed(2),
                DRG差额: (F - d).toFixed(2)
            }
        }

        const U = e => e < .3 ? "低倍率" : e >= 3 ? "高倍率" : "正常倍率",
            W = e => e < .5 ? "低倍率" : e >= 2 ? "高倍率" : "正常倍率",
            K = e => e <= 1 ? 3 : e > 1 && e <= 2 ? 2.5 : 2,
            B = e => e < .4 ? "低倍率" : e >= 2 ? "高倍率" : "正常倍率",
            J = e => e <= 1 ? 3 : e > 1 && e <= 2.5 ? 2 : 1.5, Y = e => e <= 1 ? 3 : e > 1 && e <= 2 ? 2 : 1.5,
            Q = e => e <= 1 ? 3 : e > 1 && e <= 3 ? 2 : 1.5,
            V = (e, t) => t < .4 ? "低倍率" : e <= 1 && t > 3 || e > 1 && e <= 3 && t > 2 || e > 3 && t > 1.5 ? "高倍率" : "正常倍率",
            X = e => e <= 1 ? 3 : e > 1 && e <= 2 ? 2.5 : e > 2 && e <= 4 ? 2 : 1.5,
            $ = (e, t) => t < .6 ? "低倍率" : e <= 1 && t > 3 || e > 1 && e <= 2 && t > 2.5 || e > 2 && e <= 4 && t > 2 || e > 4 && t > 1.5 ? "高倍率" : "正常倍率",
            ee = (e, t) => t < .4 ? "低倍率" : e <= 1 && t > 3 || e > 1 && e <= 2 && t > 2.5 || e > 2 && t > 2 ? "高倍率" : "正常倍率",
            te = e => "三级" == e ? 3 : 2,
            ie = (e, t) => t < .4 ? "低倍率" : "三级" == e && t > 3 || "三级" != e && t > 2 ? "高倍率" : "正常倍率",
            ne = (e, t) => t < .3 ? "低倍率" : "三级" == e && t > 3 || "三级" != e && t > 2 ? "高倍率" : "正常倍率",
            oe = (e, t) => t < .3 ? "低倍率" : e <= 1 && t > 3 || e > 1 && e <= 2 && t > 2.5 || e > 2 && t > 2 ? "高倍率" : "正常倍率",
            de = (e, t) => t < .3 ? "低倍率" : e <= 1 && t > 2.5 || e > 1 && e <= 2 && t > 2 || e > 2 && e <= 5 && t > 1.5 || e > 5 && t > 1.3 ? "高倍率" : "正常倍率",
            ae = e => e <= 1 ? 2.5 : e > 1 && e <= 2 ? 2 : e > 2 && e <= 5 ? 1.5 : 1.3, re = e => e <= 2 ? 2 : 1.5,
            se = (e, t) => "一级" == e ? t > 3 ? 1.7 : 1.8 : "二级" == e ? t > 4 ? 1.9 : 2 : "三级" == e ? t > 5 ? 2.3 : 3 : 0,
            le = e => "三级" == e ? 2.5 : 2,
            xe = e => e <= 1 ? 3 : e > 1 && e <= 2 ? 2.5 : e > 2 && e <= 3 ? 2 : e > 3 && e <= 5 ? 1.5 : 1.3,
            ce = e => e <= 1 ? 3 : e > 1 && e <= 2 ? 2.5 : e > 2 && e <= 3 ? 2 : e > 3 && e <= 5 ? 1.5 : 1.2,
            Fe = (e, t) => t < .3 ? "低倍率" : e <= 1 && t > 3 || e > 1 && e <= 2 && t > 2.5 || e > 2 && e <= 3 && t > 2 || e > 3 && e <= 5 && t > 1.5 || e > 5 && t > 1.3 ? "高倍率" : "正常倍率",
            ue = e => e <= 4 ? 3 : e > 4 && e <= 7 ? 2.5 : e > 7 && e <= 10 ? 2 : 1.5,
            De = e => e <= 1 ? 3 : e > 1 && e <= 2 ? 2.5 : e > 2 && e <= 3 ? 2 : 1.5,
            Re = e => e <= 1.5 ? 2.5 : e > 1.5 && e <= 2.5 ? 2 : 1.5, he = e => e < 1 ? 3 : 2,
            Ge = e => e < .35 ? "低倍率" : e >= 2 ? "高倍率" : "正常倍率";

        function pe(e, t, i, n, o, d, a, r, s, l, x, c, F, u, D, R, h, G) {
            return n ? t ? "青岛" == t ? function (e, t, i, n, o) {
                let d, a = parseFloat(t["权重"]), r = a * n * i, s = o / r, l = Ge(s);
                return "低倍率" == l ? d = o : "高倍率" == l ? d = o - r : "正常倍率" == l && (d = r), e ? {
                    DRG名称: t["DRG名称"],
                    权重: a,
                    费率: n,
                    支付标准: r.toFixed(2),
                    倍率: s.toFixed(2),
                    类型: l,
                    DRG费用: d.toFixed(2),
                    DRG差额: (d - o).toFixed(2),
                    低倍临界值: (.35 * r).toFixed(2),
                    高倍临界值: (2 * r).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: a,
                    费率: n,
                    支付标准: r.toFixed(2),
                    倍率: s.toFixed(2),
                    类型: l,
                    DRG费用: d.toFixed(2),
                    DRG差额: (d - o).toFixed(2)
                }
            }(e, n, l, c, F) : "成都" == t ? function (e, t, i, n, o, d, a) {
                let r, s = parseFloat(t["参考权重"]), l = o, x = n * s * l, c = a / x, F = U(c);
                "低倍率" == F ? r = a / n * 100 : "高倍率" == F ? r = 100 * (a / n - (Q(s) - l) * s) : "正常倍率" == F && (r = 100 * s * l);
                let u = r * d;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: s.toFixed(4),
                    系数: l,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: r.toFixed(4),
                    点值: d,
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - a).toFixed(2),
                    低倍临界值: (.3 * x).toFixed(2),
                    高倍临界值: (3 * x).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: s.toFixed(4),
                    系数: l,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: r.toFixed(4),
                    点值: d,
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - a).toFixed(2)
                }
            }(e, n, 0, d, l, x, F) : "吉林市" == t ? function (e, t, i, n) {
                let o, d = parseFloat(t["权重"]), a = d * i, r = n / a, s = Ge(r);
                return "低倍率" == s ? o = n : ("高倍率" == s || "正常倍率" == s) && (o = a), e ? {
                    DRG名称: t["DRG名称"],
                    权重: d,
                    费率: i,
                    支付标准: a.toFixed(2),
                    倍率: r.toFixed(2),
                    类型: s,
                    DRG费用: o.toFixed(2),
                    DRG差额: (o - n).toFixed(2),
                    低倍临界值: (.35 * a).toFixed(2),
                    高倍临界值: (2 * a).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: d,
                    费率: i,
                    支付标准: a.toFixed(2),
                    倍率: r.toFixed(2),
                    类型: s,
                    DRG费用: o.toFixed(2),
                    DRG差额: (o - n).toFixed(2)
                }
            }(e, n, c, F) : "昌吉" == t ? function (e, t, i, n, o) {
                let d, a = parseFloat(t["权重"]), r = parseFloat(t["系数-" + i]), s = a * n, l = o / s, x = U(l);
                return "低倍率" == x ? d = o : ("高倍率" == x || "正常倍率" == x) && (d = s), e ? {
                    DRG名称: t["DRG名称"],
                    权重: a.toFixed(4),
                    病组标识: t["病组标识"],
                    系数: r.toFixed(2),
                    费率: n,
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: d.toFixed(2),
                    DRG差额: (d - o).toFixed(2),
                    低倍临界值: (.3 * s).toFixed(2),
                    高倍临界值: (3 * s).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: a.toFixed(4),
                    病组标识: t["病组标识"],
                    系数: r.toFixed(2),
                    费率: n,
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: d.toFixed(2),
                    DRG差额: (d - o).toFixed(2)
                }
            }(e, n, r, c, F) : "伊犁" == t ? function (e, t, i, n, o) {
                let d, a = parseFloat(t["权重"]), r = parseFloat(t["系数-" + i]), s = a * n, l = o / s, x = U(l);
                return "低倍率" == x ? d = o : ("高倍率" == x || "正常倍率" == x) && (d = s), e ? {
                    DRG名称: t["DRG名称"],
                    权重: a.toFixed(4),
                    系数: r.toFixed(2),
                    费率: n,
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: d.toFixed(2),
                    DRG差额: (d - o).toFixed(2),
                    低倍临界值: (.3 * s).toFixed(2),
                    高倍临界值: (3 * s).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: a.toFixed(4),
                    系数: r.toFixed(2),
                    费率: n,
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: d.toFixed(2),
                    DRG差额: (d - o).toFixed(2)
                }
            }(e, n, r, c, F) : "阿勒泰" == t ? function (e, t, i, n, o, d) {
                let a, r = parseFloat(t["权重"]), s = n, l = r * o, x = d / l, c = ne(i, x);
                return "低倍率" == c ? a = d : ("高倍率" == c || "正常倍率" == c) && (a = l), e ? {
                    DRG名称: t["DRG名称"],
                    权重: r,
                    系数: s,
                    费率: o.toFixed(2),
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    DRG费用: a.toFixed(2),
                    DRG差额: (a - d).toFixed(2),
                    低倍临界值: (.3 * l).toFixed(2),
                    高倍临界值: (l * te(i)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: r,
                    系数: s,
                    费率: o.toFixed(2),
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    DRG费用: a.toFixed(2),
                    DRG差额: (a - d).toFixed(2)
                }
            }(e, n, r, l, c, F) : "长沙" == t ? function (e, t, i, n, o, d, a, r) {
                let s, l, x = parseFloat(i["权重"]),
                    c = ["一类"].includes(n) && "基础病组" == i["病组标识"] ? m[t]["二类"] : o, F = x * d, u = a / F,
                    D = !i["DRG编码"].startsWith("00") && !i["DRG编码"].endsWith("QY");
                return s = r <= 2 ? "按项目付费（2天）" : r >= 60 ? "按项目付费（60天）" : D && u >= 2.5 ? "按项目付费（高倍）" : D && u <= .4 ? "按项目付费（低倍）" : "按DRG付费", l = "按DRG付费" == s ? F : a, e ? {
                    DRG名称: i["DRG名称"],
                    权重: x,
                    系数: c,
                    费率: d,
                    支付标准: F.toFixed(2),
                    倍率: u.toFixed(2),
                    类型: s,
                    DRG费用: l.toFixed(2),
                    DRG差额: (l - a).toFixed(2),
                    低倍临界值: (.4 * F).toFixed(2),
                    高倍临界值: (2.5 * F).toFixed(2)
                } : {
                    DRG名称: i["DRG名称"],
                    权重: x,
                    系数: c,
                    费率: d,
                    支付标准: F.toFixed(2),
                    倍率: u.toFixed(2),
                    类型: s,
                    DRG费用: l.toFixed(2),
                    DRG差额: (l - a).toFixed(2)
                }
            }(e, t, n, r, l, c, F, D) : "阿坝" == t ? function (e, t, i, n, o, d, a) {
                let r, s = parseFloat(t["权重"]), l = o, x = n * s * l, c = a / x, F = U(c);
                "低倍率" == F ? r = a / n * 100 : ("高倍率" == F || "正常倍率" == F) && (r = 100 * s * l);
                let u = r * d;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: s.toFixed(4),
                    系数: l,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: r.toFixed(4),
                    点值: d,
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - a).toFixed(2),
                    低倍临界值: (.3 * x).toFixed(2),
                    高倍临界值: (3 * x).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: s.toFixed(4),
                    系数: l,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: r.toFixed(4),
                    点值: d,
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - a).toFixed(2)
                }
            }(e, n, 0, d, l, x, F) : "晋城" == t ? function (e, t, i, n, o) {
                let d, a = parseFloat(t["权重"]), r = parseFloat(t["系数-" + i]), s = a * n * r, l = o / s, x = U(l);
                return "低倍率" == x ? d = o : ("高倍率" == x || "正常倍率" == x) && (d = s), e ? {
                    DRG名称: t["DRG名称"],
                    权重: a,
                    病组类型: t["病组类型"],
                    系数: r,
                    费率: n.toFixed(2),
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: d.toFixed(2),
                    DRG差额: (d - o).toFixed(2),
                    低倍临界值: (.3 * s).toFixed(2),
                    高倍临界值: (3 * s).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: a,
                    病组标识: t["病组类型"],
                    系数: r,
                    费率: n.toFixed(2),
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: d.toFixed(2),
                    DRG差额: (d - o).toFixed(2)
                }
            }(e, n, r, c, F) : "马鞍山" == t ? function (e, t, i, n, o) {
                let d, a = parseFloat(t["权重"]), r = i, s = a * n * r, l = o / s, x = B(l);
                return "低倍率" == x ? d = o : ("高倍率" == x || "正常倍率" == x) && (d = s), e ? {
                    DRG名称: t["DRG名称"],
                    权重: a,
                    病组类型: t["病组类型"],
                    系数: r,
                    费率: n.toFixed(2),
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: d.toFixed(2),
                    DRG差额: (d - o).toFixed(2),
                    低倍临界值: (.4 * s).toFixed(2),
                    高倍临界值: (2 * s).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: a,
                    病组标识: t["病组类型"],
                    系数: r,
                    费率: n.toFixed(2),
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: d.toFixed(2),
                    DRG差额: (d - o).toFixed(2)
                }
            }(e, n, l, c, F) : "铜川" == t ? function (e, t, i, n) {
                let o, d = parseFloat(t["权重"]), a = d * i, r = n / a, s = U(r);
                return "低倍率" == s ? o = n : ("高倍率" == s || "正常倍率" == s) && (o = a), e ? {
                    DRG名称: t["DRG名称"],
                    权重: d,
                    病组类型: t["病组类型"],
                    费率: i.toFixed(2),
                    支付标准: a.toFixed(2),
                    倍率: r.toFixed(2),
                    类型: s,
                    DRG费用: o.toFixed(2),
                    DRG差额: (o - n).toFixed(2),
                    低倍临界值: (.3 * a).toFixed(2),
                    高倍临界值: (3 * a).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: d,
                    病组标识: t["病组类型"],
                    系数: 1,
                    费率: i.toFixed(2),
                    支付标准: a.toFixed(2),
                    倍率: r.toFixed(2),
                    类型: s,
                    DRG费用: o.toFixed(2),
                    DRG差额: (o - n).toFixed(2)
                }
            }(e, n, c, F) : "池州" == t ? function (e, t, i, n, o, d, a) {
                let r = parseFloat(i["权重"]);
                "基础组" == i["病组类型"] && (d = O[t]["一级"][o]);
                let s, l = r * d, x = a / l,
                    c = ((e, t) => "三级" == e && t < .3 || "三级" != e && t < .5 ? "低倍率" : t > 3 ? "高倍率" : "正常倍率")(n, x);
                return "低倍率" == c || "高倍率" == c ? s = a : "正常倍率" == c && (s = l), e ? {
                    DRG名称: i["DRG名称"],
                    权重: r.toFixed(4),
                    病组标识: i["病组类型"],
                    费率: d.toFixed(2),
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    DRG费用: s.toFixed(2),
                    DRG差额: (s - a).toFixed(2),
                    低倍临界值: (l * (F = n, "三级" == F ? .3 : .5)).toFixed(2),
                    高倍临界值: (3 * l).toFixed(2)
                } : {
                    DRG名称: i["DRG名称"],
                    权重: r.toFixed(4),
                    病组标识: i["病组类型"],
                    费率: d.toFixed(2),
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    DRG费用: s.toFixed(2),
                    DRG差额: (s - a).toFixed(2)
                };
                var F
            }(e, t, n, r, s, c, F) : "定州" == t ? function (e, t, i, n, o, d, a) {
                let r, s, l = parseFloat(t["权重"]), x = i, c = l * n * x, F = o / c;
                return r = F > 3 ? "按DRG付费（费用极高）" : d > 60 ? "按项目付费（时间超长）" : d < 2 ? "按项目付费（时间超短）" : F < .4 ? "按项目付费（费用极低）" : "按DRG付费（正常）", s = r.startsWith("按DRG付费") ? c : o, e ? {
                    DRG名称: t["DRG名称"],
                    权重: l,
                    系数: x,
                    费率: n,
                    支付标准: c.toFixed(2),
                    倍率: F.toFixed(2),
                    类型: r,
                    DRG费用: s.toFixed(2),
                    报销比: (a / o).toFixed(2),
                    DRG差额: ((s - o) * a / o).toFixed(2),
                    低倍临界值: (.3 * c).toFixed(2),
                    高倍临界值: (3 * c).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: l,
                    系数: x,
                    费率: n,
                    支付标准: c.toFixed(2),
                    倍率: F.toFixed(2),
                    类型: r,
                    DRG费用: s.toFixed(2),
                    DRG差额: ((s - o) * a / o).toFixed(2)
                }
            }(e, n, l, c, F, D, G) : "铜仁" == t ? function (e, t, i, n, o, d) {
                let a, r = parseFloat(t["权重"]), s = t["DRG编码"];
                "居民" == n && "基层病组" == t["病组标识"] ? (a = {
                    GW15: 1070,
                    ES35: 1300,
                    DT19: 1e3
                }[s], o = (a / r).toFixed(0)) : a = "基础病组" != t["病组标识"] && "基层病组" != t["病组标识"] || ![].includes(i) ? r * o : r * (o = "居民" == n ? 2190 : 3110);
                let l, x = d / a, c = "基础病组" == t["病组标识"] || "基层病组" == t["病组标识"] ? W(x) : U(x);
                return "低倍率" == c ? l = d : ("高倍率" == c || "正常倍率" == c) && (l = a), e ? {
                    DRG名称: t["DRG名称"],
                    权重: r,
                    病组标识: t["病组标识"],
                    费率: o,
                    支付标准: a.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    DRG费用: l.toFixed(2),
                    DRG差额: (l - d).toFixed(2),
                    低倍临界值: (a * ("基础病组" == t["病组标识"] || "基层病组" == t["病组标识"] ? .5 : .3)).toFixed(2),
                    高倍临界值: (2 * a).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: r,
                    病组标识: t["病组标识"],
                    费率: o,
                    支付标准: a.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    DRG费用: l.toFixed(2),
                    DRG差额: (l - d).toFixed(2)
                }
            }(e, n, o, s, c, F) : "眉山" == t ? I(e, n, 100, r, x, F) : "眉山2023" == t ? function (e, t, i, n, o, d, a) {
                let r, s = parseFloat(t["权重"]), l = o, x = n * s * l, c = a / x, F = $(s, c);
                "低倍率" == F ? r = a / n * 100 : ("高倍率" == F || "正常倍率" == F) && (r = 100 * s * l);
                let u = r * d;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: s.toFixed(4),
                    系数: l,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: r.toFixed(4),
                    点值: d,
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - a).toFixed(2),
                    低倍临界值: (.6 * x).toFixed(2),
                    高倍临界值: (x * X(s)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: s.toFixed(4),
                    系数: l,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: r.toFixed(4),
                    点值: d,
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - a).toFixed(2)
                }
            }(e, n, 0, d, l, x, F) : "滁州" == t ? function (e, t, i, n, o, d, a) {
                let r, s, l = {
                        EX200: 1592,
                        EX300: 2188,
                        ET200: 4883,
                        DS15: 3042,
                        ET25: 2892,
                        FV25: 3232,
                        U25: 3387,
                        NS15: 2345
                    }, x = {
                        EB11: 8e3,
                        LA15: 4e3,
                        GB21: 8e3,
                        FL19: 4e3,
                        FL23: 4e3,
                        FL25: 4e3,
                        BB21: 8e3,
                        BB23: 4e3,
                        BB25: 4e3,
                        IB29: 4e3,
                        BE21: 8e3,
                        BE23: 4e3,
                        GB11: 8e3,
                        GB13: 4e3,
                        GB15: 4e3,
                        HB13: 4e3,
                        RD19: 4e3,
                        GB23: 4e3
                    }, c = t["DRG编码"], F = parseFloat(t["权重"]), u = parseFloat(t["系数-" + o]),
                    D = l.hasOwnProperty(c) ? l[c] : n * F, R = a / D;
                r = x.hasOwnProperty(c) && R < .7 ? "低倍率" : oe(F, R), "低倍率" == r ? s = a / n * i : "高倍率" == r ? s = F * i * u + (a / (n * F) - K(F)) * F * i : "正常倍率" == r && (s = F * i * u), x.hasOwnProperty(c) && ("低倍率" != r && (s += x[c] / n * i * u), D += x[c]);
                let h = s * d, G = l.hasOwnProperty(c) ? "基层病组" : x.hasOwnProperty(c) ? "扶持病组" : "";
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: F.toFixed(4),
                    支付标准: D.toFixed(2),
                    病组标识: G,
                    系数: u,
                    倍率: R.toFixed(2),
                    类型: r,
                    总点数: s.toFixed(2),
                    点值: d,
                    DRG费用: h.toFixed(2),
                    DRG差额: (h - a).toFixed(2),
                    低倍临界值: (.3 * D).toFixed(2),
                    高倍临界值: (D * K(F)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: F.toFixed(4),
                    支付标准: D.toFixed(2),
                    病组标识: G,
                    系数: u,
                    倍率: R.toFixed(2),
                    类型: r,
                    总点数: s.toFixed(2),
                    点值: d,
                    DRG费用: h.toFixed(2),
                    DRG差额: (h - a).toFixed(2)
                }
            }(e, n, 100, d, a, x, F) : "驻马店" == t ? function (e, t, i, n, o) {
                let d, a = parseFloat(t["权重"]), r = "基础病组" == t["病组标识"] ? 1 : i, s = a * n * r, l = o / s,
                    x = ((e, t) => t < .4 ? "低倍率" : e < 1 && t > 3 || e >= 1 && t > 2 ? "高倍率" : "正常倍率")(a, l);
                return "低倍率" == x ? d = o : "高倍率" == x ? d = (l - he(a) + 1) * s : "正常倍率" == x && (d = s), e ? {
                    DRG名称: t["DRG名称"],
                    权重: a.toFixed(4),
                    基础病组: t["基础病组"],
                    系数: r,
                    费率: n,
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: d.toFixed(2),
                    DRG差额: (d - o).toFixed(2),
                    低倍临界值: (.5 * s).toFixed(2),
                    高倍临界值: (s * he(a)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: a.toFixed(4),
                    病组标识: t["基础病组"] ? "基础病组" : "",
                    系数: r,
                    费率: n,
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: d.toFixed(2),
                    DRG差额: (d - o).toFixed(2)
                }
            }(e, n, l, c, F) : "福州" == t ? function (e, t, i, n, o) {
                let d, a = parseFloat(t["权重"]), r = i, s = a * n * r, l = o / s, x = W(l);
                return "低倍率" == x ? d = o : ("高倍率" == x || "正常倍率" == x) && (d = s), e ? {
                    DRG名称: t["DRG名称"],
                    权重: a.toFixed(4),
                    系数: r,
                    费率: n,
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: d.toFixed(2),
                    DRG差额: (d - o).toFixed(2),
                    低倍临界值: (.5 * s).toFixed(2),
                    高倍临界值: (2 * s).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: a.toFixed(4),
                    系数: r,
                    费率: n,
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: d.toFixed(2),
                    DRG差额: (d - o).toFixed(2)
                }
            }(e, n, l, c, F) : ["广安", "广安扩面"].includes(t) ? function (e, t, i, n, o, d, a) {
                let r, s = parseFloat(t["权重"]), l = o, x = n * s, c = a / x,
                    F = ((e, t) => t < .4 ? "低倍率" : e <= 1 && t > 3 || e > 1 && e <= 2 && t > 2 || e > 2 && t > 1.5 ? "高倍率" : "正常倍率")(s, c);
                "低倍率" == F ? r = a / n * i : "高倍率" == F ? r = s * i * l + (a / (n * s) - Y(s)) * s * i : "正常倍率" == F && (r = s * i * l);
                let u = r * d;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: s.toFixed(4),
                    系数: l,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: r.toFixed(4),
                    点值: d,
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - a).toFixed(2),
                    低倍临界值: (.4 * x).toFixed(2),
                    高倍临界值: (x * Y(s)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: s.toFixed(4),
                    系数: l,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: r.toFixed(4),
                    点值: d,
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - a).toFixed(2)
                }
            }(e, n, 100, d, l, x, F) : "泰州" == t ? function (e, t, i, n, o, d, a, r) {
                let s, l = parseFloat(t["权重"]);
                s = t["系数-" + o] ? parseFloat(t["系数-" + o]) : .8 != d ? d : .8;
                let x, c = n * l, F = r / c,
                    u = ((e, t) => t < .4 ? "低倍率" : e <= 1.5 && t > 2.5 || e > 1.5 && e <= 2.5 && t > 2 || e > 2.5 && t > 1.5 ? "高倍率" : "正常倍率")(l, F);
                "低倍率" == u ? x = r / n * i * s : "高倍率" == u ? x = l * i * s + (r / (n * l) - Re(l)) * l * i : "正常倍率" == u && (x = l * i * s);
                let D = x * a;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: l.toFixed(4),
                    病组标识: t["病组标识"],
                    系数: s,
                    支付标准: c.toFixed(2),
                    倍率: F.toFixed(2),
                    类型: u,
                    总点数: x.toFixed(4),
                    点值: a.toFixed(2),
                    DRG费用: D.toFixed(2),
                    DRG差额: (D - r).toFixed(2),
                    低倍临界值: (.4 * c).toFixed(2),
                    高倍临界值: (c * Re(l)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: l.toFixed(4),
                    病组标识: t["病组标识"],
                    系数: s,
                    支付标准: c.toFixed(2),
                    倍率: F.toFixed(2),
                    类型: u,
                    总点数: x.toFixed(4),
                    点值: a.toFixed(2),
                    DRG费用: D.toFixed(2),
                    DRG差额: (D - r).toFixed(2)
                }
            }(e, n, 100, d, r, l, x, F) : "西宁" == t ? function (e, t, i, n) {
                let o, d = t["权重"] ? parseFloat(t["权重"]) : 1, a = d * i * 1, r = n / a, s = U(r);
                return "低倍率" == s ? o = n : ("高倍率" == s || "正常倍率" == s) && (o = a), e ? {
                    DRG名称: t["DRG名称"],
                    权重: d,
                    费率: i,
                    支付标准: a.toFixed(2),
                    倍率: r.toFixed(2),
                    类型: s,
                    DRG费用: o.toFixed(2),
                    DRG差额: (o - n).toFixed(2),
                    低倍临界值: (.3 * a).toFixed(2),
                    高倍临界值: (3 * a).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: d,
                    费率: i,
                    支付标准: a.toFixed(2),
                    倍率: r.toFixed(2),
                    类型: s,
                    DRG费用: o.toFixed(2),
                    DRG差额: (o - n).toFixed(2)
                }
            }(e, n, c, F) : "资阳" == t ? function (e, t, i, n, o, d) {
                let a = parseFloat(t["权重"]), r = parseFloat(t["系数-" + n]), s = t["均费"];
                if (isNaN(a) || isNaN(r) || isNaN(s)) return {
                    DRG名称: t["DRG名称"],
                    权重: "",
                    例均费用: "",
                    系数: "",
                    倍率: "",
                    类型: "",
                    总点数: "",
                    点值: "",
                    DRG费用: "",
                    DRG差额: ""
                };
                let l, x = parseFloat(s), c = d / x, F = de(a, c);
                "低倍率" == F ? l = d / s * a * i : "高倍率" == F ? l = a * i * r + (d / s - ae(a)) * a * i : "正常倍率" == F && (l = a * i * r);
                let u = l * o;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: a,
                    例均费用: x.toFixed(2),
                    系数: r,
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: l.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - d).toFixed(2),
                    低倍临界值: (.3 * x).toFixed(2),
                    高倍临界值: (x * ae(a)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: a,
                    例均费用: x.toFixed(2),
                    系数: r,
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: l.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - d).toFixed(2)
                }
            }(e, n, rate, a, x, F) : "重庆" == t ? function (e, t, i, n, o, d, a, r) {
                let s, l = i["权重"] ? parseFloat(i["权重"]) : 1;
                s = "基础组" == i["病组标识"] ? m[t]["二级"] : "无级差组" == i["病组标识"] || "三级" == n && "三级无级差组" == i["病组标识"] ? 1 : o;
                let x, c, F = l * d * s, u = a / F;
                return x = r < 2 || r > 60 ? "按项目（住院天数）" : "无权重组" == i["病组标识"] ? "按项目（无权重组）" : "不稳定组" == i["病组标识"] ? "按项目（不稳定组）" : ((e, t) => t < .4 ? "低倍率" : e <= 1 && t > 3 || e > 1 && e <= 2 && t > 2.5 || e > 2 && e <= 3 && t > 2 || e > 3 && t > 1.5 ? "高倍率" : "正常倍率")(l, u), "低倍率" == x || x.startsWith("按项目") ? c = a : ("高倍率" == x || "正常倍率" == x) && (c = F), e ? {
                    DRG名称: i["DRG名称"],
                    // 权重: l,
                    病组标识: i["病组标识"],
                    // 系数: s,
                    // 费率: d,
                    支付标准: F.toFixed(2),
                    倍率: u.toFixed(2),
                    类型: x,
                    DRG费用: c.toFixed(2),
                    DRG差额: (c - a).toFixed(2),
                    低倍临界值: (.5 * F).toFixed(2),
                    高倍临界值: (F * De(l)).toFixed(2),
                    次均费用: Tt(window.location.href).averageAmt,
                    平均住院: Tt(window.location.href).averageDay,
                    CMI:Tt(window.location.href).cmi,
                    时间消耗:Tt(window.location.href).tcti,
                    费用消耗:Tt(window.location.href).cci,
                } : {
                    DRG名称: i["DRG名称"],
                    权重: l,
                    病组标识: i["病组标识"],
                    系数: s,
                    费率: d,
                    支付标准: F.toFixed(2),
                    倍率: u.toFixed(2),
                    类型: x,
                    DRG费用: c.toFixed(2),
                    DRG差额: (c - a).toFixed(2)
                }
            }(e, t, n, r, l, c, F, D) : "遂宁" == t ? function (e, t, i, n, o, d, a) {
                let r, s = parseFloat(t["权重"]),
                    l = t["基础病组"] ? 1 : t["系数-" + o] ? parseFloat(t["系数-" + o]) : 1, x = n * s, c = a / x,
                    F = de(s, c);
                "低倍率" == F ? r = a / n * i : "高倍率" == F ? r = s * i * l + (a / (n * s) - ae(s)) * s * i : "正常倍率" == F && (r = s * i * l);
                let u = r * d;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: s,
                    系数: l,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: r.toFixed(4),
                    点值: d.toFixed(2),
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - a).toFixed(2),
                    低倍临界值: (.3 * x).toFixed(2),
                    高倍临界值: (x * ae(s)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: s,
                    系数: l,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: r.toFixed(4),
                    点值: d.toFixed(2),
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - a).toFixed(2)
                }
            }(e, n, 100, d, r, x, F) : "衡阳" == t ? function (e, t, i, n, o) {
                let d, a = parseFloat(t["权重"]), r = "基础病组" == t["病组标识"] ? 1 : i, s = a * n * r, l = o / s,
                    x = ((e, t) => t < .5 ? "低倍率" : e <= 4 && t > 3 || e > 4 && e <= 7 && t > 2.5 || e > 7 && e <= 10 && t > 2 || e > 10 && t > 1.5 ? "高倍率" : "正常倍率")(a, l);
                return "低倍率" == x ? d = o : ("高倍率" == x || "正常倍率" == x) && (d = s), e ? {
                    DRG名称: t["DRG名称"],
                    权重: a.toFixed(4),
                    病组标识: t["病组标识"],
                    系数: r,
                    费率: n,
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: d.toFixed(2),
                    DRG差额: (d - o).toFixed(2),
                    低倍临界值: (.5 * s).toFixed(2),
                    高倍临界值: (s * ue(a)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: a.toFixed(4),
                    病组标识: t["病组标识"],
                    系数: r,
                    费率: n,
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: d.toFixed(2),
                    DRG差额: (d - o).toFixed(2)
                }
            }(e, n, l, c, F) : "郴州" == t ? function (e, t, i, n, o, d) {
                let a = parseFloat(t["权重"]);
                t["基础病组"] && (i = 3400);
                let r, s, l = a * i, x = n / l;
                return ["A", "U", "T", "X"].includes(t["DRG编码"][0]) && (r = "按项目付费（排除分组）"), t["基础病组"] && (r = "总量控制（基础病组）"), "5" == d && (r = "按项目付费（死亡）"), r = o > 60 ? "按床日清算（超60天）" : x >= 3 ? "按项目付费（费用极高）" : x <= .4 ? "按项目付费（费用极低）" : "按DRG付费", s = ["按DRG付费", "按床日清算（超60天）"].includes(r) ? l : n, e ? {
                    DRG名称: t["DRG名称"],
                    权重: a,
                    费率: i,
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: r,
                    DRG费用: s.toFixed(2),
                    DRG差额: (s - n).toFixed(2),
                    低倍临界值: (.4 * l).toFixed(2),
                    高倍临界值: (3 * l).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: a,
                    费率: i,
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: r,
                    DRG费用: s.toFixed(2),
                    DRG差额: (s - n).toFixed(2)
                }
            }(e, n, c, F, D, R) : "武汉" == t ? function (e, t, i, n) {
                let o, d = parseFloat(t["权重"]), a = d * i, r = n / a, s = U(r);
                return "低倍率" == s ? o = n : ("高倍率" == s || "正常倍率" == s) && (o = a), e ? {
                    DRG名称: t["DRG名称"],
                    权重: d,
                    费率: i,
                    支付标准: a.toFixed(2),
                    倍率: r.toFixed(2),
                    类型: s,
                    DRG费用: o.toFixed(2),
                    DRG差额: (o - n).toFixed(2),
                    低倍临界值: (.4 * a).toFixed(2),
                    高倍临界值: (2 * a).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: d,
                    费率: i,
                    支付标准: a.toFixed(2),
                    倍率: r.toFixed(2),
                    类型: s,
                    DRG费用: o.toFixed(2),
                    DRG差额: (o - n).toFixed(2)
                }
            }(e, n, c, F) : "太原" == t ? function (e, t, i, n) {
                let o, d = parseFloat(t["权重"]), a = d * i, r = n / a, s = B(r);
                return "低倍率" == s ? o = n : ("高倍率" == s || "正常倍率" == s) && (o = a), e ? {
                    DRG名称: t["DRG名称"],
                    权重: d.toFixed(4),
                    费率: i.toFixed(2),
                    支付标准: a.toFixed(2),
                    倍率: r.toFixed(2),
                    类型: s,
                    DRG费用: o.toFixed(2),
                    DRG差额: (o - n).toFixed(2),
                    低倍临界值: (.4 * a).toFixed(2),
                    高倍临界值: (2 * a).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: d.toFixed(4),
                    费率: i.toFixed(2),
                    支付标准: a.toFixed(2),
                    倍率: r.toFixed(2),
                    类型: s,
                    DRG费用: o.toFixed(2),
                    DRG差额: (o - n).toFixed(2)
                }
            }(e, n, c, F) : "邯郸" == t ? function (e, t, i, n) {
                let o, d = parseFloat(t["权重"]), a = d * i, r = n / a,
                    s = (e => e < .4 ? "低倍率" : e >= 2 ? "高倍率" : "正常倍率")(r);
                return "低倍率" == s ? o = n : ("高倍率" == s || "正常倍率" == s) && (o = a), e ? {
                    DRG名称: t["DRG名称"],
                    权重: d.toFixed(4),
                    费率: i,
                    支付标准: a.toFixed(2),
                    倍率: r.toFixed(2),
                    类型: s,
                    DRG费用: o.toFixed(2),
                    DRG差额: (o - n).toFixed(2),
                    低倍临界值: (.4 * a).toFixed(2),
                    高倍临界值: (2 * a).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: d.toFixed(4),
                    病组标识: t["病组标识"],
                    费率: i,
                    支付标准: a.toFixed(2),
                    倍率: r.toFixed(2),
                    类型: s,
                    DRG费用: o.toFixed(2),
                    DRG差额: (o - n).toFixed(2)
                }
            }(e, n, c, F) : "沈阳" == t ? function (e, t, i, n, o) {
                let d = parseFloat(t["权重"]);
                t["病组类型"].startsWith("基层病组") && (n = "职工" == i ? 8500 : 8e3);
                let a, r = d * n, s = o / r, l = U(s);
                return "低倍率" == l ? a = o : ("高倍率" == l || "正常倍率" == l) && (a = r), e ? {
                    DRG名称: t["DRG编码"] + "-" + t["DRG名称"].split("，").slice(-1) + (t["病组类型"] ? "[" + t["病组类型"] + "]" : ""),
                    权重: d.toFixed(4),
                    费率: n,
                    支付标准: r.toFixed(2),
                    倍率: s.toFixed(2),
                    类型: l,
                    DRG费用: a.toFixed(2),
                    DRG差额: (a - o).toFixed(2),
                    低倍临界值: (.3 * r).toFixed(2),
                    高倍临界值: (3 * r).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: d.toFixed(4),
                    病组标识: t["病组类型"],
                    费率: n,
                    支付标准: r.toFixed(2),
                    倍率: s.toFixed(2),
                    类型: l,
                    DRG费用: a.toFixed(2),
                    DRG差额: (a - o).toFixed(2)
                }
            }(e, n, s, c, F) : "蚌埠" == t ? function (e, t, i, n, o, d, a) {
                let r, s = parseFloat(t["权重"]),
                    l = t["基础病组"] ? 1 : t["系数-" + o] ? parseFloat(t["系数-" + o]) : 1, x = n * s, c = a / x,
                    F = oe(s, c);
                "低倍率" == F ? r = a / n * i : "高倍率" == F ? r = s * i * l + (a / (n * s) - K(s)) * s * i : "正常倍率" == F && (r = s * i * l);
                let u = r * d;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: s.toFixed(4),
                    基础病组: t["基础病组"],
                    系数: l,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: r.toFixed(4),
                    点值: d.toFixed(2),
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - a).toFixed(2),
                    低倍临界值: (.3 * x).toFixed(2),
                    高倍临界值: (x * K(s)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: s.toFixed(4),
                    病组标识: t["基础病组"] ? "基础病组" : "",
                    系数: l,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: r.toFixed(4),
                    点值: d.toFixed(2),
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - a).toFixed(2)
                }
            }(e, n, 100, d, r, x, F) : "牡丹江" == t ? function (e, t, i, n, o, d) {
                let a, r = parseFloat(t["权重"]), s = n, l = i * r, x = d / l, c = B(x);
                "低倍率" == c ? a = d / i : "高倍率" == c ? a = r * s + (d / (i * r) - 2) * r : "正常倍率" == c && (a = r * s);
                let F = a * o;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    支付标准: l.toFixed(2),
                    系数: s.toFixed(4),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(4),
                    点值: o.toFixed(0),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2),
                    低倍临界值: (.4 * l).toFixed(2),
                    高倍临界值: (2 * l).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    支付标准: l.toFixed(2),
                    系数: s.toFixed(4),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(4),
                    点值: o.toFixed(0),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2)
                }
            }(e, n, d, l, x, F) : "大同" == t ? function (e, t, i, n, o) {
                let d = parseFloat(t["权重"]);
                "激励病组" == t["病组类型"] && (d *= 1.07), "分级诊疗组" == t["病组类型"] && n > 8422.7 && (n = 8422.7);
                let a, r = d * n, s = o / r, l = [""].includes(i) ? "三级" : "二级", x = ne(l, s);
                return "低倍率" == x ? a = o : ("高倍率" == x || "正常倍率" == x) && (a = r), e ? {
                    DRG名称: t["DRG编码"] + "-" + t["DRG名称"].split("，").slice(-1) + (t["病组类型"] ? "[" + t["病组类型"] + "]" : ""),
                    权重: d.toFixed(4),
                    费率: n.toFixed(2),
                    支付标准: r.toFixed(2),
                    倍率: s.toFixed(2),
                    类型: x,
                    DRG费用: a.toFixed(2),
                    DRG差额: (a - o).toFixed(2),
                    低倍临界值: (.3 * r).toFixed(2),
                    高倍临界值: (r * te(l)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: d.toFixed(4),
                    费率: n.toFixed(2),
                    支付标准: r.toFixed(2),
                    倍率: s.toFixed(2),
                    类型: x,
                    DRG费用: a.toFixed(2),
                    DRG差额: (a - o).toFixed(2)
                }
            }(e, n, o, c, F) : "天津" == t ? function (e, t, i, n, o, d, a) {
                if ("" == t["权重"]) return {
                    DRG名称: t["DRG名称"],
                    权重: "",
                    备注: t["备注"],
                    DRG费用: d.toFixed(2),
                    DRG差额: 0
                };
                let r, s = parseFloat(t["权重"]), l = a, x = t["平均住院费用"], c = d / x,
                    F = (e => e < .5 ? "低倍率" : e >= 3 ? "高倍率" : "正常倍率")(c);
                "" !== t["备注"] || "低倍率" == F ? r = d : ("高倍率" == F || "正常倍率" == F) && (r = 1e4 * s * l);
                let u = r * o;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: s,
                    备注: t["备注"],
                    系数: l,
                    支付标准: s * l * 1e4 * o,
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: r.toFixed(2),
                    点值: o,
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - d).toFixed(2),
                    低倍临界值: (.5 * x).toFixed(2),
                    高倍临界值: (3 * x).toFixed(2),
                    平均住院天数: t["平均住院天数"],
                    平均住院费用: t["平均住院费用"]
                } : {
                    DRG名称: t["DRG名称"],
                    权重: s,
                    病组标识: t["备注"],
                    系数: l,
                    支付标准: s * l * 1e4 * o,
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: r.toFixed(2),
                    点值: o,
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - d).toFixed(2),
                    平均住院天数: t["平均住院天数"],
                    平均住院费用: t["平均住院费用"]
                }
            }(e, n, 0, 0, x, F, h) : "无锡" == t ? function (e, t, i, n, o) {
                let d = parseFloat(t["权重"]);
                t["基础病组"] && (i = 8767);
                let a = o, r = d * i * a, s = n / r, l = r;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: d,
                    基础病组: t["基础病组"],
                    系数: a.toFixed(2),
                    费率: i.toFixed(2),
                    支付标准: r.toFixed(2),
                    倍率: s.toFixed(2),
                    类型: "正常",
                    DRG费用: l.toFixed(2),
                    DRG差额: (l - n).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: d,
                    病组标识: t["基础病组"] ? "基础病组" : "",
                    系数: a.toFixed(2),
                    费率: i.toFixed(2),
                    支付标准: r.toFixed(2),
                    倍率: s.toFixed(2),
                    类型: "正常",
                    DRG费用: l.toFixed(2),
                    DRG差额: (l - n).toFixed(2)
                }
            }(e, n, c, F, h) : "喀什" == t ? function (e, t, i, n, o, d, a) {
                let r, s = parseFloat(t["权重"]), l = "基础病组" == t["病组类型"] ? 1 : o, x = s * d * l, c = a / x,
                    F = ie(n, c);
                return "低倍率" == F ? r = a : ("高倍率" == F || "正常倍率" == F) && (r = x), e ? {
                    DRG名称: t["DRG名称"],
                    权重: s,
                    基础病组: t["基础病组"],
                    系数: l,
                    费率: d.toFixed(2),
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    DRG费用: r.toFixed(2),
                    DRG差额: (r - a).toFixed(2),
                    低倍临界值: (.4 * x).toFixed(2),
                    高倍临界值: (x * te(n)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: s,
                    病组标识: t["基础病组"] ? "基础病组" : "",
                    系数: l,
                    费率: d.toFixed(2),
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    DRG费用: r.toFixed(2),
                    DRG差额: (r - a).toFixed(2)
                }
            }(e, n, 0, r, l, c, F) : "济源" == t ? function (e, t, i, n, o, d, a) {
                let r, s = parseFloat(t["权重"]), l = t["基础病组"] ? .8 : parseFloat(t["系数-" + n]), x = i * s * l,
                    c = d / x, F = oe(s, c);
                "低倍率" == F ? r = d / i * 100 : "高倍率" == F ? r = 100 * (d / i - (K(s) - 1) * s * l) : "正常倍率" == F && (r = 100 * s * l), a < 17 && (r *= 1.1);
                let u = r * o;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: s.toFixed(4),
                    系数: l,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: r.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - d).toFixed(2),
                    低倍临界值: (.3 * x).toFixed(2),
                    高倍临界值: (x * K(s)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: s.toFixed(4),
                    系数: l,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: r.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - d).toFixed(2)
                }
            }(e, n, d, r, x, F, u) : "鞍山" == t ? function (e, t, i, n, o) {
                let d, a = parseFloat(t["权重"]), r = t["基础病组"] ? .8 : i, s = a * n * r, l = o / s, x = ee(a, l);
                return "低倍率" == x ? d = o : "高倍率" == x ? d = s + .9 * (o - s) : "正常倍率" == x && (d = s), e ? {
                    DRG名称: t["DRG名称"],
                    权重: a.toFixed(4),
                    基础病组: t["基础病组"],
                    系数: r,
                    费率: n,
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: d.toFixed(2),
                    DRG差额: (d - o).toFixed(2),
                    低倍临界值: (.4 * s).toFixed(2),
                    高倍临界值: (s * K(a)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: a.toFixed(4),
                    病组标识: t["基础病组"] ? "基础病组" : "",
                    系数: r,
                    费率: n,
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: d.toFixed(2),
                    DRG差额: (d - o).toFixed(2)
                }
            }(e, n, l, c, F) : "南京" == t ? function (e, t, i, n, o, d) {
                let a, r = parseFloat(t["权重"]), s = t["基础病组"] ? 1 : n, l = i * r, x = d / l, c = V(r, x);
                "低倍率" == c ? a = d / i * 100 : "高倍率" == c ? a = 100 * r * s + 100 * r * (d / (i * r) - Q(r)) : "正常倍率" == c && (a = 100 * r * s);
                let F = a * o;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: r,
                    基础病组: t["基础病组"],
                    系数: s,
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o,
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2),
                    低倍临界值: (.4 * l).toFixed(2),
                    高倍临界值: (l * Q(r)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: r,
                    基础病组: t["基础病组"],
                    系数: s,
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o,
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2)
                }
            }(e, n, d, l, x, F) : "绵阳" == t ? function (e, t, i, n, o, d) {
                let a, r = parseFloat(t["权重"]),
                    s = "基础病组" == t["病组类型"] ? 1 : t["系数-" + n] ? parseFloat(t["系数-" + n]) : 1, l = i * r,
                    x = d / l,
                    c = ((e, t) => t < .3 ? "低倍率" : e <= 1 && t > 3 || e > 1 && e <= 2.5 && t > 2 || e > 2.5 && t > 1.5 ? "高倍率" : "正常倍率")(r, x);
                "低倍率" == c ? a = d / i * 100 : "高倍率" == c ? a = 100 * r * s + (d / (i * r) - J(r)) * r * 100 : "正常倍率" == c && (a = 100 * r * s);
                let F = a * o;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    病组标识: t["病组类型"],
                    系数: s,
                    支付标准: (4500 * r * s).toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2),
                    低倍临界值: (.3 * l).toFixed(2),
                    高倍临界值: (l * J(r)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    病组标识: t["病组类型"],
                    系数: s,
                    支付标准: (4500 * r * s).toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2)
                }
            }(e, n, d, r, x, F) : "四川省本级" == t ? function (e, t, i, n, o, d) {
                let a, r = parseFloat(t["权重"]), s = parseFloat(t["系数-" + n]), l = i * r, x = d / l,
                    c = ((e, t) => t < .3 ? "低倍率" : e <= 2 && t > 2 || e > 2 && t > 1.5 ? "高倍率" : "正常倍率")(r, x);
                "低倍率" == c ? a = d / i * 100 : "高倍率" == c ? a = 100 * r * s + (d / (i * r) - re(r)) * r * 100 : "正常倍率" == c && (a = 100 * r * s);
                let F = a * o;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: r,
                    例均费用: l.toFixed(2),
                    系数: s,
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2),
                    低倍临界值: (.3 * l).toFixed(2),
                    高倍临界值: (l * re(r)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: r,
                    例均费用: l.toFixed(2),
                    系数: s,
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2)
                }
            }(e, n, d, a, x, F) : "西安" == t ? function (e, t, i, n, o, d) {
                let a = parseFloat(i["权重"]);
                "基层病组" == i["病组标识"] && "三级" == n && (o = O[t]["二级"]);
                let r, s = a * o, l = d / s, x = U(l);
                return "低倍率" == x || "不稳定病组" == i["病组标识"] || "高倍率" == x ? r = d : "正常倍率" == x && (r = s), e ? {
                    DRG名称: i["DRG名称"],
                    权重: a.toFixed(4),
                    病组标识: i["病组标识"],
                    费率: o.toFixed(2),
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: r.toFixed(2),
                    DRG差额: (r - d).toFixed(2),
                    低倍临界值: (.3 * s).toFixed(2),
                    高倍临界值: (3 * s).toFixed(2)
                } : {
                    DRG名称: i["DRG名称"],
                    权重: a.toFixed(4),
                    病组标识: i["病组标识"],
                    费率: o.toFixed(2),
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: r.toFixed(2),
                    DRG差额: (r - d).toFixed(2)
                }
            }(e, t, n, r, c, F) : "自贡" == t ? function (e, t, i, n, o, d) {
                let a, r = parseFloat(t["权重"]), s = parseFloat(t["系数-" + n]), l = i * r, x = d / l, c = oe(r, x);
                "低倍率" == c ? a = d / i * 100 : "高倍率" == c ? a = 100 * r * s + (d / (i * r) - K(r)) * r * 100 : "正常倍率" == c && (a = 100 * r * s);
                let F = a * o;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    支付标准: l.toFixed(2),
                    系数: s.toFixed(4),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o,
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2),
                    低倍临界值: (.3 * l).toFixed(2),
                    高倍临界值: (l * K(r)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    支付标准: l.toFixed(2),
                    系数: s.toFixed(4),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o,
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2)
                }
            }(e, n, d, r, x, F) : "雅安" == t ? function (e, t, i, n, o, d) {
                let a, r = parseFloat(t["权重"]), s = parseFloat(t["系数-" + n]), l = i * r, x = d / l, c = oe(r, x);
                "低倍率" == c ? a = d / i * 100 : "高倍率" == c ? a = 100 * r * s + (d / (i * r) - K(r)) * r * 100 : "正常倍率" == c && (a = 100 * r * s);
                let F = a * o;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    支付标准: l.toFixed(2),
                    病组标识: t["病组标识"],
                    系数: s,
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o,
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2),
                    低倍临界值: (.3 * l).toFixed(2),
                    高倍临界值: (l * K(r)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    病组标识: t["病组标识"],
                    系数: s,
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o,
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2)
                }
            }(e, n, d, r, x, F) : "丹东" == t ? function (e, t, i, n, o, d, a) {
                let r, s = parseFloat(i["权重"]), l = i["基础病组"] ? m[t]["二级"] : o, x = s * d * l, c = a / x,
                    F = ((e, t) => t < .6 ? "低倍率" : e <= 1 && t > 3 || e > 1 && e <= 3 && t > 2 || e > 3 && t > 1.5 ? "高倍率" : "正常倍率")(s, c);
                return "低倍率" == F ? r = a / (n * s) * s * d : "高倍率" == F ? r = (a / n - (Q(s) - 1) * s) * d : "正常倍率" == F && (r = x), e ? {
                    DRG名称: i["DRG名称"],
                    权重: s.toFixed(4),
                    基础病组: i["基础病组"],
                    系数: l,
                    费率: d.toFixed(2),
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    DRG费用: r.toFixed(2),
                    DRG差额: (r - a).toFixed(2),
                    低倍临界值: (.6 * x).toFixed(2),
                    高倍临界值: (x * Q(s)).toFixed(2)
                } : {
                    DRG名称: i["DRG名称"],
                    权重: s.toFixed(4),
                    病组标识: i["基础病组"] ? "基础病组" : "",
                    系数: l,
                    费率: d.toFixed(2),
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    DRG费用: r.toFixed(2),
                    DRG差额: (r - a).toFixed(2)
                }
            }(e, t, n, d, l, c, F) : "盐城" == t ? function (e, t, i, n, o, d) {
                let a, r = parseFloat(t["权重"]), s = t["系数-" + n] ? parseFloat(t["系数-" + n]) : {
                    一级: .64,
                    二级中医: .6,
                    二级: .98,
                    三级: 1.14
                }[n], l = i * r, x = d / l, c = V(r, x);
                "低倍率" == c || t["单议病组"] ? a = d / i * 100 : "高倍率" == c ? a = 100 * (d / i - (xe(r) - 1) * r * s) : "正常倍率" == c && (a = r * s * 100);
                let F = a * o;
                if (e) {
                    let e = {
                        DRG名称: t["DRG名称"],
                        权重: r.toFixed(4),
                        例均费用: l.toFixed(2),
                        系数: s.toFixed(4),
                        倍率: x.toFixed(2),
                        类型: c,
                        总点数: a.toFixed(2),
                        点值: o.toFixed(2),
                        DRG费用: F.toFixed(2),
                        DRG差额: (F - d).toFixed(2),
                        低倍临界值: (.4 * l).toFixed(2),
                        高倍临界值: (l * xe(r)).toFixed(2)
                    };
                    return t["单议病组"] ? Object.assign({单议病组: "是"}, e) : e
                }
                return {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    例均费用: l.toFixed(2),
                    系数: s.toFixed(4),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2)
                }
            }(e, n, d, r, x, F) : "咸阳" == t ? function (e, t, i, n, o) {
                let d, a = parseFloat(t["权重"]), r = parseFloat(t["系数-" + i]), s = a * n * r, l = o / s, x = U(l);
                return "低倍率" == x || "高倍率" == x ? d = o : "正常倍率" == x && (d = s), e ? {
                    DRG名称: t["DRG名称"],
                    权重: a.toFixed(4),
                    系数: r,
                    费率: n.toFixed(2),
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: d.toFixed(2),
                    DRG差额: (d - o).toFixed(2),
                    低倍临界值: (.3 * s).toFixed(2),
                    高倍临界值: (3 * s).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: a.toFixed(4),
                    系数: r,
                    费率: n.toFixed(2),
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: d.toFixed(2),
                    DRG差额: (d - o).toFixed(2)
                }
            }(e, n, r, c, F) : "克州" == t ? function (e, t, i, n, o, d) {
                let a, r = parseFloat(t["权重"]), s = t["基础病组"] ? 1 : n, l = i * r, x = d / l,
                    c = (e => e < .5 ? "低倍率" : e >= 2 ? "高倍率" : "正常倍率")(r);
                "低倍率" == c ? a = d / i : "高倍率" == c ? a = r * s + (d / (i * r) - 1) * r : "正常倍率" == c && (a = r * s);
                let F = a * o;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    系数: s,
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    费率: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2),
                    低倍临界值: (.5 * l).toFixed(2),
                    高倍临界值: (2 * l).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    系数: s,
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    费率: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2)
                }
            }(e, n, d, l, c, F) : "漯河" == t ? function (e, t, i, n, o, d) {
                let a, r = parseFloat(t["权重"]),
                    s = "基础病组" == t["病组类型"] ? 1 : t["系数-" + n] ? parseFloat(t["系数-" + n]) : 1, l = i * r,
                    x = d / l, c = ee(r, x);
                "低倍率" == c ? a = d / i * 100 : "高倍率" == c ? a = 100 * r * s + (d / (i * r) - K(r)) * r * 100 : "正常倍率" == c && (a = 100 * r * s);
                let F = a * o;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    系数: s.toFixed(4),
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2),
                    低倍临界值: (.4 * l).toFixed(2),
                    高倍临界值: (l * K(n)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    系数: s.toFixed(4),
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2)
                }
            }(e, n, d, r, x, F) : ["兰州", "甘肃省直"].includes(t) ? function (e, t, i, n, o, d, a, r) {
                let l = parseFloat(i["权重"]);
                let x = i["基础病组"]?.includes(d);
                let s, c = x && !["一级", "二级"].includes(n) ? m[t]["二级"] : o, F = l * a * c, u = r / F,
                    D = ie(n, u);
                return "低倍率" == D || "高倍率" == D ? s = r : "正常倍率" == D && (s = F), e ? {
                    DRG名称: i["DRG名称"],
                    权重: l,
                    基础病组: i["基础病组"],
                    系数: c,
                    费率: a.toFixed(2),
                    支付标准: F.toFixed(2),
                    倍率: u.toFixed(2),
                    类型: D,
                    DRG费用: s.toFixed(2),
                    DRG差额: (s - r).toFixed(2),
                    低倍临界值: (.4 * F).toFixed(2),
                    高倍临界值: (F * te(n)).toFixed(2)
                } : {
                    DRG名称: i["DRG名称"],
                    权重: l,
                    病组标识: x ? "基础病组" : "",
                    系数: c,
                    费率: a.toFixed(2),
                    支付标准: F.toFixed(2),
                    倍率: u.toFixed(2),
                    类型: D,
                    DRG费用: s.toFixed(2),
                    DRG差额: (s - r).toFixed(2)
                }
            }(e, t, n, r, l, s, c, F) : "巴中" == t ? function (e, t, i, n, o, d, a) {
                let r, s = parseFloat(t["权重"]), l = parseFloat(t["系数-" + o]), x = n * s * l, c = a / x, F = $(s, c);
                "低倍率" == F ? r = a / n * 100 : "高倍率" == F ? r = 100 * (a / n - (X(s) - 1) * s * l) : "正常倍率" == F && (r = 100 * s * l);
                let u = r * d;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: s.toFixed(4),
                    系数: l,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: r.toFixed(2),
                    点值: d.toFixed(2),
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - a).toFixed(2),
                    低倍临界值: (.6 * x).toFixed(2),
                    高倍临界值: (x * X(s)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: s.toFixed(4),
                    系数: l,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: r.toFixed(2),
                    点值: d.toFixed(2),
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - a).toFixed(2)
                }
            }(e, n, 0, d, r, x, F) : "南通" == t ? function (e, t, i, n, o, d, a) {
                let r, s = parseFloat(t["权重"]);
                r = "基础病组" == t["备注"] ? 1 : t["系数-" + o] ? parseFloat(t["系数-" + o]) : 0;
                let l, x = n * s, c = a / x,
                    F = ((e, t) => t < .5 ? "低倍率" : e <= 1 && t > 3 || e > 1 && e <= 2 && t > 2.5 || e > 2 && e <= 3 && t > 2 || e > 3 && e <= 5 && t > 1.5 || e > 5 && t > 1.3 ? "高倍率" : "正常倍率")(s, c);
                "低倍率" == F ? l = a / n * i : "高倍率" == F ? l = s * i * r + (a / (n * s) - xe(s)) * s * i : "正常倍率" == F && (l = s * i * r);
                let u = l * d;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: s.toFixed(4),
                    备注: t["备注"],
                    支付标准: x.toFixed(2),
                    系数: r,
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: l.toFixed(2),
                    点值: d.toFixed(2),
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - a).toFixed(2),
                    低倍临界值: (.5 * x).toFixed(2),
                    高倍临界值: (x * xe(s)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: s.toFixed(4),
                    病组标识: t["备注"],
                    支付标准: x.toFixed(2),
                    系数: r,
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: l.toFixed(2),
                    点值: d.toFixed(2),
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - a).toFixed(2)
                }
            }(e, n, 100, d, a, x, F) : "达州" == t ? function (e, t, i, n, o, d, a, r, s) {
                let l, x = parseFloat(t["权重"]), c = t["系数-" + d] ? parseFloat(t["系数-" + d]) : a, F = o * x,
                    u = s / F,
                    D = ((e, t) => t < .4 ? "低倍率" : e <= 1 && t > 2.5 || e > 1 && e <= 2 && t > 2 || e > 2 && e <= 5 && t > 1.5 || e > 5 && t > 1.3 ? "高倍率" : "正常倍率")(x, u);
                "低倍率" == D ? l = s / o * n * c : "高倍率" == D ? l = x * n * c + (s / (o * x) - ae(x)) * x * n : "正常倍率" == D && (l = x * n * c);
                let R = l * r;
                return t["DRG名称"], e ? {
                    DRG名称: t["DRG名称"],
                    权重: x,
                    支付标准: F.toFixed(2),
                    系数: c,
                    倍率: u.toFixed(2),
                    类型: D,
                    总点数: l.toFixed(2),
                    点值: r.toFixed(2),
                    DRG费用: R.toFixed(2),
                    DRG差额: (R - s).toFixed(2),
                    低倍临界值: (.4 * F).toFixed(2),
                    高倍临界值: (F * ae(x)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: x,
                    支付标准: F.toFixed(2),
                    系数: c,
                    倍率: u.toFixed(2),
                    类型: D,
                    总点数: l.toFixed(2),
                    点值: r.toFixed(2),
                    DRG费用: R.toFixed(2),
                    DRG差额: (R - s).toFixed(2)
                }
            }(e, n, 0, 100, d, r, l, x, F) : "徐州" == t ? function (e, t, i, n, o, d) {
                let a, r = parseFloat(t["权重"]), s = t["基础病组"] ? 1 : n, l = i * r * s, x = d / l,
                    c = ((e, t) => t < .4 ? "低倍率" : e <= 1 && t > 3 || e > 1 && e <= 2 && t > 2.5 || e > 2 && e <= 3 && t > 2 || e > 3 && e <= 5 && t > 1.5 || e > 5 && t > 1.3 ? "高倍率" : "正常倍率")(r, x);
                "低倍率" == c ? a = d / i * 100 : "高倍率" == c ? a = 100 * r * s + (d / (i * r) - xe(r)) * r * 100 : "正常倍率" == c && (a = 100 * r * s);
                let F = a * o;
                return e ? {
                    DRG名称: t["DRG名称"],
                    基准点数: (100 * r).toFixed(2),
                    系数: s,
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o,
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2),
                    低倍临界值: (.4 * l).toFixed(2),
                    高倍临界值: (l * xe(r)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    系数: s,
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o,
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2)
                }
            }(e, n, d, l, x, F) : "运城" == t ? function (e, t, i, n, o) {
                let d = parseFloat(t["权重"]);
                "分级诊疗组" == t["病组类型"] && ["县二级", "市二级", "三级"].includes(i) && (n = 4996.17);
                let a, r = 1 * d * n, s = o / r, l = ee(i, s);
                return "低倍率" == l ? a = o : "高倍率" == l ? a = r * (1 + .7 * (s - K(i))) : "正常倍率" == l && (a = r), e ? {
                    DRG名称: t["DRG名称"],
                    权重: d,
                    病组标识: t["病组类型"],
                    费率: n,
                    支付标准: r.toFixed(2),
                    倍率: s.toFixed(2),
                    类型: l,
                    DRG费用: a.toFixed(2),
                    DRG差额: (a - o).toFixed(2),
                    低倍临界值: (.4 * r).toFixed(2),
                    高倍临界值: (r * K(i)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: d,
                    病组标识: t["病组类型"],
                    费率: n,
                    支付标准: r.toFixed(2),
                    倍率: s.toFixed(2),
                    类型: l,
                    DRG费用: a.toFixed(2),
                    DRG差额: (a - o).toFixed(2)
                }
            }(e, n, r, c, F) : "内江" == t ? function (e, t, i, n, o, d, a) {
                let r, s = parseFloat(t["权重"]), l = t["系数-" + o] ? parseFloat(t["系数-" + o]) : 1, x = n * s * l,
                    c = a / x,
                    F = ((e, t) => t < .5 ? "低倍率" : e <= 1 && t > 3 || e > 1 && e <= 2 && t > 2.5 || e > 2 && e <= 4 && t > 2 || e > 4 && t > 1.5 ? "高倍率" : "正常倍率")(s, c);
                "低倍率" == F ? r = a / n * i : ("高倍率" == F || "正常倍率" == F) && (r = s * i * l);
                let u = r * d;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: s.toFixed(4),
                    基础病组: t["基础病组"],
                    系数: l,
                    支付标准: (s * l * d * i).toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: r.toFixed(2),
                    点值: d.toFixed(2),
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - a).toFixed(2),
                    低倍临界值: (.5 * x).toFixed(2),
                    高倍临界值: (x * X(s)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: s.toFixed(4),
                    病组标识: t["基础病组"] ? "基础病组" : "",
                    系数: l,
                    支付标准: (s * l * d * i).toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    总点数: r.toFixed(2),
                    点值: d.toFixed(2),
                    DRG费用: u.toFixed(2),
                    DRG差额: (u - a).toFixed(2)
                }
            }(e, n, 100, d, r, x, F) : "银川" == t ? function (e, t, i, n, o, d) {
                let a, r, s = parseFloat(t["标准分值"]), l = t["基础病组标识"] ? 1 : parseFloat(t["系数-" + i]),
                    x = 3 * s * l, c = o / x,
                    F = (e => e <= .4 ? "超低异常" : e > .4 && e < .8 ? "低异常" : e >= .8 && e <= 1.4 ? "正常" : e >= 1.4 && e <= 2.5 ? "高异常" : "超高异常")(c);
                return "超低异常" == F ? r = o : "低异常" == F ? a = s * c * l : "正常" == F ? a = s * l : "高异常" == F ? a = s * (c - .4) * l : "超高异常" == F && (a = 2.1 * s * l), a || (a = r / 3), d < 6 && (a *= 1.03), r || (r = a * n), e ? {
                    DRG名称: t["DRG名称"],
                    标准分值: s.toFixed(4),
                    系数: l.toFixed(4),
                    费率: n.toFixed(2),
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    病例分值: a.toFixed(2),
                    DRG费用: r.toFixed(2),
                    DRG差额: (r - o).toFixed(2),
                    低异常临界值: (.8 * x).toFixed(2),
                    高异常临界值: (1.4 * x).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    标准分值: s.toFixed(4),
                    系数: l.toFixed(4),
                    费率: n.toFixed(2),
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    病例分值: a.toFixed(2),
                    DRG费用: r.toFixed(2),
                    DRG差额: (r - o).toFixed(2)
                }
            }(e, n, r, c, F, u) : "六安" == t ? function (e, t, i, n, o, d) {
                let a = parseFloat(t["权重"]);
                t["同等费率组"] && (o = "职工" == n ? 6239.05 : 5526.34);
                let r, s = a * o, l = d / s,
                    x = ((e, t) => t < .5 ? "低倍率" : "三级" == e && t > 3 || "三级" != e && t > 2 ? "高倍率" : "正常倍率")(i, l);
                return "低倍率" == x || "高倍率" == x ? r = d : "正常倍率" == x && (r = s), e ? {
                    DRG名称: t["DRG名称"],
                    权重: a.toFixed(4),
                    费率: o.toFixed(2),
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: r.toFixed(2),
                    DRG差额: (r - d).toFixed(2),
                    低倍临界值: (.5 * s).toFixed(2),
                    高倍临界值: (s * te(i)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: a.toFixed(4),
                    费率: o.toFixed(2),
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: r.toFixed(2),
                    DRG差额: (r - d).toFixed(2)
                }
            }(e, n, r, s, c, F) : ["长春", "长春2024"].includes(t) ? function (e, t, i, n) {
                let o, d = parseFloat(t["权重"]), a = d * i, r = n / a, s = U(r);
                return "低倍率" == s || "高倍率" == s ? o = n : "正常倍率" == s && (o = a), e ? {
                    DRG名称: t["DRG名称"],
                    权重: d,
                    费率: i,
                    支付标准: a.toFixed(2),
                    倍率: r.toFixed(2),
                    类型: s,
                    DRG费用: o.toFixed(2),
                    DRG差额: (o - n).toFixed(2),
                    低倍临界值: (.3 * a).toFixed(2),
                    高倍临界值: (3 * a).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: d,
                    费率: i,
                    支付标准: a.toFixed(2),
                    倍率: r.toFixed(2),
                    类型: s,
                    DRG费用: o.toFixed(2),
                    DRG差额: (o - n).toFixed(2)
                }
            }(e, n, c, F) : "宜宾" == t ? function (e, t, i, n, o, d) {
                let a, r = parseFloat(t["权重"]),
                    s = "基层病组" == t["病组标识"] ? 1 : t["系数-" + n] ? parseFloat(t["系数-" + n]) : {
                        长宁: 1,
                        卫生院: .72,
                        卫兴医院: .75,
                        宜宾中医院: 1.35
                    }[n], l = i * r, x = d / l, c = V(r, x);
                "低倍率" == c ? a = d / i * 100 : "高倍率" == c ? a = 100 * (d / i - (Q(r) - s) * r) : "正常倍率" == c && (a = 100 * r * s);
                let F = a * o;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: r,
                    例均费用: l.toFixed(2),
                    系数: s.toFixed(4),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2),
                    低倍临界值: (.4 * l).toFixed(2),
                    高倍临界值: (l * Q(r)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: r,
                    病组标识: t["病组标识"],
                    例均费用: l.toFixed(2),
                    系数: s.toFixed(4),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2)
                }
            }(e, n, d, a, x, F) : "乐山" == t ? function (e, t, i, n, o, d) {
                let a, r = parseFloat(t["权重"]), s = "基础病组" == t["病组标识"] ? 1 : parseFloat(t["系数-" + n]),
                    l = i * r, x = d / l,
                    c = ((e, t) => t < .35 ? "低倍率" : e <= 2 && t > 2 || e > 2 && t > 1.5 ? "高倍率" : "正常倍率")(r, x);
                "低倍率" == c ? a = d / i * 100 : "高倍率" == c ? a = 100 * r * s + (d / (i * r) - re(r)) * r * 100 : "正常倍率" == c && (a = 100 * r * s);
                let F = a * o;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    病组标识: t["病组标识"],
                    例均费用: l.toFixed(2),
                    系数: s,
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2),
                    低倍临界值: (.35 * l).toFixed(2),
                    高倍临界值: (l * re(r)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    病组标识: t["病组标识"],
                    例均费用: l.toFixed(2),
                    系数: s,
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2)
                }
            }(e, n, d, a, x, F) : "安康" == t ? function (e, t, i, n, o, d) {
                let a, r = parseFloat(t["权重"]), s = parseFloat(t["系数-" + n]), l = i * r * s, x = d / l,
                    c = ((e, t) => t < .3 ? "低倍率" : e <= 1 && t > 3 || e > 1 && e <= 2 && t > 2.5 || e > 2 && e <= 3 && t > 2 || e > 3 && e <= 5 && t > 1.5 || e > 5 && t > 1.2 ? "高倍率" : "正常倍率")(r, x);
                "低倍率" == c ? a = d / i * 100 : "高倍率" == c ? a = 100 * r * s + (d / (i * r) - ce(r)) * r * 100 : "正常倍率" == c && (a = 100 * r * s);
                let F = a * o;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    系数: s.toFixed(4),
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2),
                    低倍临界值: (.3 * l).toFixed(2),
                    高倍临界值: (l * ce(r)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    系数: s.toFixed(4),
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2)
                }
            }(e, n, d, r, x, F) : "开封" == t ? function (e, t, i, n, o, d) {
                let a, r = parseFloat(t["权重"]), s = parseFloat(t["系数-" + n]),
                    l = (i = "儿科" == t["权重"] ? i / 1.1 : i) * r, x = d / l, c = ee(r, x);
                "低倍率" == c ? a = d / i * 100 : "高倍率" == c ? a = 100 * r * s + (d / (i * r) - K(r)) * r * 100 : "正常倍率" == c && (a = 100 * r * s);
                let F = a * o;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: r,
                    系数: s,
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2),
                    低倍临界值: (.4 * l).toFixed(2),
                    高倍临界值: (l * K(r)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: r,
                    系数: s,
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2)
                }
            }(e, n, d, r, x, F) : "泉州" == t ? function (e, t, i, n, o, d, a) {
                let r, s = parseFloat(i["权重"]), l = i["基础病组标识"] && "A类" == n ? m[t]["B类"] : o, x = s * d * l,
                    c = a / x, F = (e => e <= .5 ? "低倍率" : e >= 2 ? "高倍率" : "正常倍率")(c);
                return "低倍率" == F || "高倍率" == F ? r = a : "正常倍率" == F && (r = x), e ? {
                    DRG名称: i["DRG名称"],
                    权重: s,
                    系数: l,
                    费率: d,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    DRG费用: r.toFixed(2),
                    DRG差额: (r - a).toFixed(2),
                    低倍临界值: (.5 * x).toFixed(2),
                    高倍临界值: (2 * x).toFixed(2)
                } : {
                    DRG名称: i["DRG名称"],
                    权重: s,
                    系数: l,
                    费率: d,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    DRG费用: r.toFixed(2),
                    DRG差额: (r - a).toFixed(2)
                }
            }(e, t, n, r, l, c, F) : "合肥" == t ? function (e, t, i, n, o, d) {
                let a, r = t["权重"] ? parseFloat(t["权重"]) : 1, s = t["系数-" + n] ? parseFloat(t["系数-" + n]) : 1,
                    l = i * r, x = d / l, c = ee(r, x);
                "低倍率" == c ? a = d / i * 100 : "高倍率" == c ? a = 100 * r * s + (d / (i * r) - (r >= 4 ? 1.5 : 2)) * r * 100 : "正常倍率" == c && (a = 100 * r * s);
                let F = a * o;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: r,
                    系数: s,
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2),
                    低倍临界值: (.825 * l).toFixed(2),
                    高倍临界值: (l * K(r)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: r,
                    系数: s,
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2)
                }
            }(e, n, d, a, x, F) : "苏州" == t ? function (e, t, i, n, o, d) {
                let a, r = parseFloat(t["权重"]), s = "是" == t["基础病组"] ? 1.02 : n, l = r * o * s, x = d / l,
                    c = ie(i, x);
                return "低倍率" == c || "高倍率" == c ? a = d : "正常倍率" == c && (a = l), e ? {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    基础病组: t["基础病组"],
                    系数: s,
                    费率: o,
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    DRG费用: a.toFixed(2),
                    DRG差额: (a - d).toFixed(2),
                    低倍临界值: (.4 * l).toFixed(2),
                    高倍临界值: (l * te(i)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    病组标识: t["基础病组"] ? "基础病组" : "",
                    系数: s,
                    费率: o,
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    DRG费用: a.toFixed(2),
                    DRG差额: (a - d).toFixed(2)
                }
            }(e, n, r, l, c, F) : "济南" == t ? function (e, t, i, n, o, d) {
                let a = parseFloat(i["权重"]);
                "2023" == i["基础病组"] ? o = O[t]["D类"][n] : "2024" == i["基础病组"] && "职工" == n && (o = O[t]["C类"]["职工"]);
                let r, s = a * o, l = d / s, x = Fe(a, l);
                return "低倍率" == x || "高倍率" == x ? r = d : "正常倍率" == x && (r = s), e ? {
                    DRG名称: i["DRG名称"],
                    权重: a.toFixed(4),
                    基础病组: i["基础病组"] ? "是" : "",
                    费率: o,
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: r.toFixed(2),
                    DRG差额: (r - d).toFixed(2),
                    低倍临界值: (.3 * s).toFixed(2),
                    高倍临界值: (s * xe(a)).toFixed(2)
                } : {
                    DRG名称: i["DRG名称"],
                    权重: a.toFixed(4),
                    病组标识: i["基础病组"] ? "是" : "",
                    费率: o,
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: x,
                    DRG费用: r.toFixed(2),
                    DRG差额: (r - d).toFixed(2)
                }
            }(e, t, n, s, c, F) : "临沂" == t ? function (e, t, i, n, o, d, a) {
                let r, s = parseFloat(t["权重"]), l = (n = t["备注"].includes("基础病组") ? 1 : n) * a, x = s * o * l,
                    c = d / x,
                    F = ((e, t, i) => i < .4 ? "低倍率" : "一级" == e && i > 1.8 || "一级" == e && i > 1.7 && t > 3 || "二级" == e && i > 2 || "二级" == e && i > 1.9 && t > 4 || "三级" == e && i > 3 || "三级" == e && i > 2.3 && t > 5 ? "高倍率" : "正常倍率")(i, s, c);
                return "低倍率" == F || "高倍率" == F ? r = d : "正常倍率" == F && (r = x), e ? {
                    DRG名称: t["DRG名称"],
                    权重: s,
                    备注: t["备注"],
                    等级系数: n.toFixed(2),
                    加权系数: a.toFixed(2),
                    费率: o.toFixed(2),
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    DRG费用: r.toFixed(2),
                    DRG差额: (r - d).toFixed(2),
                    低倍临界值: (.4 * x).toFixed(2),
                    高倍临界值: (x * se(i, s)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: s,
                    病组标识: t["备注"],
                    系数: l.toFixed(2),
                    费率: o.toFixed(2),
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: F,
                    DRG费用: r.toFixed(2),
                    DRG差额: (r - d).toFixed(2)
                }
            }(e, n, r, l, c, F, h) : "周口" == t ? function (e, t, i, n, o, d) {
                let a, r = parseFloat(t["权重"]);
                if (t.hasOwnProperty("系数_" + n)) a = parseFloat(t["系数_" + n]); else {
                    if (!t["基础病组标识"]) return {
                        DRG名称: t["DRG名称"],
                        权重: r.toFixed(4),
                        基础病组: t["基础病组标识"],
                        系数: "未知"
                    };
                    a = "乡级" == n ? .8 : "一级" == n ? .9 : 1
                }
                let s, l = r * i * a, x = d / l,
                    c = ((e, t) => t < .35 ? "低倍率" : e <= 1 && t > 3 || e > 1 && e <= 2 && t > 2.5 || e > 2 && e <= 3 && t > 2 || e > 3 && e <= 5 && t > 1.5 || e > 5 && t > 1.3 ? "高倍率" : "正常倍率")(r, x);
                "低倍率" == c ? s = d / i * 100 : "高倍率" == c ? s = 100 * (d / i - (xe(r) - 1) * r * a) : "正常倍率" == c && (s = r * a * 100);
                let F = s * o,
                    u = t["基础病组标识"] && t["非稳定病组标识"] ? "基础病组 非稳定病组" : t["基础病组标识"] ? "基础病组" : t["非稳定病组标识"] ? "非稳定病组" : "";
                return e ? {
                    DRG名称: t["DRG名称"],
                    病组标识: u,
                    权重: r,
                    系数: a.toFixed(4),
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: s.toFixed(2),
                    点值: o,
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2),
                    低倍临界值: (.35 * l).toFixed(2),
                    高倍临界值: (l * xe(r)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    病组标识: u,
                    权重: r,
                    系数: a.toFixed(4),
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: s.toFixed(2),
                    点值: o,
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2)
                }
            }(e, n, d, r, x, F) : "庆阳" == t ? function (e, t, i, n, o, d) {
                let a, r = parseFloat(t["权重"]), s = n, l = r * s * o, x = d / l,
                    c = ((e, t) => t < .4 ? "低倍率" : "三级" == e && t > 2.5 || "三级" != e && t > 2 ? "高倍率" : "正常倍率")(i, x);
                return "低倍率" == c ? a = d : "高倍率" == c ? a = l * (1 + .7 * (x - le(i))) : "正常倍率" == c && (a = l), e ? {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    系数: s.toFixed(4),
                    费率: o.toFixed(2),
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    DRG费用: a.toFixed(2),
                    DRG差额: (a - d).toFixed(2),
                    低倍临界值: (.4 * l).toFixed(2),
                    高倍临界值: (l * le(i)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    系数: s.toFixed(4),
                    费率: o.toFixed(2),
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    DRG费用: a.toFixed(2),
                    DRG差额: (a - d).toFixed(2)
                }
            }(e, n, r, l, c, F) : "哈尔滨" == t ? function (e, t, i, n, o, d) {
                let a = parseFloat(t["权重"]);
                "基层病组" == t["病组类型"] && (o = "职工" == n ? 11352.45 : 8940.28);
                let r, s = a * o, l = d / s, x = ["哈尔滨奥兰医院"].includes(i) ? "三级" : "二级", c = ie(x, l);
                return "低倍率" == c ? r = d : "高倍率" == c ? r = s * (1.7 + .5 * (l - te(x))) : "正常倍率" == c && (r = s), e ? {
                    DRG名称: t["DRG编码"] + "-" + t["DRG名称"].split("，").slice(-1) + (t["病组类型"] ? "[" + t["病组类型"] + "]" : ""),
                    权重: a.toFixed(4),
                    费率: o.toFixed(2),
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: c,
                    DRG费用: r.toFixed(2),
                    DRG差额: (r - d).toFixed(2),
                    低倍临界值: (.4 * s).toFixed(2),
                    高倍临界值: (s * te(x)).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: a.toFixed(4),
                    费率: o.toFixed(2),
                    支付标准: s.toFixed(2),
                    倍率: l.toFixed(2),
                    类型: c,
                    DRG费用: r.toFixed(2),
                    DRG差额: (r - d).toFixed(2)
                }
            }(e, n, o, s, c, F) : "南昌" == t ? function (e, t, i, n, o, d) {
                let a, r = parseFloat(t["权重"]), s = parseFloat(t["系数-" + n]), l = i * r * s, x = d / l,
                    c = (e => e < .8 ? "低倍率" : e > 1.5 ? "高倍率" : "正常倍率")(x);
                "低倍率" == c ? a = d / i * 1e3 : "高倍率" == c ? a = 1e3 * r * s + (d / (i * r) - 1.5) * r * 1e3 : "正常倍率" == c && (a = 1e3 * r * s);
                let F = a * o;
                return e ? {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    系数: s.toFixed(4),
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2),
                    低倍临界值: (.825 * l).toFixed(2),
                    高倍临界值: (1.5 * l).toFixed(2),
                    次均统筹: t["次均统筹"],
                    手术费占比: t["手术费占比"],
                    药占比: t["药占比"]
                } : {
                    DRG名称: t["DRG名称"],
                    权重: r.toFixed(4),
                    系数: s.toFixed(4),
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: c,
                    总点数: a.toFixed(2),
                    点值: o.toFixed(2),
                    DRG费用: F.toFixed(2),
                    DRG差额: (F - d).toFixed(2)
                }
            }(e, n, d, a, x, F) : ["玉林", "防城港", "来宾", "桂林", "贵港", "南宁", "崇左", "河池", "北海", "百色"].includes(t) ? function (e, t, i, n, o, d, a, r) {
                let s, l = parseFloat(i["权重"]),
                    x = i["基础病组"] ? "贵港" == t ? .806 : "玉林" == t ? .8 : "百色" == t ? .75 : 1 : d,
                    c = o * l * x, F = r / c, u = Fe(l, F);
                "低倍率" == u ? s = r / o * 100 : "高倍率" == u ? s = 100 * (r / o - (xe(l) - 1) * l * x) : "正常倍率" == u && (s = l * x * 100);
                let D = s * a;
                return e ? {
                    DRG名称: i["DRG名称"],
                    权重: l.toFixed(4),
                    基础病组: i["基础病组"],
                    系数: x,
                    支付标准: c.toFixed(2),
                    倍率: F.toFixed(2),
                    类型: u,
                    总点数: s.toFixed(2),
                    点值: a,
                    DRG费用: D.toFixed(2),
                    DRG差额: (D - r).toFixed(2),
                    低倍临界值: (.3 * c).toFixed(2),
                    高倍临界值: (c * xe(l)).toFixed(2)
                } : {
                    DRG名称: i["DRG名称"],
                    权重: l.toFixed(4),
                    病组标识: i["基础病组"] ? "基础病组" : "",
                    系数: x,
                    支付标准: c.toFixed(2),
                    倍率: F.toFixed(2),
                    类型: u,
                    总点数: s.toFixed(2),
                    点值: a,
                    DRG费用: D.toFixed(2),
                    DRG差额: (D - r).toFixed(2)
                }
            }(e, t, n, 0, d, l, x, F) : ["曲靖", "红河", "西双版纳"].includes(t) ? function (e, t, i, n) {
                let o, d = parseFloat(t["权重"]), a = d * i, r = n / a, s = U(r);
                return "" == t["权重"] || "低倍率" == s ? o = n : ("高倍率" == s || "正常倍率" == s) && (o = a), e ? {
                    DRG名称: t["DRG名称"],
                    权重: d,
                    费率: i,
                    支付标准: a.toFixed(2),
                    倍率: r.toFixed(2),
                    类型: s,
                    DRG费用: o.toFixed(2),
                    DRG差额: (o - n).toFixed(2),
                    低倍临界值: (.3 * a).toFixed(2),
                    高倍临界值: (3 * a).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: d,
                    费率: i,
                    支付标准: a.toFixed(2),
                    倍率: r.toFixed(2),
                    类型: s,
                    DRG费用: o.toFixed(2),
                    DRG差额: (o - n).toFixed(2)
                }
            }(e, n, c, F) : "大理" == t ? function (e, t, i, n, o, d, a) {
                let r = parseFloat(t["权重"]);
                ["一类"].includes(i) && t["基层病组"].includes("大理") && (n = (.9 * n).toFixed(0));
                let s, l, x = r * n, c = o / x, F = !t["DRG编码"].startsWith("00") && !t["DRG编码"].endsWith("QY");
                return s = d >= 60 ? "按项目付费（60天）" : F && "一类" != i && c >= 2 || "一类" == i && c >= 3 ? "按项目付费（高倍）" : F && c <= .4 ? "按项目付费（低倍）" : F && "4" == a && c > .4 && c <= .6 ? "按支付标准70%" : F && d > 20 && c > 1 ? "按支付标准110%" : "按支付标准", l = "按支付标准" == s ? x : "按支付标准70%" == s ? .7 * x : "按支付标准110%" == s ? 1.1 * x : o, e ? {
                    DRG名称: t["DRG名称"],
                    权重: r,
                    费率: n,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: s,
                    DRG费用: l.toFixed(2),
                    DRG差额: (l - o).toFixed(2),
                    低倍临界值: (.3 * x).toFixed(2),
                    高倍临界值: (3 * x).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: r,
                    费率: n,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: s,
                    DRG费用: l.toFixed(2),
                    DRG差额: (l - o).toFixed(2)
                }
            }(e, n, r, c, F, D, R) : "迪庆" == t ? function (e, t, i, n, o, d, a) {
                let r, s, l = parseFloat(t["权重"]), x = l * n, c = o / x,
                    F = !t["DRG编码"].startsWith("00") && !t["DRG编码"].endsWith("QY");
                return r = d >= 60 ? "按项目付费（60天）" : F && c >= 3 ? "按项目付费（高倍）" : F && c <= .3 ? "按项目付费（低倍）" : "2" == a && d <= 3 ? "按项目付费（3日内转院）" : "按支付标准", s = "按支付标准" == r ? x : o, e ? {
                    DRG名称: t["DRG名称"],
                    权重: l,
                    费率: n,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: r,
                    DRG费用: s.toFixed(2),
                    DRG差额: (s - o).toFixed(2),
                    低倍临界值: (.3 * x).toFixed(2),
                    高倍临界值: (3 * x).toFixed(2)
                } : {
                    DRG名称: t["DRG名称"],
                    权重: l,
                    费率: n,
                    支付标准: x.toFixed(2),
                    倍率: c.toFixed(2),
                    类型: r,
                    DRG费用: s.toFixed(2),
                    DRG差额: (s - o).toFixed(2)
                }
            }(e, n, 0, c, F, D, R) : "楚雄" == t ? function (e, t, i, n, o, d) {
                let a, r, s = parseFloat(t["权重"]), l = s * i, x = n / l;
                return a = "5" == d ? "按项目付费（死亡）" : "2" == d && o <= 3 ? "按项目付费（3日内转院）" : "按DRG付费", r = "按DRG付费" == a ? l : n, {
                    DRG名称: t["DRG名称"],
                    权重: s,
                    费率: i,
                    支付标准: l.toFixed(2),
                    倍率: x.toFixed(2),
                    类型: a,
                    DRG费用: r.toFixed(2),
                    DRG差额: (r - n).toFixed(2)
                }
            }(0, n, c, F, D, R) : "保山" == t ? function (e, t, i, n, o, d, a, r) {
                let s = parseFloat(i["权重"]);
                i["基层病组"].includes(t) && (o = O[t]["四档"]);
                let l, x, c = s * o, F = d / c, u = !i["DRG编码"].startsWith("00");
                return l = a >= 60 ? "按项目付费（60天）" : u && (["一档", "二档"].includes(n) && F >= 3 || !["一档", "二档"].includes(n) && F >= 2) ? "按项目付费（高倍）" : u && F <= .3 ? "按项目付费（低倍）" : "5" == r ? "按项目付费（死亡）" : "2" == r && a <= 3 ? "按项目付费（3日内转院）" : "按DRG付费", x = "按DRG付费" == l ? c : d, e ? {
                    DRG名称: i["DRG名称"],
                    权重: s,
                    基层病组: i["基层病组"].includes(t) ? "是" : "",
                    费率: o,
                    支付标准: c.toFixed(2),
                    倍率: F.toFixed(2),
                    类型: l,
                    DRG费用: x.toFixed(2),
                    DRG差额: (x - d).toFixed(2),
                    低倍临界值: (.3 * c).toFixed(2),
                    高倍临界值: (c * te(n)).toFixed(2)
                } : {
                    DRG名称: i["DRG名称"],
                    权重: s,
                    病组标识: i["基层病组"].includes(t) ? "基层病组" : "",
                    费率: o,
                    支付标准: c.toFixed(2),
                    倍率: F.toFixed(2),
                    类型: l,
                    DRG费用: x.toFixed(2),
                    DRG差额: (x - d).toFixed(2)
                }
            }(e, t, n, r, c, F, D, R) : ["普洱", "临沧"].includes(t) ? function (e, t, i, n, o, d, a, r) {
                let s = parseFloat(i["权重"]);
                ["A档"].includes(n) && i["基层病组"].includes(t) && (o = (.9 * o).toFixed(0));
                let l, x, c = s * o, F = d / c, u = !i["DRG编码"].startsWith("00");
                return l = a >= 60 ? "按项目付费（60天）" : u && s >= 4 && F >= 2 && s < 4 && F >= 3 ? "按项目付费（高倍）" : u && F <= .3 ? "按项目付费（低倍）" : u && "4" == r && F > .3 && F <= .5 ? "按支付标准70%" : u && a > 20 && F > 1 ? "按支付标准110%" : "按支付标准", x = "按支付标准" == l ? c : "按支付标准70%" == l ? .7 * c : "按支付标准110%" == l ? 1.1 * c : d, e ? {
                    DRG名称: i["DRG名称"],
                    权重: s,
                    基层病组: i["基层病组"].includes(t) ? "是" : "",
                    费率: o,
                    支付标准: c.toFixed(2),
                    倍率: F.toFixed(2),
                    类型: l,
                    DRG费用: x.toFixed(2),
                    DRG差额: (x - d).toFixed(2),
                    低倍临界值: (.3 * c).toFixed(2),
                    高倍临界值: (3 * c).toFixed(2)
                } : {
                    DRG名称: i["DRG名称"],
                    权重: s,
                    病组标识: i["基层病组"].includes(t) ? "基层病组" : "",
                    费率: o,
                    支付标准: c.toFixed(2),
                    倍率: F.toFixed(2),
                    类型: l,
                    DRG费用: x.toFixed(2),
                    DRG差额: (x - d).toFixed(2)
                }
            }(e, t, n, r, c, F, D, R) : "昆明" == t ? function (e, t, i, n, o, d, a, r) {
                let s = parseFloat(i["权重"]);
                ["一档", "二档"].includes(n) && i["基层病组"].includes(t) && (o = (.9 * o).toFixed(0));
                let l, x, c = s * o, F = d / c, u = !i["DRG编码"].startsWith("00");
                return l = a >= 60 ? "按项目付费（60天）" : u && s >= 4 && F >= 2 && s < 4 && F >= 3 ? "按项目付费（高倍）" : u && F <= .3 ? "按项目付费（低倍）" : u && "4" == r && F > .3 && F <= .5 ? "按支付标准70%" : u && a > 20 && F > 1 ? "按支付标准110%" : "按支付标准", x = "按支付标准" == l ? c : "按支付标准70%" == l ? .7 * c : "按支付标准110%" == l ? 1.1 * c : d, e ? {
                    DRG名称: i["DRG名称"],
                    权重: s,
                    基层病组: i["基层病组"].includes(t) ? "是" : "",
                    费率: o,
                    支付标准: c.toFixed(2),
                    倍率: F.toFixed(2),
                    类型: l,
                    DRG费用: x.toFixed(2),
                    DRG差额: (x - d).toFixed(2),
                    低倍临界值: (.3 * c).toFixed(2),
                    高倍临界值: (3 * c).toFixed(2)
                } : {
                    DRG名称: i["DRG名称"],
                    权重: s,
                    病组标识: i["基层病组"].includes(t) ? "基层病组" : "",
                    费率: o,
                    支付标准: c.toFixed(2),
                    倍率: F.toFixed(2),
                    类型: l,
                    DRG费用: x.toFixed(2),
                    DRG差额: (x - d).toFixed(2)
                }
            }(e, t, n, r, c, F, D, R) : ["杭州", "嘉兴", "金华", "宁波", "丽水", "台州", "衢州"].includes(t) ? function (e, t, i, n, o, d, a) {
                let r, s, l, x = parseFloat(i["权重"]),
                    c = i["基础病组"] ? 1 : i["系数-" + o] ? parseFloat(i["系数-" + o]) : 1, F = n * x * c, u = a / F;
                "嘉兴" == t ? r = (e => e < .4 ? "低倍率" : e >= 2 ? "高倍率" : "正常倍率")(u) : "金华" == t ? r = ee(x, u) : ["杭州", "宁波", "丽水"].includes(t) ? r = V(x, u) : "台州" == t ? r = ((e, t) => t < .5 ? "低倍率" : e <= 1 && t > 2.5 || e > 1 && e <= 2 && t > 2 || e > 2 && t > 1.8 ? "高倍率" : "正常倍率")(x, u) : "衢州" == t && (r = ((e, t) => t < .35 ? "低倍率" : e <= 1 && t > 3 || e > 1 && e <= 2 && t > 2.5 || e > 2 && t > 2 ? "高倍率" : "正常倍率")(x, u)), "嘉兴" == t ? l = 2 : "金华" == t ? l = K(x) : ["杭州", "宁波", "丽水"].includes(t) ? l = Q(x) : "台州" == t ? l = (e => e <= 1 ? 2.5 : e > 1 && e <= 2 ? 2 : 1.8)(x) : "衢州" == t && (l = K(x)), "低倍率" == r ? s = a / n * 100 : "高倍率" == r ? s = 100 * x * c + (a / (n * x) - l) * x * 100 : "正常倍率" == r && (s = 100 * x * c);
                let D = s * d;
                return e ? {
                    DRG名称: i["DRG编码"] + "-" + i["DRG名称"].split("，").slice(-1),
                    权重: x.toFixed(4),
                    系数: c.toFixed(4),
                    支付标准: F.toFixed(2),
                    倍率: u.toFixed(2),
                    类型: r,
                    总点数: s.toFixed(2),
                    点值: d.toFixed(2),
                    DRG费用: D.toFixed(2),
                    DRG差额: (D - a).toFixed(2),
                    低倍临界值: (.4 * F).toFixed(2),
                    高倍临界值: (F * l).toFixed(2)
                } : {
                    DRG名称: i["DRG名称"],
                    权重: x.toFixed(4),
                    系数: c.toFixed(4),
                    支付标准: F.toFixed(2),
                    倍率: u.toFixed(2),
                    类型: r,
                    总点数: s.toFixed(2),
                    点值: d.toFixed(2),
                    DRG费用: D.toFixed(2),
                    DRG差额: (D - a).toFixed(2)
                }
            }(e, t, n, d, a, x, F) : n : n : "0000" == i ? {DRG名称: "未入组"} : i.endsWith("QY") ? {DRG名称: i + "歧义组"} : {DRG名称: "未知名称"}
        }

        const fe = ["changchun_2024", "linyi_2024", "taiyuan_2024", "guangan_2023", "guangan_2024", "fuzhou_2023", "handan_2024", "shenyang_2024", "tongren_2024", "chongqing_2024","chongqing_2025", "zhejiang_2022", "yunnan_2023", "xianyang_2024"],
            _e = Tt(window.location.href).type ? Tt(window.location.href).type : "chs_drg_20",
            ve = Tt(window.location.href).hospital ? Tt(window.location.href).hospital : "",
            ge = Ht(document.body, "", "div-table"), be = Ht(ge, "", "div-full"), ye = function (e, t, i, n, o) {
                const d = document.createElement("input");
                d.type = "checkbox", d.checked = "";
                const a = document.createTextNode("使用ICD医保码"), r = document.createElement("div");
                return o && o.split(";").forEach((e => r.classList.add(e))), r.appendChild(d), r.appendChild(a), e.appendChild(r), d
            }(be, 0, 0, 0, "div-inline");
        const we = Ht(be, "", "red"), Ee = Ht(ge, "", "div-full"), me = It(Ee, "input", "病案号", "input-long;in_no"),
            je = It(Ee, "select", "性别", "input-short;sex"), Oe = It(Ee, "input", "年龄", "input-short;age"),
            Le = It(Ee, "input", "年龄天", "input-short;age_day"), Ce = It(Ee, "input", "出生体重", "input-short"),
            ze = It(Ee, "input", "科室", "input-long"), ke = It(Ee, "input", "住院天数", "input-short"),
            Te = It(Ee, "select", "离院方式", "input-long"), Se = It(Ee, "input", "ICU时长", "input-short"),
            Ae = It(Ee, "select", "重症监护", "input-short"), Me = Ht(Ee, "", "margin-top-6;div-top"),
            Pe = Ht(Me, "", "div-icd-code"), qe = It(Pe, "textarea", "诊断编码(医保版)", "icd-code;zd_code", ""),
            Ze = It(Pe, "input", "", "input-icd-code", "div-icd-code"), Ne = Ht(Me, "", "div-icd-desc"),
            He = Ht(Ne, "诊断描述", "div-block"), Ie = Ht(Ne, "", "div-icd-desc-inside"),
            Ue = (Ht(Ne, "输入ICD编码或名称查找诊断", "div-block;margin-top-6;blue;div-icd-desc-hide"), Ht(Me, "", "div-search-desc")),
            We = Ht(Ee, "", "margin-top-6;div-top"), Ke = Ht(We, "", "div-icd-code"),
            Be = It(Ke, "textarea", "手术操作(医保版)", "icd-code;ss_code", ""),
            Je = It(Ke, "input", "", "input-icd-code", "div-icd-code"), Ye = Ht(We, "", "div-icd-desc"),
            Qe = Ht(Ye, "手术操作描述", "div-block"), Ve = Ht(Ye, "", "div-icd-desc-inside;ss_desc"),
            Xe = (Ht(Ye, "输入ICD编码或名称查找手术操作", "div-block;margin-top-6;blue;div-icd-desc-hide"), Ht(We, "", "div-block"));
        Object.keys(p).forEach((e => je.add(new Option(p[e], e)))), Object.keys(f).forEach((e => Te.add(new Option(f[e], e)))), Object.keys(G).forEach((e => Ae.add(new Option(G[e], e))));
        const $e = Ht(ge, "", "div-full"), et = Ht($e, "", "div-inline-block"),
            tt = It(et, "select", "分组方案", "", "group");
        Object.keys(v).forEach((e => tt.add(new Option(v[e], e, null, _e.split(",").includes(e)))));
        const it = Ut(et, "DRG分组", "button", "drgBtn"), nt = Ut(et, "刷新", "", ""),
            ot = Ut(He, "↑", "button-arrow", "right"), dt = Ut(He, "↓", "button-arrow", "right"),
            at = Ut(Qe, "↑", "button-arrow", "right"), rt = Ut(Qe, "↓", "button-arrow", "right"),
            st = Ht(et, "", "div-margin-left;red"), lt = Ht($e, "", "div-inline-block"),
            xt = It(lt, "select", "区域", "", ""), ct = It(lt, "select", "等级", "", ""),
            Ft = It(lt, "input", "系数", "input-calc-short", ""), ut = It(lt, "select", "医院", "", ""),
            Dt = It(lt, "input", "调节系数", "input-calc-short", ""), Rt = It(lt, "select", "医保类型", "", ""),
            ht = It(lt, "input", "点值", "input-calc-middle", ""),
            Gt = It(lt, "input", "费率", "input-calc-middle", ""),
            pt = It(lt, "input", "住院费用", "input-calc-long", ""),
            ft = It(lt, "input", "统筹费用", "input-calc-long", ""), _t = Ut(lt, "费用测算", "button", "amt_cal"),
            vt = Ht(ge, "", "div-full;background;hide"), gt = Ht(vt, "", "div-block"), bt = Ht(vt, "", "div-code"),
            yt = Ht(vt, "", "div-code"), wt = Ht(vt, "", "div-block");
        ye.addEventListener("click", (e => (e.target.checked ? (Pe.querySelector("label").textContent = "诊断编码(医保版)", Ke.querySelector("label").textContent = "手术操作(医保版)") : (Pe.querySelector("label").textContent = "诊断编码(国临版)", Ke.querySelector("label").textContent = "手术操作(国临版)"), qe.dispatchEvent(new Event("input")), void Be.dispatchEvent(new Event("input"))))), ot.addEventListener("click", (() => {
            if (!qe.value) return;
            let e = qe.value.split("\n");
             0 !== e.length && (qe.value = [...e.slice(1), e[0]].join("\n"), qe.dispatchEvent(new Event("input")))
        })), dt.addEventListener("click", (() => {
            if (!qe.value) return;
            let e = qe.value.split("\n");
             0 != e.length && (qe.value = [e[e.length - 1], ...e.slice(0, e.length - 1)].join("\n"), qe.dispatchEvent(new Event("input")))
        })), at.addEventListener("click", (() => {
            if (!Be.value) return;
            let e = Be.value.split("\n");
             0 != e.length && (Be.value = [...e.slice(1), e[0]].join("\n"), Be.dispatchEvent(new Event("input")))
        })), rt.addEventListener("click", () => {
            if (!Be.value) return;
            let e = Be.value.split("\n");
             0 != e.length && (Be.value = [e[e.length - 1], ...e.slice(0, e.length - 1)].join("\n"), Be.dispatchEvent(new Event("input")))
        }), it.addEventListener("click", (async function () {
            let e = function () {
                if ("" === Oe.value) return Oe.focus(), "年龄必须输入";
                if (isNaN(parseInt(Oe.value))) return Oe.focus(), "年龄必须是有效的数字";
                if ("0" === Oe.value) {
                    if ("" === Le.value) return Le.focus(), "新生儿的年龄天必须输入";
                    if (isNaN(parseInt(Le.value))) return Le.focus(), "新生儿的年龄天必须是有效的数字";
                    if ("" === Ce.value) return Ce.focus(), "新生儿的出生体重必须输入";
                    if (isNaN(parseInt(Ce.value))) return Ce.focus(), "新生儿的出生体重必须是有效的数字"
                }
                return "" === ke.value ? (ke.focus(), "住院天数必须输入") : isNaN(parseInt(ke.value)) ? (ke.focus(), "住院天数必须是有效的数字") : "" === qe.value ? (Ze.focus(), "诊断代码必须输入") : void 0
            }();
            if (e) return void (st.innerHTML = e);
            st.innerHTML = "", vt.querySelectorAll("div").forEach((e => e.innerText = ""));
            let t = {
                Index: me.value.trim(),
                gender: je.value,
                age: parseInt(Oe.value),
                ageDay: Le.value.trim(),
                weight: Ce.value.trim(),
                dept: ze.value.trim(),
                inHospitalTime: parseInt(ke.value),
                leavingType: Te.value,
                zdList: qe.value.trim().split("\n").filter((e => "" != e)),
                ssList: Be.value.trim().split("\n").filter((e => "" != e))
            };
            "foshan_2022" == At() ? t.icuHours = Se.value.trim() : "sichuan_2024" == At() && (t.icu = Ae.value);
            let n = (ye.checked ? Jt(H(t)) : async function (e) {
                const t = await i.e(7439).then(i.t.bind(i, 7439, 23)), n = await i.e(4633).then(i.t.bind(i, 4633, 23));
                let o = {}, d = {};
                Object.values(t.default).reduce(((e, t, i) => o[t[0]] = t[1]), {}), Object.values(n.default).reduce(((e, t, i) => d[t[0]] = t[1]), {});
                let a = [];
                for (let t in e.zdList) if (e.zdList[t] in o) {
                    let i = o[e.zdList[t]];
                    a.push(e.zdList[t] + "转为医保码" + i), e.zdList[t] = i
                }
                for (let t in e.ssList) if (e.ssList[t] in d) {
                    let i = d[e.ssList[t]];
                    a.push(e.ssList[t] + "转为医保码" + i), e.ssList[t] = i
                }
                return e.mapMessages = a, e
            }(H(t)).then(Jt)).then((e => (e.forEach((([e, i]) => {
                1 == e || 4 == e ? t.qcZdList.splice(t.qcZdList.indexOf(i), 1) : 2 != e && 3 != e && 5 != e || t.qcSsList.splice(t.qcSsList.indexOf(i), 1), t.qcMessages.push(i + " " + {
                    1: "诊断代码是灰码",
                    2: "手术操作代码是灰码",
                    3: "手术操作在质控排除清单中",
                    4: "诊断代码不作为分组规则",
                    5: "手术操作代码不作为分组规则"
                }[e])
            })), t)));
            n = n.then(Yt).then(Qt), ["12", "20"].includes(Mt()) && (n = n.then(Kt).then(Bt)), n = n.then(Vt).then(Xt), b.hasOwnProperty(tt.value) || n.then((async e => {
                let t = await i(2646)("./" + St() + ".csv");
                return [t[0].slice(2), Object.values(t.default).slice(1).find((t => t[0] == e.drg))?.slice(2) ?? []]
            })).then((e => wt.innerHTML = $t(e)))

        })),
            _t.addEventListener("click", (async function () {
                let e = function () {

                    if (Object.keys(m).includes(xt.value)) {
                        if ("" === Ft.value) return Ft.focus(), "系数必须输入";
                        if (!parseFloat(Ft.value)) return Ft.focus(), "系数必须是数字"
                    }
                    if (j.hasOwnProperty(xt.value)) {
                        if ("" === ht.value) return ht.focus(), "点值必须输入";
                        if (!parseFloat(ht.value)) return ht.focus(), "点值必须是数字"
                    } else {
                        if ("" === Gt.value) return Gt.focus(), "费率必须输入";
                        if (!parseFloat(Gt.value)) return Gt.focus(), "费率必须是数字"
                    }
                    return "" === pt.value ? (pt.focus(), "总费用必须输入") : parseFloat(pt.value) ? "dingzhou_2023" == St() && "" === ft.value ? (ft.focus(), "统筹费用必须输入") : "" == bt.innerText || "0000" == bt.innerText ? (it.focus(), "请先分组，并且入组成功后再进行费用测算") : void 0 : (pt.focus(), "总费用必须是数字")
                }();
                if (e) return void (st.innerHTML = e);
                st.innerHTML = "";
                let t, n = bt.innerText, o = await i(2646)("./" + St() + ".csv");
                if (["haerbin_2024", "zhejiang_2022"].includes(At())) {
                    let e = Object.values(o.default).slice(1).slice(0, -1).filter((e => e[0].startsWith(n.substring(0, 3))));
                    if (0 == e.length) return void (wt.innerHTML = `<font color="red">病组${n}缺少医保支付规则</font>`);
                    let i = e.map((e => {
                        let t = {};
                        return e && e.length > 2 && o[0].forEach(((i, n) => t[i] = e[n])), pe(!0, xt.value, e[0], t, ve, kt(), ut.value, ct.value, Rt.value, parseFloat(Ft.value), parseFloat(ht.value), parseFloat(Gt.value), parseFloat(pt.value), parseInt(Oe.value), parseInt(ke.value), Te.value, parseFloat(Dt.value), parseFloat(ft.value))
                    }));
                    i && 1 == i.length ? t = i[0] : i && i.length > 1 && (t = [Object.keys(i[0]), ...i.map((e => Object.values(e).map((t => e["DRG名称"].startsWith(n) ? '<font color="red">' + t + "</font>" : t))))])
                } else {
                    let e = Object.values(o.default).slice(1).slice(0, -1).find((e => e[0] == n));
                    if (!e) return void (wt.innerHTML = `<font color="red">病组${n}缺少医保支付规则</font>`);
                    let i = {};
                    e && e.length > 2 && o[0].forEach(((t, n) => i[t] = e[n])), t = pe(!0, xt.value, n, i, ve, kt(), ut.value, ct.value, Rt.value, parseFloat(Ft.value), parseFloat(ht.value), parseFloat(Gt.value), parseFloat(pt.value), parseInt(Oe.value), parseInt(ke.value), Te.value, parseFloat(Dt.value), parseFloat(ft.value))
                }
                wt.innerHTML = $t(t)
            })), nt.addEventListener("click", (() => {
            localStorage.setItem("load", "1"), location.reload()
        })), qe.addEventListener("input", (e => {
            let t = e.target.value.split(/[, :;，：；\n\s]/).map((e => e ? e[0].toUpperCase() + e.substring(1) : ""));
            e.target.value = t.join("\n"), e.target.style.height = "20px", e.target.style.height = e.target.scrollHeight + "px", Ie.style.height = e.target.style.height, e.target.value.trim() ? async function (e) {
                const t = await i.e(7439).then(i.t.bind(i, 7439, 23)),
                    n = await i(9481)("./" + Pt(ye.checked) + ".csv"),
                    o = await i.e(8129).then(i.t.bind(i, 8129, 19)), {
                        CCE: d,
                        MCC: a,
                        CC: r
                    } = await i(5364)("./" + At() + ".js");
                let s;
                return e.map(((e, i) => {
                    let l = Object.values(n.default).find((t => t[0] == e)), x = l ? l[1] : "无效编码", c = e;
                    if (!ye.checked) {
                        let i = Object.values(t.default).find((t => t[0] == e));
                        c = i ? i[1] : e
                    }
                    return Object.values(o.default).includes(c) && (x = '<font color="red">灰码</font> <font color="gray">' + x + "</font>"), ["zhejiang_2022", "sichuan_2022"].includes(At()) ? a.hasOwnProperty(c) ? '<font color="red">MCC</font> ' + x : r.hasOwnProperty(c) ? '<font color="red">CC</font> ' + x : x : 0 == i ? d.hasOwnProperty(c) ? (s = d[c], '<font color="blue">排除表' + s + "</font> " + x) : x : a.hasOwnProperty(c) ? s && s == a[c] ? "MCC " + a[c] + " " + x : '<font color="red">MCC ' + a[c] + "</font> " + x : r.hasOwnProperty(c) ? s && s == r[c] ? "CC " + r[c] + " " + x : '<font color="red">CC ' + r[c] + "</font> " + x : x
                }))
            }(t).then((e => Ie.innerHTML = e.join("<br>"))) : Ie.innerHTML = ""
        })), Be.addEventListener("input", (e => {
            let t = e.target.value.split(/[, :;，：；\n\s]/).map((e => e ? e.replaceAll("X", "x") : ""));
            e.target.value = t.join("\n"), e.target.style.height = "20px", e.target.style.height = e.target.scrollHeight + "px", Ve.style.height = e.target.style.height, e.target.value.trim() ? async function (e) {
                const t = await i.e(4633).then(i.t.bind(i, 4633, 23)),
                    n = await i(9481)("./" + qt(ye.checked) + ".csv"), o = await i.e(8613).then(i.t.bind(i, 8613, 19));
                return e.map((e => {
                    let i = Object.values(n.default).find((t => t[0] == e)), d = i ? i[1] : "无效编码", a = e;
                    if (!ye.checked) {
                        let i = Object.values(t.default).find((t => t[0] == e));
                        a = i ? i[1] : e
                    }
                    return Object.values(o.default).includes(a) && (d = '<font color="red">灰码</font> <font color="gray">' + d + "</font>"), d
                }))
            }(t).then((e => Ve.innerHTML = e.join("<br>"))) : Ve.innerHTML = ""
        }));

        pt.value = Tt(window.location.href).allMoney;
        let Et = !0;

        function mt(e) {
            e.style.display = "none"
        }

        function jt(e) {
            e.style.display = ""
        }

        function Ot() {
            Ue.innerHTML = "", Et && async function (e) {
                const t = await i(9481)("./" + Pt(ye.checked) + ".csv");
                if (0 == (e = e.trim()).length || 1 == e.length && (e[0] >= "a" && e[0] <= "z" || e[0] >= "A" && e[0] <= "Z" || e[0] >= "0" && e[0] <= "9") || 2 == e.length && (e[0] >= "a" && e[0] <= "z" || e[0] >= "A" && e[0] <= "Z") && e[1] >= "0" && e[1] <= "9") return [];
                if (e.length >= 3 && (e[0] >= "a" && e[0] <= "z" || e[0] >= "A" && e[0] <= "Z") && e[1] >= "0" && e[1] <= "9" && e[2] >= "0" && e[2] <= "9") return Object.values(t.default).filter((t => t[0]?.includes(e[0].toUpperCase() + e.substring(1)))).slice(0, 3);
                if (e.startsWith("1型") || e.startsWith("2型")) return Object.values(t.default).filter((t => t[1]?.includes(e))).slice(0, 3);
                if (e.includes(" ")) return Object.values(t.default).filter((t => e.split(" ").every((e => new RegExp(e).test(t[1]))))).slice(0, 3);
                {
                    let i = Object.values(t.default).filter((t => t[1]?.includes(e))),
                        n = i.findIndex((t => t[1] == e));
                    return n && i.unshift(i[n]) && i.splice(n + 1, 1), i.slice(0, 3)
                }
            }(Ze.value).then(Zt).then((e => e.forEach((e => {
                let t = Ht(Ue, "", "div-block"), i = Ut(t, e[0]);
                Ht(t, e[1]), i.addEventListener("click", Lt)
            }))))
        }

        function Lt(e) {
            var t;
            t = e.target.innerText, qe.value = "" == qe.value ? t : qe.value + "\n" + t, qe.dispatchEvent(new Event("input")), Ze.value = "", Ze.dispatchEvent(new Event("input")), Ze.focus()
        }

        function Ct() {
            Xe.innerHTML = "", Et && async function (e) {
                const t = await i(9481)("./" + qt(ye.checked) + ".csv");
                if (0 == (e = e.trim()).length || 1 == e.length && e[0] >= "0" && e[0] <= "9" || 2 == e.length && e[0] >= "0" && e[0] <= "9" && e[1] >= "0" && e[1] <= "9") return [];
                if (e[0] >= "0" && e[0] <= "9" && e[1] >= "0" && e[1] <= "9" && e[2] >= ".") return Object.values(t.default).filter((t => t[0]?.includes(e))).slice(0, 3);
                if (e.includes(" ")) return Object.values(t.default).filter((t => e.split(" ").every((e => new RegExp(e).test(t[1]))))).slice(0, 3);
                {
                    let i = Object.values(t.default).filter((t => t[1]?.includes(e))),
                        n = i.findIndex((t => t[1] == e));
                    return n && i.unshift(i[n]) && i.splice(n + 1, 1), i.slice(0, 3)
                }
            }(Je.value).then(Nt).then((e => e.forEach((e => {
                let t = Ht(Xe, "", "div-block"), i = Ut(t, e[0]);
                Ht(t, e[1]), i.addEventListener("click", zt)
            }))))
        }

        function zt(e) {
            var t;
            t = e.target.innerText, Be.value = "" == Be.value ? t : Be.value + "\n" + t, Be.dispatchEvent(new Event("input")), Je.value = "", Je.dispatchEvent(new Event("input")), Je.focus()
        }

        function kt() {
            return Tt(window.location.href).meanAmount ? Tt(window.location.href).meanAmount : y.hasOwnProperty(xt.value) ? y[xt.value] : void 0
        }

        function Tt(e) {
            let t = e.split("?")[1];
            const i = new URLSearchParams(t);
            return Object.fromEntries(i.entries())
        }

        function St() {
            return ["zhejiang_2022", "shanxi_2024"].includes(tt.value) && "仅分组" != xt.value ? xt.value : tt.value
        }

        function At() {
            return g[tt.value] ?? tt.value
        }

        function Mt() {
            return ["sichuan_2022"].includes(At()) ? "SC" : ["zhejiang_2022"].includes(At()) ? "ZJ" : ["chs_drg_20", "tongchuan_2024", "shenyang_2024", "qinghai_2024", "shanxi_2024", "jilin_2024", "sichuan_2024", "qingdao_2024", "zhejiang_2024"].includes(At()) ? "20" : ["chs_drg_12"].includes(At()) ? "12" : ["beijing_2022", "yunnan_2023", "tianjinDRG_2024"].includes(At()) ? "BJ" : ["hefei_2023", "changsha_2023", "qingdao_2023", "changzhou_2023", "jinan_2023", "linyi_2024", "guangxi_2022", "lanzhou_2024", "mudanjiang_2023"].includes(At()) ? "11P" : ["wuhan_2024", "zhumadian_2024", "xining_2023"].includes(At()) ? "10P" : "11"
        }

        function Pt(e) {
            return e ? "YB_ZD_INFO" : "ZD_INFO"
        }

        function qt(e) {
            return e ? "YB_SS_INFO" : "SS_INFO"
        }

        async function Zt(e) {
            const t = await i.e(8129).then(i.t.bind(i, 8129, 19)), {MCC: n, CC: o} = await i(5364)("./" + At() + ".js"),
                d = await i.e(7439).then(i.t.bind(i, 7439, 23));
            return e.map((e => {
                let i, a = [e[1]];
                if (ye.checked) i = e[0]; else {
                    let t = Object.values(d.default).find((t => t[0] == e[0]));
                    t ? (i = t[1], a.push(t[1])) : i = e[0]
                }
                return ["zhejiang_2022", "sichuan_2022"].includes(At()) ? (n.hasOwnProperty(i) && a.unshift('<font color="red">MCC </font>'), o.hasOwnProperty(i) && a.unshift('<font color="red">CC </font>')) : (n.hasOwnProperty(i) && a.unshift('<font color="red">MCC ' + n[i] + "</font>"), o.hasOwnProperty(i) && a.unshift('<font color="red">CC ' + o[i] + "</font>")), Object.values(t.default).includes(i) && (a.unshift('<font color="red">灰码</font>'), a.splice(a.length - 1, 1, '<font color="gray">' + e[1] + "</font>")), [e[0], a.join(" ")]
            }))
        }

        async function Nt(e) {
            const t = await i.e(8613).then(i.t.bind(i, 8613, 19)), n = await i.e(4633).then(i.t.bind(i, 4633, 23));
            return e.map((e => {
                let i, o = [e[1]];
                if (ye.checked) i = e[0]; else {
                    let t = Object.values(n.default).find((t => t[0] == e[0]));
                    t ? (i = t[1], o.push(t[1])) : i = e[0]
                }
                return Object.values(t.default).includes(i) && (o.unshift('<font color="red">灰码</font>'), o.splice(o.length - 1, 1, '<font color="gray">' + e[1] + "</font>")), [e[0], o.join(" ")]
            }))
        }

        function Ht(e, t, i) {
            const n = document.createElement("div");
            return n.innerHTML = t, i && i.split(";").forEach((e => n.classList.add(e))), e.appendChild(n), n
        }

        function It(e, t, i, n, o) {
            const d = document.createElement(t);
            n && n.split(";").forEach((e => d.classList.add(e)));
            const a = document.createElement("div");
            if (o && o.split(";").forEach((e => a.classList.add(e))), i) {
                const e = document.createElement("label");
                e.htmlFor = d, e.textContent = i, e.classList.add(i.length <= 4 ? "label-" + i.length : "label-margin-bottom"), a.appendChild(e)
            }
            return a.appendChild(d), e.appendChild(a), d
        }

        function Ut(e, t, i, n) {
            const o = document.createElement("button");
            o.innerText = t, i && i.split(";").forEach((e => o.classList.add(e)));
            const d = document.createElement("div");
            return n && n.split(";").forEach((e => d.classList.add(e))), d.appendChild(o), e.appendChild(d), o
        }

        function Wt(e, t) {
            null == t && (t = "");
            for (let i = 0; i < e.options.length; i++) if (e.options[i].value == t) {
                e.selectedIndex = i;
                break
            }
        }

        async function Kt(e) {
            const t = await i.e(8129).then(i.t.bind(i, 8129, 19)), n = await i.e(6192).then(i.t.bind(i, 6192, 19)),
                o = e.zdList[0];
            return Object.values(t.default).includes(o) ? e.error = "主诊断无法识别" : Object.values(n.default).includes(o) && (e.error = "诊断不参与分组"), e
        }

        async function Bt(e) {
            return e.hasOwnProperty("error") ? (bt.innerHTML = "0000", yt.innerHTML = e.error, vt.style.display = "block", new Promise((() => {
            }))) : e
        }

        async function Jt(e) {
            const t = await i.e(8129).then(i.t.bind(i, 8129, 19)), n = await i.e(8613).then(i.t.bind(i, 8613, 19));
            let o = [...e.zdList.filter((e => Object.values(t.default).includes(e))).map((e => [1, e])), ...e.ssList.filter((e => Object.values(n).includes(e))).map((e => [2, e]))];
            if (["yunnan_2023"].includes(At())) {
                const t = await i.e(5068).then(i.t.bind(i, 5068, 19));
                o = o.concat(e.ssList.filter((e => Object.values(t.default).includes(e))).map((e => [3, e])))
            }
            if (["12", "20"].includes(Mt())) {
                const t = await i.e(6192).then(i.t.bind(i, 6192, 19)), n = await i.e(4421).then(i.t.bind(i, 4421, 19));
                o = [...o, ...e.zdList.filter((e => Object.values(t.default).includes(e))).map((e => [4, e])), ...e.ssList.filter((e => Object.values(n.default).includes(e))).map((e => [5, e]))]
            }
            return o
        }

        async function Yt(e) {
            const t = await i(1884)("./ZD_MDC_RULE_WK_" + Mt() + ".csv");
            let n = Object.values(t.default).find((t => t[0] == e.zdList[0]));
            if (n && 0 == e.ssList.length) {
                let t = n[1], i = n[0] + " 入" + t + "外科组，需要配合手术使用";
                e.qcMessages.push(i)
            }
            return e
        }

        async function Qt(e) {
            const t = await i(3270)("./ZD_ADRG_RULE_WK_" + Mt() + ".csv");
            let n = Object.values(t.default).find((t => t[0] == e.zdList[0]));
            if (n && 0 == e.ssList.length) {
                let t = n[1].replaceAll(" ", "/"), i = n[0] + " 入外科组" + t + "，需要配合手术使用";
                e.qcMessages.push(i)
            }
            return e
        }

        async function Vt(e) {
            const {group_record: t, CCE: n, MCC: o, CC: d, SS_VALID: a} = await i(5364)("./" + At() + ".js");
            let r = [], s = "", l = [], x = [];
            ["zhejiang_2022", "sichuan_2022"].includes(At()) ? (l = e.zdList.filter((e => o.hasOwnProperty(e))), x = e.zdList.filter((e => d.hasOwnProperty(e)))) : (s = n.hasOwnProperty(e.zdList[0]) ? n[e.zdList[0]] : "", l = e.zdList.slice(1).filter((e => o.hasOwnProperty(e) && s !== o[e])), x = e.zdList.slice(1).filter((e => d.hasOwnProperty(e) && s !== d[e])), s && r.push("主诊断" + e.zdList[0] + "排除表" + s) && Object.assign(e, {
                cce: s
            }));
            let c = e.ssList ? e.ssList.filter((e => a.includes(e))) : [];
            l && l.length > 0 && r.push("以下诊断是MCC：" + l.join(" ")) && Object.assign(e, {mccList: l}) || Object.assign(e, {mccList: []}), x && x.length > 0 && r.push("以下诊断是CC：" + x.join(" ")) && Object.assign(e, {ccList: x}) || Object.assign(e, {ccList: []}), c && c.length > 0 && r.push("以下手术操作有效：" + c.join(" ")) && Object.assign(e, {ssValidList: c}) || Object.assign(e, {ssValidList: []});
            let F = t(e);
            return Object.assign(F, e), F.messages.unshift.apply(F.messages, r), F.messages.unshift.apply(F.messages, e.qcMessages.map((e => new RegExp(/^[A-Z]\d{2}[.]/).test(e) || new RegExp(/^\d{2}[.]/).test(e) ? '<font color="red">' + e.substring(0, e.indexOf(" ")) + "</font>" + e.substring(e.indexOf(" ")) : e))), F.messages.unshift.apply(F.messages, e?.mapMessages), F
        }

        async function Xt(e) {
            gt.innerHTML = function (e) {
                const t = document.createElement("ul");
                e.forEach((e => {
                    let i = document.createElement("li");
                    i.innerHTML = e, t.appendChild(i)
                }));
                const i = document.createElement("div");
                return i.appendChild(t), i.innerHTML
            }(e.messages), bt.innerHTML = e.drg;
            let t, n = await i(2646)("./" + St() + ".csv"),
                o = Object.values(n.default).slice(1).find((t => t[0] == e.drg));
            if (o && o.length > 2 && Object.values(n.default)[0].includes("权重")) {
                let i = Object.values(n.default)[0].indexOf("权重");
                i < 0 && (i = Object.values(n.default)[0].indexOf("参考权重")), e.rw = o[i];
                let d = Object.values(n.default)[0].indexOf("支付标准（" + ct.value + "）");
                d < 0 && (d = Object.values(n.default)[0].indexOf("支付标准（" + ct.value + Rt.value + "）")), d < 0 && (d = Object.values(n.default)[0].indexOf("支付标准（" + ut.value + "）")), d < 0 && (d = Object.values(n.default)[0].indexOf("例均费用（" + ct.value + "）")), d < 0 && (d = Object.values(n.default)[0].indexOf("例均费用（" + ct.value + Rt.value + "）"));
                let a = o[i]?.replace(/([0-9]+.[0-9]{4})[0-9]*/, "$1");
                t = d > 0 ? o[1] + "&nbsp;&nbsp;&nbsp;&nbsp;" + a + "&nbsp;&nbsp;&nbsp;&nbsp;" + o[d].replace(/([0-9]+.[0-9]{2})[0-9]*/, "$1") + "元" : o[1] + " " + a
            } else t = o && o.length > 1 ? o[1] : "";
            return yt.innerHTML = t, vt.style.display = "block", e
        }

        function $t(e) {
            if (!e) return "";
            let t, i = document.createElement("table"), n = document.createElement("thead"),
                o = document.createElement("tbody");
            if (e instanceof Array) {
                if (0 == e.length) return "";
                t = document.createElement("tr"), e[0].forEach((e => {
                    let i = document.createElement("th");
                    i.innerHTML = e, t.appendChild(i)
                })), n.appendChild(t), e.slice(1).forEach((e => {
                    t = document.createElement("tr"), e.forEach((e => {
                        let i = document.createElement("td");
                        void 0 === e || "number" == typeof e && isNaN(e) || "NaN" === e ? i.innerHTML = "" : "string" == typeof e ? (e.startsWith("<"), i.innerHTML = e.replace(/([0-9]+.[0-9]{4})[0-9]*/, "$1")) : "number" == typeof item ? i.innerHTML = e : "object" == typeof e ? i.appendChild(e) : i.innerHTML = e, t.appendChild(i)
                    })), o.appendChild(t)
                }))
            } else {
                let i = [], n = [];
                Object.keys(e).forEach((t => {
                    if (!["haerbin_2024", "zhejiang_2022"].includes(At()) && "DRG名称" == t) return;
                    let o = document.createElement("th");
                    o.innerHTML = t.replace("（", "<br>（"), i.push(o);
                    let d = document.createElement("td");
                    void 0 === e[t] || "number" == typeof e[t] && isNaN(e[t]) || "NaN" === e[t] ? d.innerHTML = "" : t.endsWith("占比") || "入组率" == t ? d.innerHTML = toPercent(e[t], 2) : "string" == typeof e[t] ? t.startsWith("支付标准") ? d.innerHTML = e[t].replace(/([0-9]+.[0-9]{2})[0-9]*/, "$1") : d.innerHTML = e[t] : "number" == typeof e[t] ? d.innerHTML = e[t] : "object" == typeof e[t] ? d.appendChild(e[t]) : d.innerHTML = e[t], n.push(d)
                })), t = document.createElement("tr"), i.forEach((e => t.appendChild(e))), o.appendChild(t), t = document.createElement("tr"), n.forEach((e => t.appendChild(e))), o.appendChild(t)
            }
            i.appendChild(n), i.appendChild(o);
            const d = document.createElement("div");
            return d.appendChild(i), d.innerHTML
        }

        Ze.addEventListener("compositionstart", (() => {
            Et = !1
        })), Ze.addEventListener("compositionend", (() => {
            Et = !0
        })), Ze.addEventListener("input", (e => {
            setTimeout(Ot, 100)
        })), Ze.addEventListener("keypress", (e => {
            if (13 == e.keyCode) {
                let e = Ue.querySelector("button");
                e && e.focus()
            }
        })), Je.addEventListener("compositionstart", (() => {
            Et = !1
        })), Je.addEventListener("compositionend", (() => {
            Et = !0
        })), Je.addEventListener("input", (e => {
            setTimeout(Ct, 100)
        })), Je.addEventListener("keypress", (e => {
            if (13 == e.keyCode) {
                let e = Xe.querySelector("button");
                e && e.focus()
            }
        })), tt.addEventListener("change", (async e => {
            var t = $e.querySelector(".red");
            t && (t.innerText = ""), "foshan_2022" == At() ? (jt(Se), Ee.querySelectorAll("label").forEach((e => "ICU时长" == e.innerText && jt(e)))) : (mt(Se), Ee.querySelectorAll("label").forEach((e => "ICU时长" == e.innerText && mt(e)))), "sichuan_2024" == At() ? (jt(Ae), Ee.querySelectorAll("label").forEach((e => "重症监护" == e.innerText && jt(e)))) : (mt(Ae), Ee.querySelectorAll("label").forEach((e => "重症监护" == e.innerText && mt(e)))), "dingzhou_2023" == At() ? (jt(ft), lt.querySelectorAll("label").forEach((e => "统筹费用" == e.innerText && jt(e)))) : (mt(ft), lt.querySelectorAll("label").forEach((e => "统筹费用" == e.innerText && mt(e))));
            try {
                const {group_record: e} = await i(5364)("./" + At() + ".js")
            } catch {
                return alert("社区版不支持本区域的分组方案，需要使用专业版，可通过官方网站扫码入群或邮件联系"), new Promise((() => {
                }))
            }
            b.hasOwnProperty(e.target.value) ? (fe.includes(e.target.value) && (st.innerHTML = ""), lt.style.display = "block", xt.options.length = 0, b[e.target.value]?.forEach((e => xt.add(new Option(e, e, null, null)))), xt.dispatchEvent(new Event("change"))) : (lt.style.display = "none", xt.options.length = 0), qe.dispatchEvent(new Event("input")), Be.dispatchEvent(new Event("input"))
        })), xt.addEventListener("change", (e => {
            if ("仅分组" == e.target.value) return mt(ct), lt.querySelectorAll("label").forEach((e => "等级" == e.innerText && mt(e))), mt(Ft), lt.querySelectorAll("label").forEach((e => "系数" == e.innerText && mt(e))), mt(ut), lt.querySelectorAll("label").forEach((e => "医院" == e.innerText && mt(e))), mt(Dt), lt.querySelectorAll("label").forEach((e => "调节系数" == e.innerText && mt(e))), mt(Rt), lt.querySelectorAll("label").forEach((e => "医保类型" == e.innerText && mt(e))), mt(ht), lt.querySelectorAll("label").forEach((e => "点值" == e.innerText && mt(e))), mt(Gt), lt.querySelectorAll("label").forEach((e => "费率" == e.innerText && mt(e))), mt(pt), lt.querySelectorAll("label").forEach((e => "总费用" == e.innerText && mt(e))), void mt(_t);
            if (E.hasOwnProperty(e.target.value)) {
                mt(ct), lt.querySelectorAll("label").forEach((e => "等级" == e.innerText && mt(e))), jt(ut), lt.querySelectorAll("label").forEach((e => "医院" == e.innerText && jt(e))), ut.options.length = 0;
                let t = ve;
                E[e.target.value].includes(t) && (ut.add(new Option(t, t)), mt(ut), lt.querySelectorAll("label").forEach((e => "医院" == e.innerText && mt(e))))
            } else mt(ut), lt.querySelectorAll("label").forEach((e => "医院" == e.innerText && mt(e)));
            w.hasOwnProperty(e.target.value) ? (jt(ct), lt.querySelectorAll("label").forEach((e => "等级" == e.innerText && jt(e))), ct.options.length = 0, w.hasOwnProperty(e.target.value) ? w[e.target.value].forEach((e => ct.add(new Option(e, e, null, null)))) : w["默认"].forEach((e => ct.add(new Option(e, e, null, null))))) : (mt(ct), lt.querySelectorAll("label").forEach((e => "等级" == e.innerText && mt(e)))), m.hasOwnProperty(e.target.value) ? (jt(Ft), lt.querySelectorAll("label").forEach((e => "系数" == e.innerText && jt(e)))) : (mt(Ft), lt.querySelectorAll("label").forEach((e => "系数" == e.innerText && mt(e)))), ["无锡", "临沂", "天津"].includes(e.target.value) ? (jt(Dt), lt.querySelectorAll("label").forEach((e => "调节系数" == e.innerText && jt(e))), Dt.value = fe.includes(St()) ? "" : 1) : (mt(Dt), lt.querySelectorAll("label").forEach((e => "调节系数" == e.innerText && mt(e)))), j.hasOwnProperty(e.target.value) ? (jt(ht), lt.querySelectorAll("label").forEach((e => "点值" == e.innerText && jt(e))), j[e.target.value].hasOwnProperty("职工") ? (jt(Rt), lt.querySelectorAll("label").forEach((e => "医保类型" == e.innerText && jt(e))), Rt.options.length = 0, Object.keys(j[e.target.value]).forEach((e => Rt.add(new Option(e, e, null, Tt(window.location.href).insuranceType == e))))) : (mt(Rt), lt.querySelectorAll("label").forEach((e => "医保类型" == e.innerText && mt(e))))) : (mt(ht), lt.querySelectorAll("label").forEach((e => "点值" == e.innerText && mt(e)))), O.hasOwnProperty(e.target.value) ? (jt(Gt), lt.querySelectorAll("label").forEach((e => "费率" == e.innerText && jt(e))), O[e.target.value].hasOwnProperty("职工") ? (jt(Rt), lt.querySelectorAll("label").forEach((e => "医保类型" == e.innerText && jt(e))), Rt.options.length = 0, Object.keys(O[e.target.value]).forEach((e => Rt.add(new Option(e, e, null, Tt(window.location.href).insuranceType == e))))) : O[e.target.value].hasOwnProperty("一级") && O[e.target.value]["一级"].hasOwnProperty("职工") || O[e.target.value].hasOwnProperty("二级") && O[e.target.value]["二级"].hasOwnProperty("职工") || O[e.target.value].hasOwnProperty("三级") && O[e.target.value]["三级"].hasOwnProperty("职工") || O[e.target.value].hasOwnProperty("Ⅰ级") && O[e.target.value]["Ⅰ级"].hasOwnProperty("职工") || O[e.target.value].hasOwnProperty("C级") && O[e.target.value]["C级"].hasOwnProperty("职工") || O[e.target.value].hasOwnProperty("A类") && O[e.target.value]["A类"].hasOwnProperty("职工") || O[e.target.value].hasOwnProperty("Ⅰ类") && O[e.target.value]["Ⅰ类"].hasOwnProperty("职工") || O[e.target.value].hasOwnProperty("一类") && O[e.target.value]["一类"].hasOwnProperty("职工") || O[e.target.value].hasOwnProperty("一档") && O[e.target.value]["一档"].hasOwnProperty("职工") || O[e.target.value].hasOwnProperty("A档") && O[e.target.value]["A档"].hasOwnProperty("职工") ? (jt(Rt), lt.querySelectorAll("label").forEach((e => "医保类型" == e.innerText && jt(e))), Rt.options.length = 0, Object.keys(O[e.target.value][Object.keys(O[e.target.value])[0]]).forEach((e => Rt.add(new Option(e, e, null, Tt(window.location.href).insuranceType == e))))) : (mt(Rt), lt.querySelectorAll("label").forEach((e => "医保类型" == e.innerText && mt(e))))) : (mt(Gt), lt.querySelectorAll("label").forEach((e => "费率" == e.innerText && mt(e)))), jt(pt), lt.querySelectorAll("label").forEach((e => "总费用" == e.innerText && jt(e))), jt(_t), ct.dispatchEvent(new Event("change"))
        })), ct.addEventListener("change", (e => {
            var t;
            Ft.value = (t = e.target.value, fe.includes(St()) ? "" : Tt(window.location.href)[t] ? Tt(window.location.href)[t] : Tt(window.location.href).coef ? Tt(window.location.href).coef : m.hasOwnProperty(xt.value) ? m[xt.value][t] ?? "" : void 0), Rt.dispatchEvent(new Event("change"))
        })), Rt.addEventListener("change", (e => {
            ct.value = "二级";
            Gt.value = 9575.25;
            Ft.value = 0.92;
        })), function (e) {
            const t = function (e) {
                let t, i = e, n = new RegExp('"(.*?)"', "g");
                for (; t = n.exec(e);) i = i.replace(t[0], t[1].replaceAll(",", "|"));
                return i
            }(e).split(",");
            if (t.length < 10) return void (we.innerText = "病案信息格式不正确，信息不全");
            let i = 0;
            me.value = t[i++], Wt(je, t[i++]), Oe.value = t[i++], Le.value = t[i++], Ce.value = t[i++], ze.value = t[i++], ke.value = t[i++], Wt(Te, t[i++]), qe.value = (t[i++] ?? "").split("|").join("\n"), Be.value = (t[i++] ?? "").split("|").join("\n");
            let n = i++;
            "foshan_2022" == St() ? Se.value = t[n] : "sichuan_2024" == St() && Wt(Ae, t[n])
        }(Tt(window.location.href).groupVal), "1" == localStorage.getItem("load") && (qe.value = "", Be.value = "", qe.dispatchEvent(new Event("input")), Be.dispatchEvent(new Event("input"))), localStorage.removeItem("load"), ye.dispatchEvent(new Event("click")), tt.dispatchEvent(new Event("change"))
    }, 5902: (e, t, i) => {
        "use strict";
        i.d(t, {Z: () => r});
        var n = i(8081), o = i.n(n), d = i(3645), a = i.n(d)()(o());
        a.push([e.id, ":root{\n  --div-full-width:880px;\n}\n\n@media (max-width: 880px) {\n  :root{\n    --div-full-width:410px;\n  }\n}\n\n.div-full-half{\n  width:600px;\n  padding: 6px;\n  border: 1px solid black;\n  display:block;\n}\n\n.left {\n  float:left;\n}\n\n.right {\n  float:right;\n}\n\n.red {\n  color: red;\n}\n\n.blue {\n  color: blue;\n}\n\ndiv {\n  margin-top:2px;margin-right:10px;display:inline-block;\n  /* border: 1px solid red; */\n}\n\nlabel,a,div,button {\n  font-size:11pt;font-family:'Microsoft YaHei';\n}\n\n.button {\n  background-color: #4CAF50; /* Green */\n  border: none;\n  color: white;\n  /* padding: 15px 32px; */\n  text-align: center;\n  text-decoration: none;\n  display: inline-block;\n  /* font-size: 16px; */\n}\n\n.button-arrow {\n  border: none;\n  text-align: center;\n  text-decoration: none;\n  display: inline-block;\n  font-size: 10pt;\n}\n\n.label-margin-bottom{\n  margin-bottom:2px;\n}\n\n.margin-top-6 {\n  margin-top: 6px;\n}\n\n.margin-top-20 {\n  margin-top: 20px;\n}\n\n.margin-left {\n  margin-left: 20px;\n}\n\n.margin-right {\n  margin-right: 20px;\n}\n\ninput,select,textarea {\n  font-size:11pt;font-family:'Microsoft YaHei';\n}\n\ntextarea {\n  resize: none;\n}\n\n.div-table {\n  display:table;\n  margin:0 auto;\n}\n\n.div-card {\n  width: 160px;\n  border: 1px solid red;\n}\n\n.div-inline-block {\n  display:inline-block;\n}\n\n.div-block {\n  display:block;\n}\n\n.div-full {\n  width: var(--div-full-width);\n  padding: 6px;\n  border: 1px solid black;\n  display:block;\n}\n\n.div-full-embed {\n  width: 900px;\n  padding: 6px;\n  border: 1px solid black;\n  display:block;\n}\n\n.div-top {\n  vertical-align:top;\n}\n\n.div-icd-code{\n  width:130px;\n}\n\n.div-icd-desc{\n  width:240px;\n  vertical-align:top;\n}\n\n.div-icd-desc-inside{\n  width:240px;\n  padding: 4px 6px;\n  border:1px solid black;\n  white-space: nowrap;\n}\n\n.div-search-desc{\n  width: 400px;\n  display:block;\n}\n\n.div-icd-desc-440{\n  width:440px;\n  vertical-align:top;\n}\n\n.div-icd-desc-inside-440{\n  width:440px;\n  padding: 4px 6px;\n  border:1px solid black;\n  white-space: nowrap;\n}\n\n.div-search-desc-600{\n  width: 600px;\n  display:block;\n}\n\n.background {\n  background-color: antiquewhite;\n}\n\n.hide {\n  display: none;\n}\n\n.div-code {\n  color: red;\n  font-size: 14pt;\n  /* display:block; */\n}\n\n.div-input {\n  width: 200px;\n}\n\n.input-calc-short {\n  width: 30px;\n}\n.input-calc-middle {\n  width: 55px;\n}\n.input-calc-long {\n  width: 70px;\n}\n\nlabel {\n  display:inline-block;\n}\n\n.label-short {\n  width:70px;\n}\n.label-2 {\n  width:35px;\n}\n.label-3 {\n  width:50px;\n}\n.label-4 {\n  width:65px;\n}\n\n.input-short {\n  width:50px;\n  display:block;\n}\n\n.input-long {\n  width:100px;\n  display:block;\n}\n\n.input-icd-code {\n  width:125px;\n  display:block;\n}\n\n.input-file {\n  width:160px;display:inline-block;margin-left: 10px;\n}\n\n.icd-code{\n  width:100%;\n  padding: 4px 6px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.icd-name{\n  width:100%;\n  background-color:beige;cursor:not-allowed;opacity:0.6;\n}\n\n.zdList,.ssList {\n  width: 40%;\n  font-size: 11pt; \n  padding: 6px;\n}\n\npre {\n  white-space: pre-wrap; \n  color: blue; \n  font-size: 11pt; \n  font-family: monospace;\n}\n\ntable {\n  font-family: verdana,arial,sans-serif;\n  font-size:11px;\n  color:#333333;\n  border-width: 1px;\n  border-color: #666666;\n  border-collapse: collapse;\n  /* width: 100%; */\n}\n\nth {\n  border-width: 1px;\n  padding: 8px;\n  border-style: solid;\n  border-color: #666666;\n  background-color: #dedede;\n}\ntd {\n  border-width: 1px;\n  padding: 8px;\n  border-style: solid;\n  border-color: #666666;\n  background-color: #ffffff;\n}", ""]);
        const r = a
    }, 3645: e => {
        "use strict";
        e.exports = function (e) {
            var t = [];
            return t.toString = function () {
                return this.map((function (t) {
                    var i = "", n = void 0 !== t[5];
                    return t[4] && (i += "@supports (".concat(t[4], ") {")), t[2] && (i += "@media ".concat(t[2], " {")), n && (i += "@layer".concat(t[5].length > 0 ? " ".concat(t[5]) : "", " {")), i += e(t), n && (i += "}"), t[2] && (i += "}"), t[4] && (i += "}"), i
                })).join("")
            }, t.i = function (e, i, n, o, d) {
                "string" == typeof e && (e = [[null, e, void 0]]);
                var a = {};
                if (n) for (var r = 0; r < this.length; r++) {
                    var s = this[r][0];
                    null != s && (a[s] = !0)
                }
                for (var l = 0; l < e.length; l++) {
                    var x = [].concat(e[l]);
                    n && a[x[0]] || (void 0 !== d && (void 0 === x[5] || (x[1] = "@layer".concat(x[5].length > 0 ? " ".concat(x[5]) : "", " {").concat(x[1], "}")), x[5] = d), i && (x[2] ? (x[1] = "@media ".concat(x[2], " {").concat(x[1], "}"), x[2] = i) : x[2] = i), o && (x[4] ? (x[1] = "@supports (".concat(x[4], ") {").concat(x[1], "}"), x[4] = o) : x[4] = "".concat(o)), t.push(x))
                }
            }, t
        }
    }, 8081: e => {
        "use strict";
        e.exports = function (e) {
            return e[1]
        }
    }, 3379: e => {
        "use strict";
        var t = [];

        function i(e) {
            for (var i = -1, n = 0; n < t.length; n++) if (t[n].identifier === e) {
                i = n;
                break
            }
            return i
        }

        function n(e, n) {
            for (var d = {}, a = [], r = 0; r < e.length; r++) {
                var s = e[r], l = n.base ? s[0] + n.base : s[0], x = d[l] || 0, c = "".concat(l, " ").concat(x);
                d[l] = x + 1;
                var F = i(c), u = {css: s[1], media: s[2], sourceMap: s[3], supports: s[4], layer: s[5]};
                if (-1 !== F) t[F].references++, t[F].updater(u); else {
                    var D = o(u, n);
                    n.byIndex = r, t.splice(r, 0, {identifier: c, updater: D, references: 1})
                }
                a.push(c)
            }
            return a
        }

        function o(e, t) {
            var i = t.domAPI(t);
            return i.update(e), function (t) {
                if (t) {
                    if (t.css === e.css && t.media === e.media && t.sourceMap === e.sourceMap && t.supports === e.supports && t.layer === e.layer) return;
                    i.update(e = t)
                } else i.remove()
            }
        }

        e.exports = function (e, o) {
            var d = n(e = e || [], o = o || {});
            return function (e) {
                e = e || [];
                for (var a = 0; a < d.length; a++) {
                    var r = i(d[a]);
                    t[r].references--
                }
                for (var s = n(e, o), l = 0; l < d.length; l++) {
                    var x = i(d[l]);
                    0 === t[x].references && (t[x].updater(), t.splice(x, 1))
                }
                d = s
            }
        }
    }, 569: e => {
        "use strict";
        var t = {};
        e.exports = function (e, i) {
            var n = function (e) {
                if (void 0 === t[e]) {
                    var i = document.querySelector(e);
                    if (window.HTMLIFrameElement && i instanceof window.HTMLIFrameElement) try {
                        i = i.contentDocument.head
                    } catch (e) {
                        i = null
                    }
                    t[e] = i
                }
                return t[e]
            }(e);
            if (!n) throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");
            n.appendChild(i)
        }
    }, 9216: e => {
        "use strict";
        e.exports = function (e) {
            var t = document.createElement("style");
            return e.setAttributes(t, e.attributes), e.insert(t, e.options), t
        }
    }, 3565: (e, t, i) => {
        "use strict";
        e.exports = function (e) {
            var t = i.nc;
            t && e.setAttribute("nonce", t)
        }
    }, 7795: e => {
        "use strict";
        e.exports = function (e) {
            if ("undefined" == typeof document) return {
                update: function () {
                }, remove: function () {
                }
            };
            var t = e.insertStyleElement(e);
            return {
                update: function (i) {
                    !function (e, t, i) {
                        var n = "";
                        i.supports && (n += "@supports (".concat(i.supports, ") {")), i.media && (n += "@media ".concat(i.media, " {"));
                        var o = void 0 !== i.layer;
                        o && (n += "@layer".concat(i.layer.length > 0 ? " ".concat(i.layer) : "", " {")), n += i.css, o && (n += "}"), i.media && (n += "}"), i.supports && (n += "}");
                        var d = i.sourceMap;
                        d && "undefined" != typeof btoa && (n += "\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(d)))), " */")), t.styleTagTransform(n, e, t.options)
                    }(t, e, i)
                }, remove: function () {
                    !function (e) {
                        if (null === e.parentNode) return !1;
                        e.parentNode.removeChild(e)
                    }(t)
                }
            }
        }
    }, 4589: e => {
        "use strict";
        e.exports = function (e, t) {
            if (t.styleSheet) t.styleSheet.cssText = e; else {
                for (; t.firstChild;) t.removeChild(t.firstChild);
                t.appendChild(document.createTextNode(e))
            }
        }
    }, 2646: (e, t, i) => {
        var n = {
            "./aba_2023.csv": [3083, 3083],
            "./aletai_2023.csv": [7904, 7904],
            "./ankang_2024.csv": [3437, 3437],
            "./anshan_2024.csv": [761, 761],
            "./anyang_2021.csv": [5339, 5339],
            "./bazhong_2024.csv": [5338, 5338],
            "./beijing_2022.csv": [5001, 5001],
            "./bengbu_2023.csv": [2914, 2914],
            "./changchun_2023.csv": [8126, 8126],
            "./changji_2024.csv": [9623, 9623],
            "./changsha_2023.csv": [5024, 5024],
            "./changzhi_2022.csv": [5233, 5233],
            "./changzhou_2023.csv": [6288, 6288],
            "./chengdu_2024.csv": [1644, 1644],
            "./chenzhou_2024.csv": [2617, 2617],
            "./chizhou_2024.csv": [7221, 7221],
            "./chongqing_2024.csv": [217, 217],
            "./chongqing_2025.csv": [217, 217],
            "./chs_drg_10.csv": [5086, 5086],
            "./chs_drg_11.csv": [4110, 4110],
            "./chs_drg_12.csv": [4102, 4102],
            "./chs_drg_20.csv": [6905, 6905],
            "./chuzhou_2024.csv": [2011, 2011],
            "./dalian_2022.csv": [9245, 9245],
            "./foshan_2022.csv": [3913, 3913],
            "./fuzhou_2024.csv": [64, 64],
            "./guangan_2023.csv": [6230, 6230],
            "./guangan_2024.csv": [1941, 1941],
            "./guangxi_2022.csv": [9798, 9798],
            "./handan_2024.csv": [3420, 3420],
            "./hefei_2023.csv": [8355, 8355],
            "./henan_2024.csv": [1553, 9216],
            "./jiyuan_2023.csv": [8359, 8359],
            "./kashi_2023.csv": [2346, 2346],
            "./kezhou_2023.csv": [2384, 2384],
            "./leshan_2023.csv": [5910, 5910],
            "./liaocheng_2022.csv": [6913, 6913],
            "./linfen_2022.csv": [6678, 6678],
            "./liuan_2023.csv": [3667, 3667],
            "./luohe_2023.csv": [3802, 3802],
            "./maanshan_2023.csv": [8704, 8704],
            "./meishan_2023.csv": [6320, 6320],
            "./meishan_2024.csv": [4716, 4716],
            "./mudanjiang_2023.csv": [3321, 3321],
            "./nanchang_2023.csv": [2676, 2676],
            "./nanjing_2022.csv": [8163, 8163],
            "./nanping_2023.csv": [1232, 1232],
            "./nantong_2024.csv": [4221, 4221],
            "./qinghai_2024.csv": [5151, 5151],
            "./qingyang_2023.csv": [2833, 2833],
            "./quanzhou_2023.csv": [3921, 3921],
            "./shanghaiDRG_2023.csv": [7723, 7723],
            "./shenyang_2024.csv": [1276, 1276],
            "./sichuan_2022.csv": [2045, 2045],
            "./sichuan_2024.csv": [1929, 1929],
            "./suining_2023.csv": [8010, 8010],
            "./suzhou_2024.csv": [359, 359],
            "./taizhou_2023.csv": [6533, 6533],
            "./tianjinDRG_2024.csv": [6880, 6880],
            "./tongchuan_2023.csv": [9331, 9331],
            "./tongren_2024.csv": [1684, 1684],
            "./wlmq_2022.csv": [3537, 3537],
            "./wuhan_2024.csv": [3692, 3692],
            "./wuxi_2023.csv": [4119, 4119],
            "./xian_2024.csv": [1820, 1820],
            "./xianyang_2024.csv": [3391, 3391],
            "./xining_2023.csv": [92, 92],
            "./xjbt_2022.csv": [9562, 9562],
            "./xuzhou_2023.csv": [6377, 6377],
            "./yaan_2024.csv": [9437, 9437],
            "./yancheng_2023.csv": [8434, 8434],
            "./yantai_2023.csv": [3373, 3373],
            "./yili_2023.csv": [3407, 3407],
            "./yinchuan_2023.csv": [1830, 1830],
            "./yunnan_2023.csv": [8890, 8890],
            "./zhejiang_2022.csv": [8058, 8058],
            "./zhejiang_2024.csv": [2699, 2699],
            "./zhumadian_2024.csv": [6958, 6958],
            "./ziyang_2023.csv": [4931, 4931],
            "./丽水.csv": [4318, 4318],
            "./台州.csv": [4032, 4032],
            "./嘉兴.csv": [9790, 9790],
            "./宁波.csv": [6016, 6016],
            "./杭州.csv": [3037, 3037],
            "./衢州.csv": [7190, 7190],
            "./金华.csv": [5368, 5368]
        };

        function o(e) {
            if (!i.o(n, e)) return Promise.resolve().then((() => {
                var t = new Error("Cannot find module '" + e + "'");
                throw t.code = "MODULE_NOT_FOUND", t
            }));
            var t = n[e], o = t[0];
            return i.e(t[1]).then((() => i.t(o, 23)))
        }

        o.keys = () => Object.keys(n), o.id = 2646, e.exports = o
    }
}, e => {
    e(e.s = 437)
}]);
