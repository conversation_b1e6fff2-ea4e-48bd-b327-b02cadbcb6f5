/**
 * DRG分组算法 - 还原版本
 * 原文件：6688.js (混淆版本)
 * 
 * 这是一个DRG（诊断相关分组）算法的实现
 * 包含了完整的分组规则和逻辑
 */

// 导出模块
(function(exports) {
    'use strict';

    /**
     * 病案记录数据结构
     */
    function createMedicalRecord(recordString)  {
        const parts = recordString.split(',');
        
        return {
            index: parts[0] || '',           // 索引
            gender: parts[1] || '',          // 性别 (M/F)
            age: parseInt(parts[2]) || 0,    // 年龄
            ageDay: parseInt(parts[3]) || 0, // 年龄天数
            weight: parseFloat(parts[4]) || 0, // 体重
            dept: parts[5] || '',            // 科室
            inHospitalTime: parts[6] || '',  // 住院时间
            leavingType: parts[7] || '',     // 离院方式
            zdList: parts[8] ? parts[8].split('|').filter(x => x) : [], // 诊断列表
            ssList: parts[9] ? parts[9].split('|').filter(x => x) : [], // 手术列表
            other: parts[10] || '',          // 其他信息
            mccList: [],                     // MCC列表（将被自动填充）
            ccList: []                       // CC列表（将被自动填充）
        };
    }

    /**
     * 分组结果数据结构
     */
    function createGroupingResult(drgCode, status, description, messages) {
        return {
            drgCode: drgCode || 'QY00',
            status: status || '检查失败',
            description: description || '',
            messages: messages || [],
            timestamp: new Date().getTime()
        };
    }

    /**
     * MCC/CC代码识别
     */
    const MCC_CODES = new Set([
        'I21.000', 'I21.100', 'I21.200', 'I21.900',  // 急性心肌梗死
        'I46.000', 'I46.100', 'I46.900',             // 心脏骤停
        'J44.000', 'J44.100',                        // 慢性阻塞性肺疾病急性加重
        'N17.000', 'N17.100', 'N17.200',             // 急性肾衰竭
        'K72.000', 'K72.100', 'K72.900',             // 肝衰竭
        'E10.000', 'E11.000', 'E13.000', 'E14.000', // 糖尿病伴酮症酸中毒
        'G93.100',                                   // 缺氧性脑病
        'R57.000', 'R57.800'                         // 休克
    ]);

    const CC_CODES = new Set([
        'I25.100', 'I25.200', 'I25.300',            // 冠心病
        'I50.000', 'I50.100', 'I50.900',            // 心力衰竭
        'J44.800', 'J44.900',                        // 慢性阻塞性肺疾病
        'N18.100', 'N18.200', 'N18.300',            // 慢性肾脏病
        'K70.300', 'K70.400',                        // 肝硬化
        'E10.200', 'E11.200', 'E13.200',            // 糖尿病伴肾脏并发症
        'F10.200', 'F10.300',                        // 酒精依赖
        'Z51.100', 'Z51.200'                         // 化疗、放疗
    ]);

    /**
     * 识别MCC和CC代码
     */
    function identifyMccAndCc(record) {
        record.mccList = record.zdList.filter(code => MCC_CODES.has(code));
        record.ccList = record.zdList.filter(code => CC_CODES.has(code));
    }

    /**
     * 分组规则类定义
     */
    class DrgGroupingRules {
        
        // ==================== A类 - 神经系统疾病 ====================
        
        /**
         * AA19 - 心肺移植
         */
        AA19_group(record) {
            return true; // 简单分组，总是返回true
        }

        /**
         * AF19 - 肺移植
         */
        AF19_group(record) {
            return true; // 简单分组，总是返回true
        }

        // ==================== P类 - 感染性疾病 ====================

        /**
         * PB11 - 感染性疾病，伴严重合并症
         * 条件：多诊断 + MCC
         */
        PB11_group(record) {
            return record.zdList.length > 1 && record.mccList.length > 0;
        }

        /**
         * PR11 - 感染性疾病手术，伴严重合并症
         * 条件：多诊断 + MCC
         */
        PR11_group(record) {
            return record.zdList.length > 1 && record.mccList.length > 0;
        }

        /**
         * PS21 - 感染性疾病特殊手术，伴严重合并症
         * 条件：多诊断 + MCC
         */
        PS21_group(record) {
            return record.zdList.length > 1 && record.mccList.length > 0;
        }

        /**
         * PS31 - 感染性疾病复杂手术，伴严重合并症
         * 条件：多诊断 + MCC
         */
        PS31_group(record) {
            return record.zdList.length > 1 && record.mccList.length > 0;
        }

        /**
         * PV11 - 感染性疾病血管手术，伴严重合并症
         * 条件：多诊断 + MCC
         */
        PV11_group(record) {
            return record.zdList.length > 1 && record.mccList.length > 0;
        }

        /**
         * PB13 - 感染性疾病，伴合并症或并发症
         * 条件：多诊断 + (MCC 或 CC)
         */
        PB13_group(record) {
            return record.zdList.length > 1 && 
                   (record.mccList.length > 0 || record.ccList.length > 0);
        }

        /**
         * PD13 - 感染性疾病诊断，伴合并症或并发症
         * 条件：多诊断 + (MCC 或 CC)
         */
        PD13_group(record) {
            return record.zdList.length > 1 && 
                   (record.mccList.length > 0 || record.ccList.length > 0);
        }

        /**
         * PR13 - 感染性疾病手术，伴合并症或并发症
         * 条件：多诊断 + (MCC 或 CC)
         */
        PR13_group(record) {
            return record.zdList.length > 1 && 
                   (record.mccList.length > 0 || record.ccList.length > 0);
        }

        /**
         * PS43 - 感染性疾病特殊手术4，伴合并症或并发症
         * 条件：多诊断 + (MCC 或 CC)
         */
        PS43_group(record) {
            return record.zdList.length > 1 && 
                   (record.mccList.length > 0 || record.ccList.length > 0);
        }

        /**
         * PU13 - 感染性疾病泌尿手术，伴合并症或并发症
         * 条件：多诊断 + (MCC 或 CC)
         */
        PU13_group(record) {
            return record.zdList.length > 1 && 
                   (record.mccList.length > 0 || record.ccList.length > 0);
        }

        /**
         * PK15 - 感染性疾病肾脏手术，不伴合并症或并发症
         */
        PK15_group(record) {
            return true; // 简单分组
        }

        /**
         * PS35 - 感染性疾病特殊手术3，不伴合并症或并发症
         */
        PS35_group(record) {
            return true; // 简单分组
        }

        /**
         * PU15 - 感染性疾病泌尿手术，不伴合并症或并发症
         */
        PU15_group(record) {
            return true; // 简单分组
        }

        /**
         * PV15 - 感染性疾病血管手术，不伴合并症或并发症
         */
        PV15_group(record) {
            return true; // 简单分组
        }

        // ==================== Y类 - 儿科疾病 ====================

        /**
         * YC11 - 儿科循环系统疾病，伴严重合并症
         * 条件：多诊断 + MCC
         */
        YC11_group(record) {
            return record.zdList.length > 1 && record.mccList.length > 0;
        }

        /**
         * YR11 - 儿科呼吸系统疾病，伴严重合并症
         * 条件：多诊断 + MCC
         */
        YR11_group(record) {
            return record.zdList.length > 1 && record.mccList.length > 0;
        }

        /**
         * YC13 - 儿科循环系统疾病，伴合并症或并发症
         * 条件：多诊断 + (MCC 或 CC)
         */
        YC13_group(record) {
            return record.zdList.length > 1 && 
                   (record.mccList.length > 0 || record.ccList.length > 0);
        }

        // ==================== Z类 - 其他疾病 ====================

        /**
         * ZD19 - 其他诊断相关疾病
         */
        ZD19_group(record) {
            return true; // 简单分组
        }

        /**
         * ZZ11 - 其他疾病，伴严重合并症
         * 条件：多诊断 + MCC
         */
        ZZ11_group(record) {
            return record.zdList.length > 1 && record.mccList.length > 0;
        }

        /**
         * ZZ13 - 其他疾病，伴合并症或并发症
         * 条件：多诊断 + (MCC 或 CC)
         */
        ZZ13_group(record) {
            return record.zdList.length > 1 && 
                   (record.mccList.length > 0 || record.ccList.length > 0);
        }

        /**
         * ZZ15 - 其他疾病，不伴合并症或并发症
         */
        ZZ15_group(record) {
            return true; // 简单分组
        }

        // ==================== M类 - 新生儿疾病 ====================

        /**
         * MA13 - 新生儿疾病A类，伴合并症或并发症
         * 条件：多诊断 + (MCC 或 CC)
         */
        MA13_group(record) {
            return record.zdList.length > 1 && 
                   (record.mccList.length > 0 || record.ccList.length > 0);
        }

        /**
         * MS13 - 新生儿手术，伴合并症或并发症
         * 条件：多诊断 + (MCC 或 CC)
         */
        MS13_group(record) {
            return record.zdList.length > 1 && 
                   (record.mccList.length > 0 || record.ccList.length > 0);
        }

        /**
         * MZ13 - 新生儿其他疾病，伴合并症或并发症
         * 条件：多诊断 + (MCC 或 CC)
         */
        MZ13_group(record) {
            return record.zdList.length > 1 && 
                   (record.mccList.length > 0 || record.ccList.length > 0);
        }

        /**
         * MA15 - 新生儿疾病A类，不伴合并症或并发症
         */
        MA15_group(record) {
            return true; // 简单分组
        }

        /**
         * MS15 - 新生儿手术，不伴合并症或并发症
         */
        MS15_group(record) {
            return true; // 简单分组
        }
    }

    /**
     * DRG分组引擎
     */
    class DrgGroupingEngine {
        constructor() {
            this.rules = new DrgGroupingRules();
            this.ruleOrder = [
                // 按优先级排序
                { name: 'AA19', priority: 1, description: '心肺移植' },
                { name: 'AF19', priority: 1, description: '肺移植' },
                { name: 'PS31', priority: 2, description: '感染性疾病复杂手术，伴严重合并症' },
                { name: 'PS21', priority: 3, description: '感染性疾病特殊手术，伴严重合并症' },
                { name: 'PV11', priority: 4, description: '感染性疾病血管手术，伴严重合并症' },
                { name: 'PR11', priority: 5, description: '感染性疾病手术，伴严重合并症' },
                { name: 'PS43', priority: 6, description: '感染性疾病特殊手术4，伴合并症或并发症' },
                { name: 'PU13', priority: 7, description: '感染性疾病泌尿手术，伴合并症或并发症' },
                { name: 'PR13', priority: 8, description: '感染性疾病手术，伴合并症或并发症' },
                { name: 'YR11', priority: 8, description: '儿科呼吸系统疾病，伴严重合并症' },
                { name: 'MS13', priority: 8, description: '新生儿手术，伴合并症或并发症' },
                { name: 'PB11', priority: 10, description: '感染性疾病，伴严重合并症' },
                { name: 'YC11', priority: 10, description: '儿科循环系统疾病，伴严重合并症' },
                { name: 'PD13', priority: 12, description: '感染性疾病诊断，伴合并症或并发症' },
                { name: 'MA13', priority: 12, description: '新生儿疾病A类，伴合并症或并发症' },
                { name: 'PB13', priority: 15, description: '感染性疾病，伴合并症或并发症' },
                { name: 'YC13', priority: 15, description: '儿科循环系统疾病，伴合并症或并发症' },
                { name: 'MZ13', priority: 15, description: '新生儿其他疾病，伴合并症或并发症' },
                { name: 'PK15', priority: 20, description: '感染性疾病肾脏手术，不伴合并症或并发症' },
                { name: 'MS15', priority: 20, description: '新生儿手术，不伴合并症或并发症' },
                { name: 'PS35', priority: 25, description: '感染性疾病特殊手术3，不伴合并症或并发症' },
                { name: 'MA15', priority: 25, description: '新生儿疾病A类，不伴合并症或并发症' },
                { name: 'PU15', priority: 30, description: '感染性疾病泌尿手术，不伴合并症或并发症' },
                { name: 'PV15', priority: 35, description: '感染性疾病血管手术，不伴合并症或并发症' },
                { name: 'ZZ11', priority: 40, description: '其他疾病，伴严重合并症' },
                { name: 'ZZ13', priority: 45, description: '其他疾病，伴合并症或并发症' },
                { name: 'ZD19', priority: 50, description: '其他诊断相关疾病' },
                { name: 'ZZ15', priority: 60, description: '其他疾病，不伴合并症或并发症' }
            ];
        }

        /**
         * 执行DRG分组
         */
        performGrouping(recordString) {
            try {
                // 解析病案记录
                const record = createMedicalRecord(recordString);
                
                // 数据验证
                if (!this.validateRecord(record)) {
                    return createGroupingResult('QY00', '检查失败', '数据验证失败', ['数据格式不正确']);
                }

                // 识别MCC和CC
                identifyMccAndCc(record);

                // 按优先级尝试匹配规则
                for (const ruleInfo of this.ruleOrder) {
                    const methodName = ruleInfo.name + '_group';
                    if (this.rules[methodName] && this.rules[methodName](record)) {
                        return createGroupingResult(
                            ruleInfo.name,
                            '成功',
                            ruleInfo.description,
                            [`符合${ruleInfo.name}入组条件`, `匹配规则：${ruleInfo.description}`]
                        );
                    }
                }

                // 没有匹配的规则
                return createGroupingResult('QY00', '无法入组', '未找到匹配的分组规则', []);

            } catch (error) {
                return createGroupingResult('QY00', '检查失败', '病案信息解析出错', [error.message]);
            }
        }

        /**
         * 验证病案记录
         */
        validateRecord(record) {
            if (!record.index) return false;
            if (!record.gender || !['M', 'F'].includes(record.gender)) return false;
            if (record.age < 0 || record.age > 150) return false;
            if (!record.zdList || record.zdList.length === 0) return false;
            return true;
        }
    }

    // 导出API
    exports.group_record = function(recordString) {
        const engine = new DrgGroupingEngine();
        return engine.performGrouping(recordString);
    };

    exports.createMedicalRecord = createMedicalRecord;
    exports.DrgGroupingEngine = DrgGroupingEngine;

})(typeof module !== 'undefined' ? module.exports : window.DRG = {});
