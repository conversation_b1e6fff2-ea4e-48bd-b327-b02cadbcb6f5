server:
  port: 80
  error:
    path: /error
zk:
  homepage: sys/index
  zul-view-resolver-prefix: /page
  zul-view-resolver-suffix: .zul
  update-uri: /zw/url
  resource-uri: /zw/url/res
spring:
  datasource:
    master:
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      url: ****************************************************************************************************************************************************
      username: root
      password: GljP3viNtL0rYXEH
      # 连接池基本配置
      minIdle: 10
      maxActive: 100
      initialSize: 10
      # 减少获取连接等待时间
      maxWait: 5000
      # 优化连接检测参数
      timeBetweenEvictionRunsMillis: 30000
      minEvictableIdleTimeMillis: 600000
      # 启用PSCache并指定大小
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 100
      # 启用连接泄漏检测
      remove-abandoned: true
      remove-abandoned-timeout: 300
      log-abandoned: true
      # 合并多个DruidDataSource的监控数据
      useGlobalDataSourceStat: true
      # 启用监控统计功能
      testOnBorrow: true
      testOnReturn: false
      testWhileIdle: true
      validationQuery: select 1
      register-mbeans: true
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin
    slave:
      driver-class-name: org.mariadb.jdbc.Driver
      url: ****************************************************************************************************************************************************
      username: root
      password: GljP3viNtL0rYXEH

      testWhileIdle: true
      validationQuery: select 1 from dual
#测试基础配置
base:
  path:
  webUrl: http://localhost:8080
  resourceHandler: /files/**
  resourceLocation: c:/yzjw/files/
  developPath: C:\yzjw\chromedriver\chromedriver.exe
  reportUrl: http://**************:10004/report/ureport/preview?_u=mysql-
  dataJsonUrl: C:/yzjw/apache-tomcat-9.0.30/webapps/ROOT/data
#统一权限配置
config:
  oauth-url: login
  syn-url: http://***************:8077/zlstddService

