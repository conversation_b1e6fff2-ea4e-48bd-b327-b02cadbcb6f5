<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<recwnd id="proc_user" height="auto" maximizable="false" saveExit="true"  use="com.cisdi.ui.ProcUserRecWnd" width="60%" disable="false">
    <grid>
        <columns>
            <column width="80px"/>
            <column/>
            <column width="80px"/>
            <column/>
            <column width="80px"/>
            <column/>
            <column width="80px"/>
            <column/>
        </columns>
        <rows>
            <row spans="1,1,1,1,1,1,1,1">
                <label value="客户单位"/>
                <textbox bind="cisdi_problem.customerName" disabled="true"/>
                <label value="项目名称"/>
                <textbox bind="cisdi_problem.projectName" disabled="true"/>
                <label value="关联项目"/>
                <textbox bind="cisdi_problem.relaProName" disabled="true"/>
                <label value="关联编号"/>
                <intbox bind="cisdi_problem.relaProNo" disabled="true"/>
            </row>
            <row spans="1,1,1,1,1,1,1,1">
                <label value="联系人"/>
                <textbox bind="cisdi_problem.contactName" disabled="true"/>
                <label value="联系电话"/>
                <textbox bind="cisdi_problem.contactPhone" disabled="true"/>
                <label value="联系邮箱"/>
                <textbox bind="cisdi_problem.contactEmail" disabled="true"/>
                <label value="工厂"/>
                <textbox bind="cisdi_problem.factory" disabled="true"/>
            </row>
            <row spans="1,7">
                <label value="反馈内容"/>
                <textbox Multiline="true" bind="cisdi_problem.problemDesc" disabled="true" height="80px"/>
            </row>
            <row spans="1,7">
                <label value="处理结果"/>
                <textbox Multiline="true" bind="cisdi_problem.processResult" height="80px" require="true"/>
            </row>
            <row spans="1,7" height="80px">
                <label value="处理文件" tooltip="可上传与业主沟通记录"/>
                <file bind="cisdi_problem.processFiles" multiple="true"/>
            </row>
            <row spans="8" style="text-align:center">
                <radiogroup id="procResult">
                    <radio label="问题处理完成" style="padding-right:100px"/>
                    <radio label="问题没处理完成"/>
                </radiogroup>
            </row>
            <row spans="8">
                <div style="text-align:center">
                    <button label="提交" onClick="proc_user.save()"/>
                    <space width="100px"/>
                    <button label="取消" onClick="proc_user.detach()"/>
                </div>
            </row>
        </rows>
    </grid>
</recwnd>
