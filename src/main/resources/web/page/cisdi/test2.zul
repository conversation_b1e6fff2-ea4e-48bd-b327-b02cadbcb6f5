<?xml version="1.0" encoding="UTF-8"?>
<zk xmlns:h="http://www.w3.org/1999/xhtml" xmlns:w="http://www.zkoss.org/2005/zk/client">
    <style>
        .mobile-container {
            max-width: 100%;
            margin: 0 auto;
            padding: 16px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            min-height: 100vh;
        }

        .section-card {
            background: white;
            border-radius: 8px;
            margin-bottom: 16px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 16px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 8px;
        }

        .form-item {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background-color: #f8f8f8;
            box-sizing: border-box;
        }

        .form-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background-color: #f8f8f8;
            min-height: 80px;
            resize: vertical;
            box-sizing: border-box;
        }

        .editable-textarea {
            background-color: white;
            min-height: 120px;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .contact-label {
            min-width: 60px;
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .contact-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background-color: #f8f8f8;
        }

        .required {
            color: #ff4444;
        }

        .attachment-section {
            border: 1px dashed #ddd;
            border-radius: 4px;
            padding: 20px;
            text-align: center;
            background-color: #fafafa;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .attachment-section:hover {
            border-color: #1890ff;
            background-color: #f0f8ff;
        }

        .attachment-icon {
            font-size: 24px;
            color: #1890ff;
            margin-bottom: 8px;
        }

        .attachment-text {
            font-size: 14px;
            color: #666;
        }

        .file-list {
            margin-top: 12px;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background-color: #f5f5f5;
            border-radius: 4px;
            margin-bottom: 8px;
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-size: 14px;
            color: #333;
            margin-bottom: 2px;
        }

        .file-size {
            font-size: 12px;
            color: #666;
        }

        .file-remove {
            background: none;
            border: none;
            color: #ff4444;
            cursor: pointer;
            font-size: 16px;
            padding: 4px;
        }

        .file-remove:hover {
            color: #ff6666;
        }

        .radio-group {
            display: flex;
            gap: 20px;
            margin-top: 12px;
            justify-content: center;
            align-items: center;
        }

        .radio-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: #333;
        }

        .radio-item input[type="radio"] {
            margin: 0;
        }

        .button-container {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 12px;
            z-index: 1000;
            background: white;
            padding: 12px 20px;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .btn {
            padding: 12px 32px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            min-width: 100px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #40a9ff;
        }

        .btn-secondary {
            background-color: white;
            color: #666;
            border: 1px solid #ddd;
        }

        .btn-secondary:hover {
            background-color: #f5f5f5;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .mobile-container {
                padding: 12px;
            }

            .section-card {
                padding: 16px;
                margin-bottom: 12px;
            }

            .section-title {
                font-size: 16px;
            }

            .form-label, .contact-label {
                font-size: 13px;
            }

            .form-input, .form-textarea, .contact-input {
                font-size: 13px;
                padding: 10px;
            }

            .btn {
                padding: 10px 24px;
                font-size: 14px;
                min-width: 80px;
            }

            .button-container {
                bottom: 10px;
                left: 10px;
                right: 10px;
                transform: none;
                width: calc(100% - 20px);
                padding: 10px 16px;
            }

            .radio-group {
                flex-direction: column;
                gap: 12px;
                align-items: center;
            }
        }

        /* 小屏幕适配 */
        @media (max-width: 480px) {
            .mobile-container {
                padding: 8px;
            }

            .section-card {
                padding: 12px;
            }

            .contact-item {
                flex-direction: column;
                align-items: stretch;
                gap: 4px;
            }

            .contact-label {
                min-width: auto;
            }

            .attachment-section {
                padding: 16px;
            }
        }
    </style>

    <div id="mainContainer" class="mobile-container" use="com.cisdi.ui.Test2Div"
         onLoadFormData="self.loadFormData(event)"
         onFileUpload="self.onFileUpload(event)"
         onRemoveFile="self.removeFile(event)"
         onSubmitForm="self.submitForm(event)"
         onGetUploadedFiles="self.getUploadedFiles(event)">
        <!-- 问卷结果部分 -->
        <div class="section-card">
            <div class="section-title">问卷结果</div>

            <div class="form-item">
                <h:span class="form-label"><h:span class="required">*</h:span>1.客户单位名称：</h:span>
                <textbox id="customerNameBox" class="form-input" value="" readonly="true"/>
            </div>

            <div class="form-item">
                <h:span class="form-label"><h:span class="required">*</h:span>2.项目名称：</h:span>
                <textbox id="projectNameBox" class="form-input" value="" readonly="true"/>
            </div>

            <div class="form-item">
                <h:span class="form-label"><h:span class="required">*</h:span>3.需反馈/投诉的问题：</h:span>
                <textbox id="problemDescBox" class="form-textarea" multiline="true" rows="4" value="" readonly="true"/>
            </div>

            <div class="form-item">
                <label class="form-label">4.联系方式：</label>
                <div class="contact-grid">
                    <div class="contact-item">
                        <h:span class="contact-label"><h:span class="required">*</h:span>姓名：</h:span>
                        <textbox id="contactNameBox" class="contact-input" value="" readonly="true"/>
                    </div>
                    <div class="contact-item">
                        <h:span class="contact-label"><h:span class="required">*</h:span>电话：</h:span>
                        <textbox id="contactPhoneBox" class="contact-input" value="" readonly="true"/>
                    </div>
                    <div class="contact-item">
                        <h:span class="contact-label"><h:span class="required">*</h:span>邮箱：</h:span>
                        <textbox id="contactEmailBox" class="contact-input" value="" readonly="true"/>
                    </div>
                    <div class="contact-item">
                        <h:span class="contact-label">QQ：</h:span>
                        <textbox id="contactQQBox" class="contact-input" value="" readonly="true"/>
                    </div>
                </div>
            </div>
        </div>

        <!-- 反馈处理部分 -->
        <div class="section-card">
            <div class="section-title">反馈处理</div>

            <div class="form-item">
                <h:span class="form-label"><h:span class="required">*</h:span>处理结果：</h:span>
                <textbox id="processResultBox" class="form-textarea editable-textarea" multiline="true" rows="6" placeholder="请输入处理结果..."/>
            </div>

            <div class="form-item">
                <label class="form-label">附件：</label>
                <div class="attachment-section" w:onClick="triggerFileUpload()">
                    <div class="attachment-icon">+</div>
                    <div class="attachment-text">点击上传或拖拽文件到此区域</div>
                </div>
                <div class="file-list" id="fileList">
                    <!-- 文件列表将通过JavaScript动态生成 -->
                </div>
                <!-- 文件上传组件 -->
                <div style="margin-top: 10px;">
                    <button id="fileUploadBtn" upload="true,maxsize=-1,multiple=true"
                            onUpload="Events.postEvent('onFileUpload', mainContainer, event)"
                            class="btn btn-secondary" style="font-size: 12px; padding: 6px 12px;">
                        选择文件
                    </button>
                </div>
            </div>

            <div class="form-item">
                <div class="radio-group">
                    <div class="radio-item">
                        <radio name="processStatus" value="completed" checked="true"/>
                        <label>问题处理完成</label>
                    </div>
                    <div class="radio-item">
                        <radio name="processStatus" value="incomplete"/>
                        <label>问题处理未完成</label>
                    </div>
                </div>
            </div>

            <!-- 按钮区域 -->
            <div class="button-container">
                <button class="btn btn-primary" onClick="submitForm()">提交</button>
                <button class="btn btn-secondary" onClick="goBack()">返回</button>
            </div>
        </div>
    </div>

    <script><![CDATA[
        // 页面加载完成后从后台加载数据
        window.addEventListener('load', function() {
            // 调用后台方法加载表单数据
            zk.Widget.$('$mainContainer').fire('onLoadFormData', null);
        });

        // 加载表单数据
        function loadFormData(data) {
            if (data) {
                // 使用ZK组件的方式来设置值
                try {
                    if (zk.Widget.$('$customerNameBox')) zk.Widget.$('$customerNameBox').setValue(data.customerName);
                    if (zk.Widget.$('$projectNameBox')) zk.Widget.$('$projectNameBox').setValue(data.projectName);
                    if (zk.Widget.$('$problemDescBox')) zk.Widget.$('$problemDescBox').setValue(data.problemDesc);
                    if (zk.Widget.$('$contactNameBox')) zk.Widget.$('$contactNameBox').setValue(data.contactName);
                    if (zk.Widget.$('$contactPhoneBox')) zk.Widget.$('$contactPhoneBox').setValue(data.contactPhone);
                    if (zk.Widget.$('$contactEmailBox')) zk.Widget.$('$contactEmailBox').setValue(data.contactEmail);
                    if (zk.Widget.$('$contactQQBox')) zk.Widget.$('$contactQQBox').setValue(data.contactQQ);
                } catch (e) {
                    console.log('Error loading form data:', e);
                }
            }
        }

        // 触发文件上传
        function triggerFileUpload() {
            try {
                // 查找上传按钮并点击
                var buttons = document.querySelectorAll('button[upload]');
                for (var i = 0; i < buttons.length; i++) {
                    if (buttons[i].getAttribute('upload')) {
                        buttons[i].click();
                        return;
                    }
                }

                // 如果找不到，提示用户点击按钮
                alert('请点击下方的"选择文件"按钮上传文件');
            } catch (e) {
                console.log('Error triggering file upload:', e);
                alert('请点击下方的"选择文件"按钮上传文件');
            }
        }

        // 更新文件列表
        function updateFileList(files) {
            var fileList = document.getElementById('fileList');
            if (!fileList) return;

            fileList.innerHTML = '';
            if (files && files.length > 0) {
                files.forEach(function(file) {
                    var fileItem = document.createElement('div');
                    fileItem.className = 'file-item';
                    fileItem.innerHTML =
                        '<div class="file-info">' +
                        '<div class="file-name">' + file.fileName + '</div>' +
                        '<div class="file-size">' + formatFileSize(file.fileSize) + '</div>' +
                        '</div>' +
                        '<button class="file-remove" onclick="removeFile(\'' + file.fileName + '\')">×</button>';
                    fileList.appendChild(fileItem);
                });
            }
        }

        // 删除文件
        function removeFile(fileName) {
            zk.Widget.$('$mainContainer').fire('onRemoveFile', fileName);
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            var k = 1024;
            var sizes = ['Bytes', 'KB', 'MB', 'GB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 收集表单数据
        function collectFormData() {
            try {
                return {
                    customerName: zk.Widget.$('$customerNameBox') ? zk.Widget.$('$customerNameBox').getValue() : '',
                    projectName: zk.Widget.$('$projectNameBox') ? zk.Widget.$('$projectNameBox').getValue() : '',
                    problemDesc: zk.Widget.$('$problemDescBox') ? zk.Widget.$('$problemDescBox').getValue() : '',
                    contactName: zk.Widget.$('$contactNameBox') ? zk.Widget.$('$contactNameBox').getValue() : '',
                    contactPhone: zk.Widget.$('$contactPhoneBox') ? zk.Widget.$('$contactPhoneBox').getValue() : '',
                    contactEmail: zk.Widget.$('$contactEmailBox') ? zk.Widget.$('$contactEmailBox').getValue() : '',
                    contactQQ: zk.Widget.$('$contactQQBox') ? zk.Widget.$('$contactQQBox').getValue() : '',
                    processResult: zk.Widget.$('$processResultBox') ? zk.Widget.$('$processResultBox').getValue() : '',
                    processStatus: getSelectedRadioValue('processStatus')
                };
            } catch (e) {
                console.log('Error collecting form data:', e);
                return {};
            }
        }

        // 获取选中的单选按钮值
        function getSelectedRadioValue(name) {
            var radios = document.getElementsByName(name);
            for (var i = 0; i < radios.length; i++) {
                if (radios[i].checked) {
                    return radios[i].value;
                }
            }
            return '';
        }
    ]]></script>

    <zscript><![CDATA[
        void submitForm() {
            // 收集表单数据并调用后台方法
            Clients.evalJavaScript(
                "var formData = collectFormData();" +
                "zk.Widget.$('$mainContainer').fire('onSubmitForm', formData);"
            );
        }

        void goBack() {
            // 返回逻辑
            Executions.sendRedirect("previous-page.zul");
        }

        void loadFormData() {
            // 调用后台方法加载表单数据
            Events.postEvent("onLoadFormData", mainContainer, null);
        }
    ]]></zscript>
</zk>
