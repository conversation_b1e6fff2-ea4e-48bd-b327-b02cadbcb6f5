<?xml version="1.0" encoding="UTF-8"?>
<?link href="~./static/css/drg_index.css" rel="stylesheet" type="text/css"?>
<?link href="~./static/temp/font-awesome.min.css" type="text/css" rel="stylesheet"?>
<zk xmlns:h="http://www.w3.org/1999/xhtml" xmlns:w="http://www.zkoss.org/2005/zk/client">
    <style>
        body {
        margin: 0;
        padding: 0;
        font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #667eea 100%);
        min-height: 100vh;
        }

        .container {
        padding: 0;
        margin: 0;
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #667eea 100%);
        }
        /* 顶部导航栏 */
        .header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        padding: 12px 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: white;
        }

        .logo-section {
        display: flex;
        align-items: center;
        gap: 12px;
        }

        .logo-icon {
        width: 32px;
        height: 32px;
        background: white;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: #667eea;
        }

        .logo-text {
        font-size: 20px;
        font-weight: 600;
        letter-spacing: 1px;
        }

        .header-actions {
        display: flex;
        align-items: center;
        gap: 20px;
        }

        .user-info {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        }

        .user-avatar {
        width: 28px;
        height: 28px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        }


        /* 右侧详情面板 */
        .detail-panel {
        border: 1px solid #e5e5ea;
        border-radius: 12px;
        padding: 24px;
        background: white;
        margin-right:10px;
        margin-top:10px;
        margin-bottom:10px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        }

        .detail-panel:hover {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }

        .detail-title {
        font-size: 18px;
        font-weight: 700;
        color: #1d1d1f;
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 2px solid #667eea;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        }

        .detail-item {
        margin-bottom: 20px;
        padding: 12px;
        border-radius: 8px;
        background: #f8f9fa;
        transition: background 0.3s ease;
        }

        .detail-item:hover {
        background: #e9ecef;
        }

        .detail-label {
        font-size: 13px;
        color: #6c757d;
        margin-bottom: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        }

        .detail-value {
        font-size: 14px;
        color: #1d1d1f;
        word-break: break-all;
        line-height: 1.5;
        font-weight: 500;
        }

        .detail-result {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        padding: 20px;
        border-radius: 12px;
        margin-top: 20px;
        border-left: 4px solid #667eea;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        font-size: 14px;
        line-height: 1.6;
        color: #495057;
        }

        /* 可点击单元格样式 */
        .clickable-cell {
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.3s ease;
        display: inline-block;
        position: relative;
        }

        .clickable-cell:hover {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
        }

        .clickable-cell::after {
        content: '✏️';
        position: absolute;
        right: -2px;
        top: -2px;
        font-size: 10px;
        opacity: 0;
        transition: opacity 0.3s ease;
        }

        .clickable-cell:hover::after {
        opacity: 1;
        }

    </style>
    <recwnd vflex="1" use="com.cisdi.ui.MainDiv">
        <div class="container" vflex="1">
            <h:div class="header">
                <h:div class="logo-section">
                    <h:div class="logo-icon">🏢</h:div>
                    <h:div class="logo-text">赛迪轻呈</h:div>
                </h:div>
                <h:div class="header-actions">
                    <h:div class="user-info">
                        <h:span>中文</h:span>
                        <h:span>|</h:span>
                        <h:span>设置</h:span>
                    </h:div>
                    <h:div class="user-avatar">👤</h:div>
                </h:div>
            </h:div>
            <div style="display: grid; grid-template-columns: 1fr 330px;" vflex="1" id="mainDiv">
                <!-- 左侧详情面板 -->
                <div id="left" vflex="1" style="grid-template-rows: 130px 1fr;display: grid;padding: 0 10px 10px 10px;">
                    <div id="cardDiv"/>
                    <listwnd band="problem" vflex="1" use="com.cisdi.ui.ProbleListWnd" changeSave="true" toolItems="export"/>
                </div>
                <!-- 右侧详情面板 -->
                <div class="detail-panel" id="detail-panel">
                    <h:div class="detail-title"
                           style="display: flex;align-items: center;justify-content: space-between;">
                        <h:div class="detail-value" style="font-size: 18px;">反馈问题详情</h:div>
                        <a iconSclass="z-icon-times" id="detailClose"/>
                    </h:div>
                    <h:div class="detail-item">
                        <h:div class="detail-label">1.客户单位名称:</h:div>
                        <h:div class="detail-value">XXXXX</h:div>
                    </h:div>
                    <h:div class="detail-item">
                        <h:div class="detail-label">2.项目名称:</h:div>
                        <h:div class="detail-value">XXXXX</h:div>
                    </h:div>
                    <h:div class="detail-item">
                        <h:div class="detail-label">3.反馈问题描述:</h:div>
                        <h:div class="detail-value">XXXXXXXX</h:div>
                    </h:div>
                    <h:div class="detail-item">
                        <h:div class="detail-label">4.联系方式:</h:div>
                        <h:div class="detail-value">
                            姓名：XXXXX
                            <h:br/>
                            电话：XXXXX
                            <h:br/>
                            邮箱：XXXXX
                            <h:br/>
                            单位：XXXXX
                        </h:div>
                    </h:div>
                    <h:div class="detail-item">
                        <h:div class="detail-label">处理结果</h:div>
                        <h:div class="detail-result">
                            XXXXXXXX
                        </h:div>
                    </h:div>
                </div>
            </div>
        </div>
    </recwnd>
    <script>
        <![CDATA[
        function updateDetailPanel(data) {

        var customerUnitElement =document.querySelector('.detail-item:nth-of-type(2) .detail-value');
        if (customerUnitElement) {
              customerUnitElement.textContent = data.customerName;
        }

        // 更新项目名称
        var projectNameElement = document.querySelector('.detail-item:nth-of-type(3) .detail-value');
        if (projectNameElement) {
              projectNameElement.textContent = data.projectName;
        }

        // 更新问题描述
        var problemElement = document.querySelector('.detail-item:nth-of-type(4) .detail-value')
        if (problemElement) {
             problemElement.textContent = data.problemDesc;
        }

        // 更新联系方式
        var contactElement = document.querySelector('.detail-item:nth-of-type(5) .detail-value');
        if (contactElement) {
        contactElement.innerHTML =
             '姓名：' + data.contactName + '<br/>' +
             '电话：' + data.contactPhone + '<br/>' +
             '邮箱：' + data.contactEmail + '<br/>' +
             '工厂：' + data.factory;
        }

        // 更新处理结果
        var resultElement = document.querySelector('.detail-result');
        if (resultElement) {
           resultElement.textContent = data.processResult;
        }

        // 添加项目编码信息
        var titleElement = document.querySelector('.detail-value');
        if (titleElement) {
            titleElement.textContent = '反馈详情 - ' + data.relaProNo;
          }
        }
]]></script>
</zk>
