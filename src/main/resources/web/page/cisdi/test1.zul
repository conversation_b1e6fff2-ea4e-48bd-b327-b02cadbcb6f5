<?xml version="1.0" encoding="UTF-8"?>
<zk xmlns:h="http://www.w3.org/1999/xhtml" xmlns:w="http://www.zkoss.org/2005/zk/client">
    <style>
        .mobile-container {
            max-width: 100%;
            margin: 0 auto;
            padding: 16px;
            padding-bottom: 100px; /* 为浮动按钮留出空间 */
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            min-height: 100vh;
        }

        .section-card {
            background: white;
            border-radius: 8px;
            margin-bottom: 16px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 16px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 8px;
        }

        .form-item {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background-color: #f8f8f8;
            box-sizing: border-box;
        }

        .form-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background-color: #f8f8f8;
            min-height: 80px;
            resize: vertical;
            box-sizing: border-box;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .contact-label {
            min-width: 60px;
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .contact-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background-color: #f8f8f8;
        }

        .required {
            color: #ff4444;
        }

        .select-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background-color: white;
            box-sizing: border-box;
            cursor: pointer;
            position: relative;
        }

        .select-input:after {
            content: '▼';
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 12px;
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            padding: 16px 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            color: #999;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
        }

        .modal-body {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .search-box {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 16px;
            font-size: 14px;
        }

        .option-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .option-item {
            padding: 12px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .option-item:hover {
            background-color: #f5f5f5;
        }

        .option-item:last-child {
            border-bottom: none;
        }

        .option-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }

        .option-code {
            font-size: 12px;
            color: #666;
        }

        .button-container {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 12px;
            z-index: 1000;
            background: white;
            padding: 12px 20px;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .btn {
            padding: 12px 32px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            min-width: 100px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #40a9ff;
        }

        .btn-secondary {
            background-color: white;
            color: #666;
            border: 1px solid #ddd;
        }

        .btn-secondary:hover {
            background-color: #f5f5f5;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .mobile-container {
                padding: 12px;
                padding-bottom: 80px; /* 移动端为浮动按钮留出空间 */
            }

            .section-card {
                padding: 16px;
                margin-bottom: 12px;
            }

            .section-title {
                font-size: 16px;
            }

            .form-label, .contact-label {
                font-size: 13px;
            }

            .form-input, .form-textarea, .contact-input {
                font-size: 13px;
                padding: 10px;
            }

            .btn {
                padding: 10px 24px;
                font-size: 14px;
                min-width: 80px;
            }

            .button-container {
                bottom: 10px;
                left: 10px;
                right: 10px;
                transform: none;
                width: calc(100% - 20px);
                padding: 10px 16px;
            }

            .select-input {
                font-size: 13px;
                padding: 10px;
            }

            .modal-content {
                width: 95%;
                max-height: 85vh;
            }

            .modal-header {
                padding: 12px 16px;
            }

            .modal-title {
                font-size: 14px;
            }

            .modal-body {
                padding: 16px;
            }

            .search-box {
                font-size: 13px;
                padding: 8px;
            }

            .option-item {
                padding: 10px;
            }

            .option-name {
                font-size: 13px;
            }

            .option-code {
                font-size: 11px;
            }
        }

        /* 小屏幕适配 */
        @media (max-width: 480px) {
            .mobile-container {
                padding: 8px;
                padding-bottom: 80px; /* 小屏幕为浮动按钮留出空间 */
            }

            .section-card {
                padding: 12px;
            }

            .contact-item {
                flex-direction: column;
                align-items: stretch;
                gap: 4px;
            }

            .contact-label {
                min-width: auto;
            }

            .modal-content {
                width: 98%;
                max-height: 90vh;
            }

            .modal-header {
                padding: 10px 12px;
            }

            .modal-body {
                padding: 12px;
            }
        }
    </style>

    <div id="mainContainer" class="mobile-container" use="com.cisdi.ui.Test1Div">
        <!-- 问卷结果部分 -->
        <div class="section-card">
            <div class="section-title">问卷结果</div>

            <div class="form-item">
                <h:span class="form-label"><h:span class="required">*</h:span>1.客户单位名称：</h:span>
                <textbox class="form-input" value="xxxxx" readonly="true"/>
            </div>

            <div class="form-item">
                <h:span class="form-label"><h:span class="required">*</h:span>2.项目名称：</h:span>
                <textbox class="form-input" value="xxxxx" readonly="true"/>
            </div>

            <div class="form-item">
                <h:span class="form-label"><h:span class="required">*</h:span>3.需反馈/投诉的问题：</h:span>
                <textbox class="form-textarea" multiline="true" rows="4" value="xxxxxxxx" readonly="true"/>
            </div>

            <div class="form-item">
                <label class="form-label">4.联系方式：</label>
                <div class="contact-grid">
                    <div class="contact-item">
                        <h:span class="contact-label"><h:span class="required">*</h:span>姓名：</h:span>
                        <textbox class="contact-input" value="xxxxx" readonly="true"/>
                    </div>
                    <div class="contact-item">
                        <h:span class="contact-label"><h:span class="required">*</h:span>电话：</h:span>
                        <textbox class="contact-input" value="xxxxx" readonly="true"/>
                    </div>
                    <div class="contact-item">
                        <h:span class="contact-label"><h:span class="required">*</h:span>邮箱：</h:span>
                        <textbox class="contact-input" value="xxxxx" readonly="true"/>
                    </div>
                    <div class="contact-item">
                        <h:span class="contact-label">QQ：</h:span>
                        <textbox class="contact-input" value="xxxxx" readonly="true"/>
                    </div>
                </div>
            </div>
        </div>

        <!-- 反馈指派部分 -->
        <div class="section-card">
            <div class="section-title">反馈指派</div>

            <div class="form-item">
                <h:span class="form-label"><h:span class="required">*</h:span>项目：</h:span>
                <div class="select-input" w:onClick='openProjectModal()'>
                    <h:span id="projectDisplay">请选择项目</h:span>
                </div>
                <h:input type="hidden" id="projectId" />
                <h:input type="hidden" id="projectCode" />
            </div>

            <div class="form-item">
                <h:span class="form-label"><h:span class="required">*</h:span>责任人：</h:span>
                <div class="select-input" w:onClick="openResponsibleModal()">
                    <h:span id="responsibleDisplay">请选择责任人</h:span>
                </div>
                <h:input type="hidden" id="responsibleId" />
            </div>

            <div class="form-item">
                <label class="form-label">抄送人：</label>
                <div class="select-input" w:onClick="openCcModal()">
                    <h:span id="ccDisplay">请选择抄送人</h:span>
                </div>
                <h:input type="hidden" id="ccIds" />
            </div>

            <!-- 按钮区域 -->
            <div class="button-container">
                <button class="btn btn-primary" w:onClick="submitForm()">提交</button>
                <button class="btn btn-secondary" w:onClick="goBack()">返回</button>
            </div>
        </div>

        <!-- 项目选择弹窗 -->
        <h:div id="projectModal" class="modal-overlay" style="display: none;">
            <h:div class="modal-content">
                <h:div class="modal-header">
                    <h:div class="modal-title">选择项目</h:div>
                    <h:button class="modal-close" onclick="closeModal('projectModal')">×</h:button>
                </h:div>
                <h:div class="modal-body">
                    <h:input type="text" class="search-box" placeholder="搜索项目..." oninput="searchProjects(this.value)" />
                    <h:ul class="option-list" id="projectList">
                        <!-- 项目列表将通过JavaScript动态生成 -->
                    </h:ul>
                </h:div>
            </h:div>
        </h:div>

        <!-- 责任人选择弹窗 -->
        <h:div id="responsibleModal" class="modal-overlay" style="display: none;">
            <h:div class="modal-content">
                <h:div class="modal-header">
                    <h:div class="modal-title">选择责任人</h:div>
                    <h:button class="modal-close" onclick="closeModal('responsibleModal')">×</h:button>
                </h:div>
                <h:div class="modal-body">
                    <h:input type="text" class="search-box" placeholder="搜索责任人..." oninput="searchResponsible(this.value)" />
                    <h:ul class="option-list" id="responsibleList">
                        <!-- 责任人列表将通过JavaScript动态生成 -->
                    </h:ul>
                </h:div>
            </h:div>
        </h:div>

        <!-- 抄送人选择弹窗 -->
        <h:div id="ccModal" class="modal-overlay" style="display: none;">
            <h:div class="modal-content">
                <h:div class="modal-header">
                    <h:div class="modal-title">选择抄送人</h:div>
                    <h:button class="modal-close" onclick="closeModal('ccModal')">×</h:button>
                </h:div>
                <h:div class="modal-body">
                    <h:input type="text" class="search-box" placeholder="搜索抄送人..." oninput="searchCc(this.value)" />
                    <h:ul class="option-list" id="ccList">
                        <!-- 抄送人列表将通过JavaScript动态生成 -->
                    </h:ul>
                </h:div>
            </h:div>
        </h:div>
    </div>

    <script ><![CDATA[
        // 数据现在从后台获取，不再需要静态数据

        // 打开项目选择弹窗
        function openProjectModal() {
            var modal = document.getElementById('projectModal');
            if (modal) {
                modal.style.display = 'flex';
                // 调用后台方法获取项目数据
                 var container = zk.Widget.$(jq('$mainContainer'));
                 zAu.send(new zk.Event(container, 'onGetProjectList', null));
              } else {
                console.error('Project modal not found');
            }
        }

        // 打开责任人选择弹窗
        function openResponsibleModal() {
            var modal = document.getElementById('responsibleModal');
            if (modal) {
                modal.style.display = 'flex';
                // 调用后台方法获取责任人数据
                var container = zk.Widget.$(jq('$mainContainer'));
                zAu.send(new zk.Event(container, 'onGetResponsibleList',null));
            } else {
                console.error('Responsible modal not found');
            }
        }

        // 打开抄送人选择弹窗
        function openCcModal() {
            var modal = document.getElementById('ccModal');
            if (modal) {

                modal.style.display = 'flex';
                // 调用后台方法获取抄送人数据
                var container = zk.Widget.$(jq('$mainContainer'));
                zAu.send(new zk.Event(container, 'onGetCcList',null));
            } else {
                console.error('CC modal not found');
            }
        }

        // 关闭弹窗
        function closeModal(modalId) {
            var modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
            }
        }

        // 渲染项目列表
        function renderProjectList(data) {
            var list = document.getElementById('projectList');
            if (!list) {
                console.error('Project list not found');
                return;
            }
            list.innerHTML = '';
            data.forEach(function(item) {
                var li = document.createElement('li');
                li.className = 'option-item';
                li.innerHTML = '<div class="option-name">' + item.name + '</div><div class="option-code">编号：' + item.code + '</div>';
                li.onclick = function() {
                    selectProject(item);
                };
                list.appendChild(li);
            });
        }

        // 渲染责任人列表
        function renderResponsibleList(data) {
            var list = document.getElementById('responsibleList');
            if (!list) {
                console.error('Responsible list not found');
                return;
            }
            list.innerHTML = '';
            data.forEach(function(item) {
                var li = document.createElement('li');
                li.className = 'option-item';
                li.innerHTML = '<div class="option-name">' + item.name + '</div><div class="option-code">部门：' + item.department + '</div>';
                li.onclick = function() {
                    selectResponsible(item);
                };
                list.appendChild(li);
            });
        }

        // 渲染抄送人列表
        function renderCcList(data) {
            var list = document.getElementById('ccList');
            if (!list) {
                console.error('CC list not found');
                return;
            }
            list.innerHTML = '';
            data.forEach(function(item) {
                var li = document.createElement('li');
                li.className = 'option-item';
                li.innerHTML = '<div class="option-name">' + item.name + '</div><div class="option-code">部门：' + item.department + '</div>';
                li.onclick = function() {
                    selectCc(item);
                };
                list.appendChild(li);
            });
        }

        // 选择项目
        function selectProject(project) {
            var display = document.getElementById('projectDisplay');
            var idField = document.getElementById('projectId');
            var codeField = document.getElementById('projectCode');

            if (display) display.textContent = project.name;
            if (idField) idField.value = project.id;
            if (codeField) codeField.value = project.code;
            closeModal('projectModal');
        }

        // 选择责任人
        function selectResponsible(person) {
            var display = document.getElementById('responsibleDisplay');
            var idField = document.getElementById('responsibleId');

            if (display) display.textContent = person.name;
            if (idField) idField.value = person.id;
            closeModal('responsibleModal');
        }

        // 选择抄送人
        function selectCc(person) {
            var idsField = document.getElementById('ccIds');
            var display = document.getElementById('ccDisplay');

            if (!idsField || !display) {
                console.error('CC fields not found');
                return;
            }

            var currentIds = idsField.value;
            var currentDisplay = display.textContent;

            var newIds, newNames;
            if (currentIds && currentIds !== '') {
                newIds = currentIds + ',' + person.id;
                newNames = currentDisplay + ', ' + person.name;
            } else {
                newIds = person.id;
                newNames = person.name;
            }

            idsField.value = newIds;
            display.textContent = newNames;
            closeModal('ccModal');
        }

        // 搜索项目
        function searchProjects(keyword) {
            // 调用后台方法进行搜索
            zk.Widget.$('$mainContainer').fire('onGetProjectList', keyword);
        }

        // 搜索责任人
        function searchResponsible(keyword) {
            // 调用后台方法进行搜索
            zk.Widget.$('$mainContainer').fire('onGetResponsibleList', keyword);
        }

        // 搜索抄送人
        function searchCc(keyword) {
            // 调用后台方法进行搜索
            zk.Widget.$('$mainContainer').fire('onGetCcList', keyword);
        }

        // 点击弹窗外部关闭弹窗
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal-overlay')) {
                e.target.style.display = 'none';
            }
        });
    ]]></script>

    <zscript><![CDATA[
        void submitForm() {
            // 收集表单数据并调用后台方法
            Clients.evalJavaScript(
                "var formData = {" +
                "  projectId: document.getElementById('projectId').value," +
                "  projectCode: document.getElementById('projectCode').value," +
                "  responsibleId: document.getElementById('responsibleId').value," +
                "  ccIds: document.getElementById('ccIds').value" +
                "};" +
                "zk.Widget.$('$mainContainer').fire('onSubmitForm', formData);"
            );
        }

        void goBack() {
            // 返回逻辑
            Executions.sendRedirect("previous-page.zul");
        }
    ]]></zscript>
</zk>
