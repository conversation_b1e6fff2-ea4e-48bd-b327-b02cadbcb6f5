<?xml version="1.0" encoding="UTF-8"?>
<zk>
    <style>
        .mobile-container {
            max-width: 100%;
            margin: 0 auto;
            padding: 16px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            min-height: 100vh;
        }
        
        .section-card {
            background: white;
            border-radius: 8px;
            margin-bottom: 16px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 16px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 8px;
        }
        
        .form-item {
            margin-bottom: 16px;
        }
        
        .form-label {
            display: block;
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background-color: #f8f8f8;
            box-sizing: border-box;
        }
        
        .form-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background-color: #f8f8f8;
            min-height: 80px;
            resize: vertical;
            box-sizing: border-box;
        }
        
        .contact-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .contact-label {
            min-width: 60px;
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        
        .contact-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background-color: #f8f8f8;
        }
        
        .required {
            color: #ff4444;
        }
        
        .button-container {
            display: flex;
            gap: 12px;
            margin-top: 24px;
            justify-content: center;
        }
        
        .btn {
            padding: 12px 32px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            min-width: 100px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background-color: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #40a9ff;
        }
        
        .btn-secondary {
            background-color: white;
            color: #666;
            border: 1px solid #ddd;
        }
        
        .btn-secondary:hover {
            background-color: #f5f5f5;
        }
        
        /* 移动端适配 */
        @media (max-width: 768px) {
            .mobile-container {
                padding: 12px;
            }
            
            .section-card {
                padding: 16px;
                margin-bottom: 12px;
            }
            
            .section-title {
                font-size: 16px;
            }
            
            .form-label, .contact-label {
                font-size: 13px;
            }
            
            .form-input, .form-textarea, .contact-input {
                font-size: 13px;
                padding: 10px;
            }
            
            .btn {
                padding: 10px 24px;
                font-size: 14px;
                min-width: 80px;
            }
            
            .button-container {
                flex-direction: column;
                align-items: stretch;
            }
        }
        
        /* 小屏幕适配 */
        @media (max-width: 480px) {
            .mobile-container {
                padding: 8px;
            }
            
            .section-card {
                padding: 12px;
            }
            
            .contact-item {
                flex-direction: column;
                align-items: stretch;
                gap: 4px;
            }
            
            .contact-label {
                min-width: auto;
            }
        }
    </style>
    
    <div class="mobile-container">
        <!-- 问卷结果部分 -->
        <div class="section-card">
            <div class="section-title">问卷结果</div>
            
            <div class="form-item">
                <label class="form-label"><span class="required">*</span>1.客户单位名称：</label>
                <textbox class="form-input" value="xxxxx" readonly="true"/>
            </div>
            
            <div class="form-item">
                <label class="form-label"><span class="required">*</span>2.项目名称：</label>
                <textbox class="form-input" value="xxxxx" readonly="true"/>
            </div>
            
            <div class="form-item">
                <label class="form-label"><span class="required">*</span>3.需反馈/投诉的问题：</label>
                <textbox class="form-textarea" multiline="true" rows="4" value="xxxxxxxx" readonly="true"/>
            </div>
            
            <div class="form-item">
                <label class="form-label">4.联系方式：</label>
                <div class="contact-grid">
                    <div class="contact-item">
                        <span class="contact-label"><span class="required">*</span>姓名：</span>
                        <textbox class="contact-input" value="xxxxx" readonly="true"/>
                    </div>
                    <div class="contact-item">
                        <span class="contact-label"><span class="required">*</span>电话：</span>
                        <textbox class="contact-input" value="xxxxx" readonly="true"/>
                    </div>
                    <div class="contact-item">
                        <span class="contact-label"><span class="required">*</span>邮箱：</span>
                        <textbox class="contact-input" value="xxxxx" readonly="true"/>
                    </div>
                    <div class="contact-item">
                        <span class="contact-label">QQ：</span>
                        <textbox class="contact-input" value="xxxxx" readonly="true"/>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 反馈指派部分 -->
        <div class="section-card">
            <div class="section-title">反馈指派</div>
            
            <div class="form-item">
                <label class="form-label"><span class="required">*</span>项目：</label>
                <textbox class="form-input" placeholder="请输入项目名称"/>
            </div>
            
            <div class="form-item">
                <label class="form-label"><span class="required">*</span>责任人：</label>
                <textbox class="form-input" placeholder="请输入责任人"/>
            </div>
            
            <div class="form-item">
                <label class="form-label">抄送人：</label>
                <textbox class="form-input" placeholder="请输入抄送人"/>
            </div>
            
            <!-- 按钮区域 -->
            <div class="button-container">
                <button class="btn btn-primary" onClick="submitForm()">提交</button>
                <button class="btn btn-secondary" onClick="goBack()">返回</button>
            </div>
        </div>
    </div>
    
    <zscript><![CDATA[
        void submitForm() {
            // 提交表单逻辑
            Clients.showNotification("提交成功", "info", null, "top_center", 3000);
        }
        
        void goBack() {
            // 返回逻辑
            Executions.sendRedirect("previous-page.zul");
        }
    ]]></zscript>
</zk>
