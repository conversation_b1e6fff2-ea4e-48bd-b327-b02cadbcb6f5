<?xml version="1.0" encoding="UTF-8"?>
<?meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"?>
<?meta name="format-detection" content="telephone=no"?>
<?meta name="apple-mobile-web-app-capable" content="yes"?>
<?meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"?>

<zk xmlns:h="http://www.w3.org/1999/xhtml" xmlns:w="http://www.zkoss.org/2005/zk/client">
    <style>
        body {
        margin: 0;
        padding: 0;
        background: #f5f7fa;
        }

        .mobile-container {
        max-width: 420px;
        margin: 0 auto;
        background: #f5f7fa;
        min-height: 100vh;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft
        YaHei', sans-serif;
        }

        .header-banner {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        height: 140px;
        position: relative;
        display: flex;
        align-items: center;
        padding: 0 24px;
        color: white;
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
        }

        .logo-container {
        display: flex;
        align-items: center;
        gap: 16px;
        }

        .logo-icon {
        width: 56px;
        height: 56px;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        color: white;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo-text {
        font-size: 22px;
        font-weight: 600;
        line-height: 1.3;
        letter-spacing: 0.5px;
        }

        .form-container {
        padding: 32px 24px 200px 24px;
        background: #ffffff;
        margin: -20px 16px 0 16px;
        border-radius: 20px;
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.08);
        position: relative;
        z-index: 1;
        }

        .form-group {
        margin-bottom: 28px;
        }

        .form-label {
        display: block;
        font-size: 16px;
        color: #2c3e50;
        margin-bottom: 12px;
        font-weight: 600;
        letter-spacing: 0.3px;
        }

        .required {
        color: #e74c3c;
        margin-right: 6px;
        font-weight: bold;
        }

        .form-input {
        width: 100%;
        padding: 16px 20px;
        border: 2px solid #e8ecf0;
        border-radius: 12px;
        font-size: 16px;
        background: #ffffff;
        box-sizing: border-box;
        transition: all 0.3s ease;
        font-family: inherit;
        }

        .form-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        transform: translateY(-1px);
        }

        .form-textarea {
        width: 100%;
        padding: 16px 20px;
        border: 2px solid #e8ecf0;
        border-radius: 12px;
        font-size: 16px;
        background: #ffffff;
        box-sizing: border-box;
        resize: vertical;
        min-height: 140px;
        font-family: inherit;
        transition: all 0.3s ease;
        line-height: 1.6;
        }

        .form-textarea:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        transform: translateY(-1px);
        }

        .contact-section {
        border-top: 1px solid #f0f2f5;
        }

        .contact-title {
        font-size: 18px;
        color: #2c3e50;
        margin-bottom: 24px;
        font-weight: 600;
        letter-spacing: 0.3px;
        }

        .contact-row {
        display: flex;
        gap: 12px;
        margin-bottom: 20px;
        }

        .contact-item {
        flex: 1;
        min-width: 0;
        box-sizing: border-box;
        }

        .contact-label {
        display: block;
        font-size: 15px;
        color: #2c3e50;
        margin-bottom: 8px;
        font-weight: 600;
        letter-spacing: 0.2px;
        }

        .contact-input {
        width: 100%;
        padding: 14px 16px;
        border: 2px solid #e8ecf0;
        border-radius: 10px;
        font-size: 15px;
        background: #ffffff;
        box-sizing: border-box;
        transition: all 0.3s ease;
        font-family: inherit;
        min-width: 0;
        flex-shrink: 1;
        }

        .contact-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        transform: translateY(-1px);
        }

        .submit-container {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 20px 24px;
        padding-bottom: calc(20px + env(safe-area-inset-bottom));
        text-align: center;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(15px);
        border-top: 1px solid rgba(0, 0, 0, 0.08);
        box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.12);
        z-index: 1000;
        animation: slideUp 0.3s ease-out;
        }

        @keyframes slideUp {
        from {
        transform: translateY(100%);
        opacity: 0;
        }
        to {
        transform: translateY(0);
        opacity: 1;
        }
        }

        /* 验证码弹窗样式 */
        .verification-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 2000;
        display: none;
        align-items: center;
        justify-content: center;
        animation: fadeIn 0.3s ease-out;
        }

        .verification-overlay.show {
        display: flex;
        }

        .verification-modal {
        background: #ffffff;
        border-radius: 20px;
        padding: 40px 30px;
        margin: 20px;
        max-width: 400px;
        width: 90%;
        text-align: center;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        animation: modalSlideUp 0.4s ease-out;
        position: relative;
        }

        .verification-logo {
        width: 80px;
        height: 80px;
        margin: 0 auto 30px auto;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 32px;
        }

        .verification-title {
        font-size: 18px;
        color: #2c3e50;
        margin-bottom: 30px;
        font-weight: 600;
        }

        .phone-input-group {
        margin-bottom: 30px;
        text-align: left;
        }

        .phone-label {
        display: block;
        font-size: 16px;
        color: #2c3e50;
        margin-bottom: 10px;
        font-weight: 500;
        }

        .phone-input {
        width: 100%;
        padding: 16px 20px;
        border: 2px solid #e8ecf0;
        border-radius: 12px;
        font-size: 16px;
        background: #ffffff;
        box-sizing: border-box;
        transition: all 0.3s ease;
        font-family: inherit;
        }

        .phone-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        }

        .verification-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 16px 40px;
        border-radius: 50px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        margin-bottom: 20px;
        }

        .verification-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.5);
        }

        .verification-btn:disabled {
        background: #a0a6b1;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
        }

        /* 验证码输入步骤样式 */
        .verification-step {
        display: none;
        }

        .verification-step.active {
        display: block;
        }

        .code-info {
        background: #f8f9ff;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 24px;
        text-align: left;
        }

        .code-info-title {
        font-size: 16px;
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 8px;
        }

        .code-info-phone {
        font-size: 14px;
        color: #667eea;
        margin-bottom: 8px;
        }

        .code-info-tip {
        font-size: 13px;
        color: #a0a6b1;
        }

        .code-info-tip a {
        color: #667eea;
        text-decoration: none;
        }

        .code-input-container {
        display: flex;
        justify-content: center;
        gap: 12px;
        margin-bottom: 30px;
        }

        .code-input {
        width: 50px;
        height: 50px;
        border: 2px solid #e8ecf0;
        border-radius: 12px;
        text-align: center;
        font-size: 20px;
        font-weight: 600;
        color: #2c3e50;
        background: #ffffff;
        transition: all 0.3s ease;
        outline: none;
        }

        .code-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        transform: scale(1.05);
        }

        .code-input.filled {
        border-color: #667eea;
        background: #f8f9ff;
        }

        .resend-container {
        text-align: center;
        margin-bottom: 20px;
        }

        .resend-text {
        font-size: 14px;
        color: #a0a6b1;
        margin-bottom: 8px;
        }

        .resend-btn {
        background: none;
        border: none;
        color: #667eea;
        font-size: 14px;
        cursor: pointer;
        text-decoration: underline;
        padding: 0;
        }

        .resend-btn:disabled {
        color: #a0a6b1;
        cursor: not-allowed;
        text-decoration: none;
        }

        .submit-code-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 16px 40px;
        border-radius: 50px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        width: 100%;
        margin-bottom: 20px;
        }

        .submit-code-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.5);
        }

        .submit-code-btn:disabled {
        background: #a0a6b1;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
        }

        .close-btn {
        position: absolute;
        top: 15px;
        right: 15px;
        background: rgba(0, 0, 0, 0.05);
        border: none;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        color: #a0a6b1;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        padding: 0;
        }

        .close-btn:hover {
        background: rgba(0, 0, 0, 0.1);
        color: #2c3e50;
        transform: scale(1.1);
        }

        .close-btn:active {
        transform: scale(0.95);
        }

        .close-icon {
        font-size: 18px;
        font-weight: 400;
        line-height: 1;
        transition: transform 0.2s ease;
        }

        .close-btn:hover .close-icon {
        transform: rotate(90deg);
        }

        @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
        }

        @keyframes modalSlideUp {
        from {
        transform: translateY(50px);
        opacity: 0;
        }
        to {
        transform: translateY(0);
        opacity: 1;
        }
        }

        .submit-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 18px 80px;
        border-radius: 50px;
        font-size: 18px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        letter-spacing: 1px;
        position: relative;
        overflow: hidden;
        }

        .submit-btn:before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
        }

        .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.5);
        }

        .submit-btn:hover:before {
        left: 100%;
        }

        .submit-btn:active {
        transform: translateY(-1px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        /* 图片上传样式 */
        .upload-container {
        margin-top: 16px;
        }

        .upload-area {
        border: 2px dashed #e8ecf0;
        border-radius: 12px;
        padding: 24px;
        text-align: center;
        background: #fafbfc;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        }

        .upload-area:hover {
        border-color: #667eea;
        background: #f8f9ff;
        }

        .upload-area.dragover {
        border-color: #667eea;
        background: #f0f4ff;
        transform: scale(1.02);
        }

        .upload-icon {
        font-size: 32px;
        color: #a0a6b1;
        margin-bottom: 12px;
        }

        .upload-text {
        color: #2c3e50;
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
        }

        .upload-hint {
        color: #a0a6b1;
        font-size: 14px;
        line-height: 1.4;
        }

        .upload-preview {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        margin-top: 16px;
        }

        .preview-item {
        position: relative;
        width: 80px;
        height: 80px;
        border-radius: 8px;
        overflow: hidden;
        border: 2px solid #e8ecf0;
        background: #ffffff;
        }

        .preview-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        }

        .preview-remove {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 20px;
        height: 20px;
        background: #e74c3c;
        color: white;
        border: none;
        border-radius: 50%;
        font-size: 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .preview-remove:hover {
        background: #c0392b;
        }

        /* 验证错误样式 */
        .form-input.error,
        .contact-input.error {
        border-color: #e74c3c;
        box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
        }

        .error-message {
        color: #e74c3c;
        font-size: 13px;
        margin-top: 6px;
        display: flex;
        align-items: center;
        gap: 4px;
        }

        .error-icon {
        font-size: 12px;
        }

        /* 图片上传样式 */
        .upload-container {
        margin-top: 16px;
        }

        .upload-area {
        border: 2px dashed #e8ecf0;
        border-radius: 12px;
        padding: 24px;
        text-align: center;
        background: #fafbfc;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        }

        .upload-area:hover {
        border-color: #667eea;
        background: #f8f9ff;
        }

        .upload-area.dragover {
        border-color: #667eea;
        background: #f0f4ff;
        transform: scale(1.02);
        }

        .upload-icon {
        font-size: 32px;
        color: #a0a6b1;
        margin-bottom: 12px;
        }

        .upload-text {
        color: #2c3e50;
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
        }

        .upload-hint {
        color: #a0a6b1;
        font-size: 14px;
        line-height: 1.4;
        }

        .upload-preview {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        margin-top: 16px;
        }

        .preview-item {
        position: relative;
        width: 80px;
        height: 80px;
        border-radius: 8px;
        overflow: hidden;
        border: 2px solid #e8ecf0;
        background: #ffffff;
        }

        .preview-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        }

        .preview-remove {
        position: absolute;
        top: -8px;
        right: -8px;
        width: 20px;
        height: 20px;
        background: #e74c3c;
        color: white;
        border: none;
        border-radius: 50%;
        font-size: 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .preview-remove:hover {
        background: #c0392b;
        }

        /* 验证错误样式 */
        .form-input.error,
        .contact-input.error {
        border-color: #e74c3c;
        box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
        }

        .error-message {
        color: #e74c3c;
        font-size: 13px;
        margin-top: 6px;
        display: flex;
        align-items: center;
        gap: 4px;
        }

        .error-icon {
        font-size: 12px;
        }

        /* 输入框占位符样式 */
        .form-input::placeholder,
        .form-textarea::placeholder,
        .contact-input::placeholder {
        color: #a0a6b1;
        font-size: 15px;
        opacity: 0.8;
        }

        /* 响应式设计 - 针对不同手机屏幕尺寸 */

        /* 超大屏手机 (428px+) - iPhone 14 Pro Max, iPhone 15 Pro Max 等 */
        @media (min-width: 428px) {
        .form-container {
        padding: 32px 24px 250px 24px;
        }

        .submit-container {
        padding: 24px;
        padding-bottom: calc(24px + env(safe-area-inset-bottom));
        }

        .submit-btn {
        padding: 20px 90px;
        font-size: 18px;
        }

        .contact-row {
        gap: 10px;
        }

        .contact-item {
        flex: 1;
        min-width: 0;
        }
        }

        /* iPhone 14 Pro Max 专用适配 (430x932) */
        @media (min-width: 430px) and (min-height: 900px) {
        .form-container {
        padding: 32px 24px 320px 24px;
        }

        .contact-section {
        margin-top: 36px;
        padding-top: 24px;
        }

        .contact-row {
        gap: 8px;
        margin-bottom: 28px;
        }

        .contact-item {
        margin-bottom: 20px;
        flex: 1;
        min-width: 0;
        }

        .contact-input {
        margin-bottom: 12px;
        width: 100%;
        box-sizing: border-box;
        }
        }

        /* iPhone 14 Pro Max 高分辨率适配 */
        @media (min-device-width: 430px) and (min-device-height: 932px) and (-webkit-device-pixel-ratio: 3) {
        .form-container {
        padding: 32px 24px 350px 24px;
        }

        .submit-container {
        padding: 28px 24px;
        padding-bottom: calc(28px + env(safe-area-inset-bottom, 34px));
        }

        .contact-row {
        gap: 6px;
        margin-bottom: 24px;
        align-items: flex-start;
        }

        .contact-item {
        flex: 1;
        min-width: 0;
        max-width: calc(50% - 3px);
        }

        .contact-input {
        font-size: 15px;
        padding: 12px 14px;
        }

        .contact-label {
        font-size: 14px;
        margin-bottom: 6px;
        }
        }

        /* 大屏手机 (414px+) - iPhone 12 Pro Max, Samsung Galaxy S21+ 等 */
        @media (min-width: 415px) and (max-width: 480px) {
        .mobile-container {
        max-width: 100%;
        padding: 0 8px;
        }

        .header-banner {
        height: 140px;
        padding: 0 24px;
        }

        .form-container {
        margin: -20px 16px 0 16px;
        padding: 32px 28px;
        }

        .submit-container {
        padding: 18px 28px;
        }
        }

        /* 中等屏幕手机 (375px-414px) - iPhone 12, iPhone 13 等 */
        @media (min-width: 376px) and (max-width: 414px) {
        .mobile-container {
        max-width: 100%;
        padding: 0 6px;
        }

        .header-banner {
        height: 130px;
        padding: 0 20px;
        }

        .logo-icon {
        width: 52px;
        height: 52px;
        font-size: 26px;
        }

        .logo-text {
        font-size: 21px;
        }

        .form-container {
        padding: 28px 24px 130px 24px;
        margin: -18px 12px 0 12px;
        border-radius: 18px;
        }

        .contact-row {
        flex-direction: column;
        gap: 18px;
        }

        .submit-container {
        padding: 18px 24px;
        }

        .submit-btn {
        padding: 17px 70px;
        font-size: 17px;
        }

        .form-input,
        .form-textarea {
        padding: 15px 18px;
        font-size: 16px;
        }

        .contact-input {
        padding: 13px 15px;
        font-size: 15px;
        }
        }

        /* 标准屏幕手机 (361px-375px) - iPhone SE, 小米等 */
        @media (min-width: 361px) and (max-width: 375px) {
        .mobile-container {
        max-width: 100%;
        padding: 0 4px;
        }

        .header-banner {
        height: 120px;
        padding: 0 18px;
        }

        .logo-icon {
        width: 48px;
        height: 48px;
        font-size: 24px;
        }

        .logo-text {
        font-size: 20px;
        }
        .form-container {
        padding: 24px 20px 130px 20px;
        margin: -16px 10px 0 10px;
        border-radius: 16px;
        }

        .contact-row {
        flex-direction: column;
        gap: 16px;
        }

        .submit-container {
        padding: 16px 20px;
        }

        .submit-btn {
        padding: 16px 60px;
        font-size: 17px;
        }

        .form-input,
        .form-textarea {
        padding: 14px 16px;
        font-size: 16px;
        }

        .contact-input {
        padding: 12px 14px;
        font-size: 15px;
        }

        .upload-area {
        padding: 20px;
        }

        .upload-text {
        font-size: 15px;
        }

        .upload-hint {
        font-size: 13px;
        }
        }

        /* 小屏手机 (320px-360px) - iPhone SE 第一代, 老款Android 等 */
        @media (max-width: 360px) {
        .mobile-container {
        max-width: 100%;
        padding: 0 2px;
        }

        .header-banner {
        height: 110px;
        padding: 0 16px;
        }

        .logo-icon {
        width: 44px;
        height: 44px;
        font-size: 22px;
        }

        .logo-text {
        font-size: 18px;
        line-height: 1.2;
        }

        .form-container {
        padding: 20px 16px 140px 16px;
        margin: -12px 8px 0 8px;
        border-radius: 14px;
        }

        .form-group {
        margin-bottom: 20px;
        }

        .form-label {
        font-size: 15px;
        margin-bottom: 10px;
        }

        .contact-section {
        margin-top: 24px;
        padding-top: 20px;
        }

        .contact-title {
        font-size: 17px;
        margin-bottom: 18px;
        }

        .contact-row {
        flex-direction: column;
        gap: 14px;
        }

        .contact-label {
        font-size: 14px;
        margin-bottom: 6px;
        }

        .submit-container {
        padding: 16px;
        }

        .submit-btn {
        padding: 14px 50px;
        font-size: 16px;
        border-radius: 40px;
        }

        .form-input,
        .form-textarea {
        padding: 12px 14px;
        font-size: 15px;
        border-radius: 10px;
        }

        .contact-input {
        padding: 10px 12px;
        font-size: 14px;
        border-radius: 8px;
        }

        .upload-area {
        padding: 16px;
        }

        .upload-icon {
        font-size: 28px;
        margin-bottom: 8px;
        }

        .upload-text {
        font-size: 14px;
        margin-bottom: 6px;
        }

        .upload-hint {
        font-size: 12px;
        }

        .preview-item {
        width: 70px;
        height: 70px;
        }

        .error-message {
        font-size: 12px;
        margin-top: 4px;
        }
        }

        /* 超小屏手机 (280px-319px) - 极少数老款手机 */
        @media (max-width: 319px) {
        .header-banner {
        height: 100px;
        padding: 0 12px;
        }

        .logo-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
        }

        .logo-text {
        font-size: 16px;
        }

        .form-container {
        padding: 16px 12px 120px 12px;
        margin: -10px 6px 0 6px;
        }

        .submit-container {
        padding: 14px 12px;
        }

        .submit-btn {
        padding: 12px 40px;
        font-size: 15px;
        }

        .form-input,
        .form-textarea,
        .contact-input {
        padding: 10px 12px;
        font-size: 14px;
        }
        }

        /* 横屏适配 */
        @media (orientation: landscape) and (max-height: 500px) {
        .header-banner {
        height: 80px;
        }

        .logo-icon {
        width: 36px;
        height: 36px;
        font-size: 18px;
        }

        .logo-text {
        font-size: 16px;
        }

        .form-container {
        padding: 16px 20px 100px 20px;
        }

        .submit-container {
        padding: 16px 20px;
        }

        .form-group {
        margin-bottom: 16px;
        }

        .contact-section {
        margin-top: 20px;
        padding-top: 16px;
        }

        /* 验证码弹窗响应式 */
        .verification-modal {
        padding: 30px 20px;
        margin: 15px;
        }

        .verification-logo {
        width: 60px;
        height: 60px;
        font-size: 24px;
        margin-bottom: 20px;
        }

        .verification-title {
        font-size: 16px;
        margin-bottom: 20px;
        }

        .phone-input {
        padding: 14px 16px;
        font-size: 15px;
        }

        .verification-btn {
        padding: 14px 30px;
        font-size: 15px;
        }

        .code-input-container {
        gap: 8px;
        }

        .code-input {
        width: 40px;
        height: 40px;
        font-size: 18px;
        }

        .code-info {
        padding: 12px;
        margin-bottom: 20px;
        }

        .code-info-title {
        font-size: 14px;
        }

        .code-info-phone {
        font-size: 13px;
        }

        .code-info-tip {
        font-size: 12px;
        }

        .close-btn {
        width: 32px;
        height: 32px;
        top: 12px;
        right: 12px;
        }

        .close-icon {
        font-size: 14px;
        }
        }

        /* 超小屏设备验证码适配 */
        @media (max-width: 360px) {
        .verification-modal {
        padding: 25px 15px;
        margin: 10px;
        }

        .code-input-container {
        gap: 6px;
        }

        .code-input {
        width: 35px;
        height: 35px;
        font-size: 16px;
        }

        .verification-title {
        font-size: 15px;
        }

        .code-info {
        padding: 10px;
        margin-bottom: 16px;
        }

        .close-btn {
        width: 28px;
        height: 28px;
        top: 10px;
        right: 10px;
        }

        .close-icon {
        font-size: 12px;
        }
        }

        /* iPhone 14 Pro Max 验证码优化 */
        @media (min-width: 430px) and (min-height: 900px) {
        .code-input-container {
        gap: 16px;
        }

        .code-input {
        width: 60px;
        height: 60px;
        font-size: 24px;
        border-radius: 16px;
        }

        .verification-title {
        font-size: 20px;
        margin-bottom: 32px;
        }

        .code-info {
        padding: 20px;
        margin-bottom: 32px;
        }

        .code-info-title {
        font-size: 17px;
        }

        .code-info-phone {
        font-size: 15px;
        }

        .close-btn {
        width: 40px;
        height: 40px;
        top: 20px;
        right: 20px;
        }

        .close-icon {
        font-size: 20px;
        }
        }

        /* 成功页面样式 */
        .success-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        z-index: 3000;
        display: none;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        animation: fadeIn 0.5s ease-out;
        }

        .success-overlay.show {
        display: flex;
        }

        .success-header {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 280px;
        background-image: url('/img/3.png');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        color: white;
        padding: 20px;
        box-sizing: border-box;
        }

        .header-bottom {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        width: 100%;
        height: 100%;
        }

        .qr-codes {
        display: flex;
        gap: 15px;
        }

        .qr-code {
        width: 70px;
        height: 70px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.8);
        }

        .action-buttons {
        display: flex;
        gap: 12px;
        }

        .action-btn {
        padding: 12px 24px;
        border: none;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 100px;
        background: #3b82f6;
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .demo-btn:hover {
        background: #2563eb;
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
        }

        .consult-btn:hover {
        background: #2563eb;
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
        }



        .header-actions {
        display: flex;
        gap: 12px;
        width: 100%;
        max-width: 300px;
        }

        .header-btn {
        flex: 1;
        padding: 12px 16px;
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        color: white;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        text-align: center;
        }

        .header-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-2px);
        }

        .header-btn:active {
        transform: translateY(0);
        }

        .success-content {
        background: white;
        border-radius: 20px 20px 0 0;
        padding: 40px 30px 40px 30px;
        margin: 0;
        width: 100%;
        min-height: calc(100vh - 280px);
        text-align: center;
        box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
        animation: successSlideUp 0.6s ease-out;
        margin-top: 280px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 60px;
        }

        .success-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 30px auto;
        background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 40px;
        animation: successBounce 0.8s ease-out;
        }

        .success-title {
        font-size: 24px;
        color: #2c3e50;
        margin-bottom: 16px;
        font-weight: 600;
        }

        .success-message {
        font-size: 14px;
        color: #7f8c8d;
        margin-bottom: 40px;
        line-height: 1.6;
        }

        .success-actions {
        display: flex;
        gap: 12px;
        margin-bottom: 30px;
        }

        .action-btn {
        flex: 1;
        padding: 14px 20px;
        border: none;
        border-radius: 12px;
        font-size: 15px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        }

        .action-btn.primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .action-btn.primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
        }

        .action-btn.secondary {
        background: #f8f9fa;
        color: #667eea;
        border: 2px solid #e9ecef;
        }

        .action-btn.secondary:hover {
        background: #e9ecef;
        border-color: #667eea;
        transform: translateY(-1px);
        }

        .success-footer {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
        font-size: 12px;
        color: #a0a6b1;
        }

        @keyframes successSlideUp {
        from {
        transform: translateY(100px);
        opacity: 0;
        }
        to {
        transform: translateY(0);
        opacity: 1;
        }
        }

        @keyframes successBounce {
        0% {
        transform: scale(0);
        }
        50% {
        transform: scale(1.2);
        }
        100% {
        transform: scale(1);
        }
        }

        /* 成功页面响应式设计 */
        @media (orientation: landscape) and (max-height: 500px) {
        .success-header {
        height: 200px;
        padding: 15px;
        }

        .header-brand {
        margin-bottom: 20px;
        }

        .brand-title-main {
        font-size: 18px;
        }

        .brand-subtitle-main {
        font-size: 11px;
        }

        .brand-highlight {
        font-size: 24px;
        }

        .header-btn {
        padding: 10px 12px;
        font-size: 12px;
        }

        .success-content {
        margin-top: 200px;
        padding: 30px 20px;
        border-radius: 15px 15px 0 0;
        }

        .success-icon {
        width: 60px;
        height: 60px;
        font-size: 30px;
        margin-bottom: 20px;
        }

        .success-title {
        font-size: 20px;
        margin-bottom: 12px;
        }

        .success-message {
        font-size: 13px;
        margin-bottom: 30px;
        }

        .action-btn {
        padding: 12px 16px;
        font-size: 14px;
        }
        }

        @media (max-width: 360px) {
        .success-header {
        height: 240px;
        padding: 15px;
        }

        .qr-code {
        width: 50px;
        height: 50px;
        }

        .action-buttons {
        flex-direction: column;
        gap: 8px;
        padding: 8px;
        }

        .action-btn {
        padding: 10px 16px;
        font-size: 12px;
        min-width: 80px;
        }

        .header-btn {
        padding: 10px 16px;
        font-size: 13px;
        }

        .success-content {
        margin-top: 240px;
        padding: 40px 20px 30px 20px;
        border-radius: 20px 20px 0 0;
        }

        .success-icon {
        width: 60px;
        height: 60px;
        font-size: 28px;
        margin-bottom: 20px;
        }

        .success-title {
        font-size: 18px;
        margin-bottom: 12px;
        }

        .success-message {
        font-size: 12px;
        margin-bottom: 25px;
        }

        .success-actions {
        flex-direction: column;
        gap: 10px;
        }

        .action-btn {
        padding: 12px 16px;
        font-size: 14px;
        }
        }

        @media (min-width: 430px) and (min-height: 900px) {
        .success-header {
        height: 320px;
        padding: 30px;
        }

        .qr-code {
        width: 85px;
        height: 85px;
        }

        .action-buttons {
        padding: 12px;
        }

        .action-btn {
        padding: 16px 28px;
        font-size: 16px;
        min-width: 120px;
        }

        .header-actions {
        max-width: 350px;
        }

        .header-btn {
        padding: 16px 20px;
        font-size: 16px;
        }

        .success-content {
        margin-top: 320px;
        padding: 80px 40px 60px 40px;
        border-radius: 25px 25px 0 0;
        }

        .success-icon {
        width: 100px;
        height: 100px;
        font-size: 50px;
        margin-bottom: 40px;
        }

        .success-title {
        font-size: 28px;
        margin-bottom: 20px;
        }

        .success-message {
        font-size: 16px;
        margin-bottom: 50px;
        }

        .action-btn {
        padding: 18px 24px;
        font-size: 16px;
        }

        .success-actions {
        gap: 16px;
        }
        }

        /* 通用移动端优化 */
        @media (max-width: 768px) {
        .success-overlay {
        font-size: 14px;
        }

        .success-content {
        border-radius: 20px 20px 0 0;
        box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.1);
        }

        .success-actions {
        width: 100%;
        max-width: 300px;
        }

        .action-btn {
        min-height: 48px; /* 确保触摸目标足够大 */
        }

        .header-btn {
        min-height: 44px; /* 确保触摸目标足够大 */
        }
        }

        /* 防止在移动设备上出现横向滚动 */
        @media (max-width: 480px) {
        .success-overlay {
        overflow-x: hidden;
        }

        .success-header {
        padding-left: 15px;
        padding-right: 15px;
        }

        .success-content {
        padding-left: 20px;
        padding-right: 20px;
        }

        .header-actions {
        width: 100%;
        max-width: calc(100vw - 60px);
        }

        .success-actions {
        width: 100%;
        max-width: calc(100vw - 60px);
        }
        }
    </style>

    <h:div class="mobile-container" use="com.cisdi.ui.ProbleDiv" id="dataLoader">
        <!-- 头部横幅 -->
        <h:div class="header-banner">
            <h:div class="logo-container">
                <h:div class="logo-icon">🏢</h:div>
                <h:div class="logo-text">
                    客诉反馈
                    <h:br/>
                    系统平台
                </h:div>
            </h:div>
        </h:div>

        <!-- 表单内容 -->
        <h:div class="form-container">
            <!-- 客户单位名称 -->
            <h:div class="form-group">
                <h:div class="form-label">
                    <h:span class="required">*</h:span>1.客户单位名称：
                </h:div>
                <textbox id="customerName" class="form-input" placeholder="请输入客户单位名称"/>
            </h:div>

            <!-- 项目名称 -->
            <h:div class="form-group">
                <h:div class="form-label">
                    <h:span class="required">*</h:span>2.项目名称：
                </h:div>
                <textbox id="projectName" class="form-input" placeholder="请输入项目名称"/>
            </h:div>

            <!-- 需反馈/投诉的问题 -->
            <h:div class="form-group">
                <h:div class="form-label">
                    <h:span class="required">*</h:span>3.需反馈/投诉的问题：
                </h:div>
                <textbox id="problemDesc" class="form-textarea" multiline="true" rows="6"
                         placeholder="请详细描述您要反馈或投诉的问题..."/>

                <!-- 图片上传 -->
                <h:div class="upload-container">
                    <h:div id="uploadArea" class="upload-area" onclick="triggerFileUpload()">
                        <h:div class="upload-icon">📷</h:div>
                        <h:div class="upload-text">上传相关图片</h:div>
                        <h:div class="upload-hint">支持 JPG、PNG 格式，单张不超过 5MB<h:br/>最多可上传 3 张图片
                        </h:div>
                    </h:div>
                    <h:input type="file" id="fileInput" style="display: none;" accept="image/*" multiple="true"
                             onchange="handleFileUpload(event)"/>
                    <h:div id="uploadPreview" class="upload-preview"></h:div>
                </h:div>
            </h:div>

            <!-- 联系方式 -->
            <h:div class="contact-section">
                <h:div class="contact-title">4.联系方式：</h:div>

                <h:div class="contact-row">
                    <h:div class="contact-item">
                        <h:div class="contact-label">
                            <h:span class="required">*</h:span>姓名：
                        </h:div>
                        <textbox id="contactName" class="contact-input" placeholder="请输入姓名"/>
                        <h:div id="nameError" class="error-message" style="display: none;">
                            <h:span class="error-icon">⚠</h:span>
                            <h:span>请输入有效的姓名</h:span>
                        </h:div>
                    </h:div>
                    <h:div class="contact-item">
                        <h:div class="contact-label">
                            <h:span class="required">*</h:span>电话：
                        </h:div>
                        <textbox id="contactPhone" class="contact-input" placeholder="请输入电话号码"/>
                        <h:div id="phoneError" class="error-message" style="display: none;">
                            <h:span class="error-icon">⚠</h:span>
                            <h:span>请输入有效的手机号码</h:span>
                        </h:div>
                    </h:div>
                </h:div>

                <h:div class="contact-row">
                    <h:div class="contact-item">
                        <h:div class="contact-label">
                            <h:span class="required">*</h:span>邮箱：
                        </h:div>
                        <textbox id="contactEmail" class="contact-input" placeholder="请输入邮箱地址"/>
                        <h:div id="emailError" class="error-message" style="display: none;">
                            <h:span class="error-icon">⚠</h:span>
                            <h:span>请输入有效的邮箱地址</h:span>
                        </h:div>
                    </h:div>
                    <h:div class="contact-item">
                        <h:div class="contact-label">分厂：</h:div>
                        <textbox id="factory" class="contact-input" placeholder="请输入分厂信息"/>
                    </h:div>
                </h:div>
            </h:div>
        </h:div>

        <!-- 提交按钮 -->
        <h:div class="submit-container">
            <h:button id="submitBtn" class="submit-btn" onclick="submitForm()">提交</h:button>
        </h:div>
    </h:div>

    <!-- 验证码弹窗 -->
    <h:div id="verificationOverlay" class="verification-overlay" onclick="closeVerificationOnOverlay(event)">
        <h:div class="verification-modal" onclick="event.stopPropagation()">
            <h:button class="close-btn" onclick="closeVerification()">
                <h:span class="close-icon">X</h:span>
            </h:button>

            <h:div class="verification-logo">
                🏢
            </h:div>

            <!-- 第一步：输入手机号 -->
            <h:div id="phoneStep" class="verification-step active">
                <h:div class="verification-title">
                    请输入手机号获取验证码
                </h:div>

                <h:div class="phone-input-group">
                    <h:label class="phone-label">手机</h:label>
                    <h:input type="tel" id="verificationPhone" class="phone-input" placeholder="请输入手机号码"
                             maxlength="11"/>
                </h:div>

                <h:button id="getCodeBtn" class="verification-btn" onclick="getVerificationCode()">
                    获取验证码
                </h:button>
            </h:div>

            <!-- 第二步：输入验证码 -->
            <h:div id="codeStep" class="verification-step">
                <h:div class="verification-title">
                    短信验证码
                </h:div>

                <h:div class="code-info">
                    <h:div class="code-info-title">验证码已发送至：</h:div>
                    <h:div id="codeInfoPhone" class="code-info-phone">150****1234</h:div>
                    <h:div class="code-info-tip">
                        若未收到验证码，
                        <h:a href="#" onclick="resendCode(); return false;">请点击此处</h:a>
                    </h:div>
                </h:div>

                <h:div class="code-input-container">
                    <h:input type="text" class="code-input" maxlength="1" oninput="handleCodeInput(this, 0)"
                             onkeydown="handleCodeKeydown(this, event)"/>
                    <h:input type="text" class="code-input" maxlength="1" oninput="handleCodeInput(this, 1)"
                             onkeydown="handleCodeKeydown(this, event)"/>
                    <h:input type="text" class="code-input" maxlength="1" oninput="handleCodeInput(this, 2)"
                             onkeydown="handleCodeKeydown(this, event)"/>
                    <h:input type="text" class="code-input" maxlength="1" oninput="handleCodeInput(this, 3)"
                             onkeydown="handleCodeKeydown(this, event)"/>
                </h:div>

                <h:div class="resend-container">
                    <h:div class="resend-text">没有收到验证码？</h:div>
                    <h:button id="resendBtn" class="resend-btn" onclick="resendCode()">重新发送</h:button>
                </h:div>

                <h:button id="submitCodeBtn" class="submit-code-btn" onclick="submitVerificationCode()"
                          disabled="disabled">
                    提交
                </h:button>
            </h:div>

            <h:div style="margin-top: 20px; font-size: 12px; color: #a0a6b1;">
                赛迪轻链
            </h:div>
        </h:div>
    </h:div>

    <!-- 成功页面 -->
    <h:div id="successOverlay" class="success-overlay">
        <h:div class="success-header">
            <!-- 底部操作区域 -->
            <h:div class="header-bottom">
                <!-- 左下角二维码 -->
                <h:div class="qr-codes">
                    <h:img src="/img/<EMAIL>" alt="二维码1" class="qr-code"/>
                    <h:img src="/img/<EMAIL>" alt="二维码2" class="qr-code"/>
                </h:div>

                <!-- 右下角按钮 -->
                <h:div class="action-buttons">
                    <h:button class="action-btn demo-btn" onclick="bookDemo()">
                        预约演示
                    </h:button>
                    <h:button class="action-btn consult-btn" onclick="startConsult()">
                        立即咨询
                    </h:button>
                </h:div>
            </h:div>
        </h:div>

        <h:div class="success-content">
            <h:div class="success-icon">
                ✓
            </h:div>

            <h:div class="success-title">
                提交成功
            </h:div>

            <h:div class="success-message">
                温馨提示：我们将及时对反馈进行处理
                <h:br/>
                请注意查收电话或邮件回复
            </h:div>

            <h:div class="success-actions">
                <h:button class="action-btn secondary" onclick="downloadReport()">
                    <h:span>📄</h:span>
                    <h:span>报告查看</h:span>
                </h:button>
                <h:button class="action-btn primary" onclick="continueSubmit()">
                    <h:span>➕</h:span>
                    <h:span>继续提交</h:span>
                </h:button>
            </h:div>

            <h:div class="success-footer">
                赛迪轻链
            </h:div>
        </h:div>
    </h:div>

    <script>
        //<![CDATA[
    // 上传的图片数组
    var uploadedImages = [];
    var maxImages = 3;
    var maxFileSize = 5 * 1024 * 1024; // 5MB

    // 触发文件选择
    function triggerFileUpload() {
        document.getElementById('fileInput').click();
    }

    // 处理文件上传
    function handleFileUpload(event) {
        var files = event.target.files;

        for (var i = 0; i < files.length; i = i + 1) {
            if (uploadedImages.length >= maxImages) {
                alert('最多只能上传' + maxImages + '张图片');
                break;
            }

            var file = files[i];

            // 验证文件类型
            if (!file.type.match(/^image\/(jpeg|jpg|png)$/)) {
                alert('请选择JPG或PNG格式的图片');
                continue;
            }

            // 验证文件大小
            if (file.size > maxFileSize) {
                alert('图片大小不能超过5MB');
                continue;
            }

            // 读取并预览图片
            var reader = new FileReader();
            reader.onload = function(e) {
                addImagePreview(e.target.result, file.name);
            };
            reader.readAsDataURL(file);

            uploadedImages.push(file);
        }

        // 清空input值，允许重复选择同一文件
        event.target.value = '';
    }

    // 添加图片预览
    function addImagePreview(src, fileName) {
        var previewContainer = document.getElementById('uploadPreview');
        var index = uploadedImages.length - 1;

        var previewItem = document.createElement('div');
        previewItem.className = 'preview-item';

        // 创建图片元素
        var img = document.createElement('img');
        img.src = src;
        img.alt = fileName;
        img.className = 'preview-image';

        // 创建删除按钮
        var removeBtn = document.createElement('button');
        removeBtn.className = 'preview-remove';
        removeBtn.title = '删除图片';
        removeBtn.innerHTML = '×';
        removeBtn.onclick = function() { removeImage(index); };

        // 添加到预览项
        previewItem.appendChild(img);
        previewItem.appendChild(removeBtn);
        previewContainer.appendChild(previewItem);
    }

    // 删除图片
    function removeImage(index) {
        uploadedImages.splice(index, 1);
        refreshImagePreview();
    }

    // 刷新图片预览
    function refreshImagePreview() {
        var previewContainer = document.getElementById('uploadPreview');
        previewContainer.innerHTML = '';

        for (var i = 0; i < uploadedImages.length; i = i + 1) {
            var file = uploadedImages[i];
            var reader = new FileReader();
            reader.onload = (function(index, fileName) {
                return function(e) {
                    var previewItem = document.createElement('div');
                    previewItem.className = 'preview-item';

                    // 创建图片元素
                    var img = document.createElement('img');
                    img.src = e.target.result;
                    img.alt = fileName;
                    img.className = 'preview-image';

                    // 创建删除按钮
                    var removeBtn = document.createElement('button');
                    removeBtn.className = 'preview-remove';
                    removeBtn.title = '删除图片';
                    removeBtn.innerHTML = '×';
                    removeBtn.onclick = function() { removeImage(index); };

                    // 添加到预览项
                    previewItem.appendChild(img);
                    previewItem.appendChild(removeBtn);
                    previewContainer.appendChild(previewItem);
                };
            })(i, file.name);
            reader.readAsDataURL(file);
        }
    }

    // 获取ZK组件值的辅助函数
    function getZKValue(componentId) {
        try {
            var comp = zk.Widget.$('$' + componentId);
            return comp ? comp.getValue() : '';
        } catch (e) {
            // 如果ZK组件获取失败，尝试DOM方式
            var element = document.getElementById(componentId);
            if (element) {
                return element.value || element.textContent || '';
            }
            return '';
        }
    }

    // 验证姓名
    function validateName() {
        var nameError = document.getElementById('nameError');
        var name = getZKValue('contactName').trim();
        if (name.length < 2 || name.length > 20) {
            showError(nameError, '姓名长度应在2-20个字符之间');
            return false;
        }

        // 检查是否包含特殊字符
        var nameRegex = /^[\u4e00-\u9fa5a-zA-Z\s]+$/;
        if (!nameRegex.test(name)) {
            showError(nameError, '姓名只能包含中文、英文和空格');
            return false;
        }

        hideError(nameError);
        return true;
    }

    // 验证电话号码
    function validatePhone() {
        var phoneError = document.getElementById('phoneError');
        var phone = getZKValue('contactPhone').trim();

        // 手机号码正则表达式（支持中国大陆手机号）
        var phoneRegex = /^1[3-9]\d{9}$/;

        if (!phoneRegex.test(phone)) {
            showError(phoneError, '请输入有效的11位手机号码');
            return false;
        }

        hideError(phoneError);
        return true;
    }

    // 验证邮箱
    function validateEmail() {
        var emailError = document.getElementById('emailError');
        var email = getZKValue('contactEmail').trim();

        // 邮箱正则表达式
        var emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

        if (!emailRegex.test(email)) {
            showError(emailError, '请输入有效的邮箱地址');
            return false;
        }

        hideError(emailError);
        return true;
    }

    // 显示错误信息
    function showError(errorElement, message) {
        if (errorElement) {
            var messageSpan = errorElement.querySelector('span:last-child');
            if (messageSpan) {
                messageSpan.textContent = message;
            }
            errorElement.style.display = 'flex';
        }
    }

    // 隐藏错误信息
    function hideError(errorElement) {
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }

    // 表单提交验证
    function validateForm() {
        var isValid = true;
        var errorMessages = [];

        // 验证客户单位名称
        var customerName = getZKValue('customerName').trim();
        if (!customerName) {
            errorMessages.push('请输入客户单位名称');
            isValid = false;
        }

        // 验证项目名称
        var projectName = getZKValue('projectName').trim();
        if (!projectName) {
            errorMessages.push('请输入项目名称');
            isValid = false;
        }

        // 验证问题描述
        var problemDesc = getZKValue('problemDesc').trim();
        if (!problemDesc) {
            errorMessages.push('请描述需要反馈的问题');
            isValid = false;
        }

        // 验证联系方式字段
        if (!validateName()) {
            isValid = false;
        }

        if (!validatePhone()) {
            isValid = false;
        }

        if (!validateEmail()) {
            isValid = false;
        }

        // 如果有错误，显示第一个错误信息
        if (errorMessages.length > 0) {
            alert(errorMessages[0]);
        }

        return isValid;
    }

    // 页面加载完成后初始化功能
    setTimeout(function() {
        // 初始化拖拽上传功能
        var uploadArea = document.getElementById('uploadArea');
        if (uploadArea) {
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');

                var files = e.dataTransfer.files;
                if (files.length > 0) {
                    var fileInput = document.getElementById('fileInput');
                    fileInput.files = files;
                    handleFileUpload({ target: fileInput });
                }
            });
        }

        // 为输入框添加验证事件监听器
        var contactName = document.getElementById('contactName');
        if (contactName) {
            // 获取ZK组件的实际输入元素
            var nameInput = contactName.querySelector('input') || contactName;
            if (nameInput) {
                nameInput.addEventListener('blur', validateName);
            }
        }

        var contactPhone = document.getElementById('contactPhone');
        if (contactPhone) {
            var phoneInput = contactPhone.querySelector('input') || contactPhone;
            if (phoneInput) {
                phoneInput.addEventListener('blur', validatePhone);
            }
        }

        var contactEmail = document.getElementById('contactEmail');
        if (contactEmail) {
            var emailInput = contactEmail.querySelector('input') || contactEmail;
            if (emailInput) {
                emailInput.addEventListener('blur', validateEmail);
            }
        }

        // 动态调整大屏设备的底部间距
        if (window.innerHeight >= 900 && window.innerWidth >= 430) {
            var formContainer = document.querySelector('.form-container');
            if (formContainer) {
                formContainer.style.paddingBottom = '350px';
            }
        }
    }, 500);

    // 客户端提交函数
    function submitForm() {
        if (validateForm()) {
            // 验证成功后显示验证码弹窗
            showVerificationModal();
        }
    }

    // 显示验证码弹窗
    function showVerificationModal() {
        var overlay = document.getElementById('verificationOverlay');
        var phoneInput = document.getElementById('verificationPhone');

        // 自动填入联系电话
        var contactPhone = getZKValue('contactPhone');
        if (contactPhone && phoneInput) {
            phoneInput.value = contactPhone;
        }

        if (overlay) {
            overlay.classList.add('show');
            // 防止背景滚动
            document.body.style.overflow = 'hidden';
        }
    }

    // 关闭验证码弹窗
    function closeVerification() {
        var overlay = document.getElementById('verificationOverlay');
        if (overlay) {
            overlay.classList.remove('show');
            // 恢复背景滚动
            document.body.style.overflow = '';

            // 重置弹窗状态
            resetVerificationModal();
        }
    }

    // 重置验证码弹窗状态
    function resetVerificationModal() {
        var phoneStep = document.getElementById('phoneStep');
        var codeStep = document.getElementById('codeStep');
        var codeInputs = document.querySelectorAll('.code-input');
        var submitBtn = document.getElementById('submitCodeBtn');
        var getCodeBtn = document.getElementById('getCodeBtn');
        var resendBtn = document.getElementById('resendBtn');

        // 重置步骤显示
        if (phoneStep) phoneStep.classList.add('active');
        if (codeStep) codeStep.classList.remove('active');

        // 清空验证码输入框
        codeInputs.forEach(function(input) {
            input.value = '';
            input.classList.remove('filled');
        });

        // 重置按钮状态
        if (submitBtn) submitBtn.disabled = true;
        if (getCodeBtn) {
            getCodeBtn.disabled = false;
            getCodeBtn.textContent = '获取验证码';
        }
        if (resendBtn) {
            resendBtn.disabled = false;
            resendBtn.textContent = '重新发送';
        }
    }

    // 点击遮罩层关闭弹窗
    function closeVerificationOnOverlay(event) {
        if (event.target.id === 'verificationOverlay') {
            closeVerification();
        }
    }

    // 获取验证码
    function getVerificationCode() {
        var phoneInput = document.getElementById('verificationPhone');
        var getCodeBtn = document.getElementById('getCodeBtn');

        if (!phoneInput || !phoneInput.value) {
            alert('请输入手机号码');
            return;
        }

        // 验证手机号格式
        var phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(phoneInput.value)) {
            alert('请输入正确的手机号码');
            return;
        }

        // 禁用按钮并开始倒计时
        if (getCodeBtn) {
            getCodeBtn.disabled = true;
            var countdown = 60;
            getCodeBtn.textContent = countdown + 's后重新获取';

            var timer = setInterval(function() {
                countdown--;
                if (countdown > 0) {
                    getCodeBtn.textContent = countdown + 's后重新获取';
                } else {
                    getCodeBtn.disabled = false;
                    getCodeBtn.textContent = '获取验证码';
                    clearInterval(timer);
                }
            }, 1000);
        }

        // 模拟发送验证码
        setTimeout(function() {
            // 切换到验证码输入步骤
            zAu.send(new zk.Event(zk.Widget.$('#dataLoader'), 'sendPhoneCode', phoneInput.value));
            showCodeStep(phoneInput.value);
        }, 1000);
    }

    // 显示验证码输入步骤
    function showCodeStep(phoneNumber) {
        var phoneStep = document.getElementById('phoneStep');
        var codeStep = document.getElementById('codeStep');
        var codeInfoPhone = document.getElementById('codeInfoPhone');

        // 隐藏手机号输入步骤
        if (phoneStep) {
            phoneStep.classList.remove('active');
        }

        // 显示验证码输入步骤
        if (codeStep) {
            codeStep.classList.add('active');
        }

        // 显示脱敏手机号
        if (codeInfoPhone && phoneNumber) {
            var maskedPhone = phoneNumber.substring(0, 3) + '****' + phoneNumber.substring(7);
            codeInfoPhone.textContent = maskedPhone;
        }

        // 聚焦到第一个验证码输入框
        var firstCodeInput = document.querySelector('.code-input');
        if (firstCodeInput) {
            setTimeout(function() {
                firstCodeInput.focus();
            }, 100);
        }
    }

    // 处理验证码输入
    function handleCodeInput(input, index) {
        // 只允许数字
        input.value = input.value.replace(/[^0-9]/g, '');

        if (input.value) {
            input.classList.add('filled');
            // 自动跳转到下一个输入框
            var nextInput = input.parentNode.children[index + 1];
            if (nextInput) {
                nextInput.focus();
            }
        } else {
            input.classList.remove('filled');
        }

        // 检查是否所有输入框都已填写
        checkCodeComplete();
    }

    // 处理验证码输入框的键盘事件
    function handleCodeKeydown(input, event) {
        var index = Array.from(input.parentNode.children).indexOf(input);

        // 退格键处理
        if (event.key === 'Backspace' && !input.value) {
            var prevInput = input.parentNode.children[index - 1];
            if (prevInput) {
                prevInput.focus();
                prevInput.value = '';
                prevInput.classList.remove('filled');
                checkCodeComplete();
            }
        }

        // 左右箭头键处理
        if (event.key === 'ArrowLeft' && index > 0) {
            input.parentNode.children[index - 1].focus();
        }
        if (event.key === 'ArrowRight' && index < 3) {
            input.parentNode.children[index + 1].focus();
        }
    }

    // 检查验证码是否输入完整
    function checkCodeComplete() {
        var codeInputs = document.querySelectorAll('.code-input');
        var submitBtn = document.getElementById('submitCodeBtn');
        var allFilled = true;

        codeInputs.forEach(function(input) {
            if (!input.value) {
                allFilled = false;
            }
        });

        if (submitBtn) {
            submitBtn.disabled = !allFilled;
        }
    }

    // 重新发送验证码
    function resendCode() {
        var resendBtn = document.getElementById('resendBtn');
        var phoneInput = document.getElementById('verificationPhone');

        if (!phoneInput || !phoneInput.value) {
            alert('手机号码不能为空');
            return;
        }

        // 禁用重发按钮并开始倒计时
        if (resendBtn) {
            resendBtn.disabled = true;
            var countdown = 60;
            resendBtn.textContent = countdown + 's后重新发送';

            var timer = setInterval(function() {
                countdown--;
                if (countdown > 0) {
                    resendBtn.textContent = countdown + 's后重新发送';
                } else {
                    resendBtn.disabled = false;
                    resendBtn.textContent = '重新发送';
                    clearInterval(timer);
                }
            }, 1000);
        }

        // 模拟重新发送
        setTimeout(function() {
            alert('验证码已重新发送');
        }, 1000);
    }

    // 提交验证码
    function submitVerificationCode() {
        var codeInputs = document.querySelectorAll('.code-input');
        var code = '';

        codeInputs.forEach(function(input) {
            code += input.value;
        });

        if (code.length !== 4) {
            alert('请输入完整的验证码');
            return;
        }
          var phone=getZKValue('verificationPhone');
           var data={};
           data.code=code;
           data.phone=phone;
           data.uploadFile=uploadedImages;
           data.customerName=getZKValue('customerName');
           data.projectName=getZKValue('projectName');
           data.problemDesc=getZKValue('problemDesc');
           data.contactName=getZKValue('contactName');
           data.contactPhone=getZKValue('contactPhone');
           data.contactEmail=getZKValue('contactEmail');
           data.factory=getZKValue('factory');
           zAu.send(new zk.Event(zk.Widget.$('#dataLoader'), 'verification',data ));

    }

    // 显示成功页面
    function showSuccessPage() {
        var successOverlay = document.getElementById('successOverlay');
        if (successOverlay) {
            successOverlay.classList.add('show');
            // 防止背景滚动
            document.body.style.overflow = 'hidden';
        }
    }

    // 隐藏成功页面
    function hideSuccessPage() {
        var successOverlay = document.getElementById('successOverlay');
        if (successOverlay) {
            successOverlay.classList.remove('show');
            // 恢复背景滚动
            document.body.style.overflow = '';
        }
    }

    // 报告查看功能
    function downloadReport() {

    }

    // 继续提交功能
    function continueSubmit() {
        // 隐藏成功页面，重置表单
        hideSuccessPage();

        // 重置表单数据
        resetForm();

        // 滚动到顶部
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    // 重置表单
    function resetForm() {
        // 清空所有输入框
        var inputs = document.querySelectorAll('input[type="text"], textarea');
        inputs.forEach(function(input) {
            if (input.id && input.id !== 'fileInput') {
                input.value = '';
            }
        });

        // 清空ZK组件
        try {
            if (typeof customerName !== 'undefined' && customerName.setValue) customerName.setValue('');
            if (typeof projectName !== 'undefined' && projectName.setValue) projectName.setValue('');
            if (typeof issueDescription !== 'undefined' && issueDescription.setValue) issueDescription.setValue('');
            if (typeof contactName !== 'undefined' && contactName.setValue) contactName.setValue('');
            if (typeof contactPhone !== 'undefined' && contactPhone.setValue) contactPhone.setValue('');
            if (typeof contactEmail !== 'undefined' && contactEmail.setValue) contactEmail.setValue('');
            if (typeof factory !== 'undefined' && factory.setValue) factory.setValue('');
        } catch (e) {
            console.log('重置ZK组件时出错:', e);
        }

        // 清空上传的图片
        uploadedImages = [];
        updateImagePreview();

        // 隐藏所有错误信息
        var errorMessages = document.querySelectorAll('.error-message');
        errorMessages.forEach(function(error) {
            error.style.display = 'none';
        });

        // 重置验证码弹窗状态
        resetVerificationModal();

        setTimeout(function() {
            alert('表单已重置，您可以继续提交新的反馈！');
        }, 300);
    }

    // 预约演示功能
    function bookDemo() {

    }

    // 立即咨询功能
    function startConsult() {

    }
//]]>
    </script>

</zk>
