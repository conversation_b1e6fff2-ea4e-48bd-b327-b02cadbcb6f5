<?xml version="1.0" encoding="UTF-8"?>
<?meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"?>
<?meta name="format-detection" content="telephone=no"?>
<?meta name="apple-mobile-web-app-capable" content="yes"?>
<?meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"?>

<zk xmlns:h="http://www.w3.org/1999/xhtml" xmlns:w="http://www.zkoss.org/2005/zk/client">

    <style>
        body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        overflow-x: hidden;
        }

        .mobile-container {
        max-width: 430px;
        margin: 0 auto;
        padding: 12px 16px;
        background: transparent;
        min-height: 100vh;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        gap: 12px;
        }

        .result-card {
        background: #ffffff;
        border-radius: 12px;
        padding: 16px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        flex: 1;
        }

        .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
        text-align: left;
        margin-bottom: 12px;
        letter-spacing: 0;
        }

        .result-item {
        margin-bottom: 12px;
        }

        .result-label {
        font-size: 14px;
        color: #2c3e50;
        margin-bottom: 4px;
        font-weight: 500;
        }

        .required {
        color: #e74c3c;
        margin-right: 4px;
        }

        .result-value {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 13px;
        color: #495057;
        border: 1px solid #e9ecef;
        min-height: 16px;
        line-height: 1.3;
        }

        .result-value.large {
        min-height: 40px;
        padding: 8px 12px;
        }

        .contact-grid {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-top: 4px;
        }

        .contact-item {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 8px;
        }

        .contact-label {
        font-size: 14px;
        color: #2c3e50;
        font-weight: 500;
        min-width: 50px;
        flex-shrink: 0;
        }

        .contact-value {
        background: #f8f9fa;
        border-radius: 4px;
        padding: 6px 10px;
        font-size: 13px;
        color: #495057;
        border: 1px solid #e9ecef;
        min-height: 14px;
        flex: 1;
        }

        .feedback-card {
        background: #ffffff;
        border-radius: 12px;
        padding: 16px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        flex: 1;
        }

        .form-group {
        margin-bottom: 12px;
        }

        .form-label {
        display: block;
        font-size: 14px;
        color: #2c3e50;
        margin-bottom: 4px;
        font-weight: 500;
        }

        .form-input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #e8ecf0;
        border-radius: 6px;
        font-size: 14px;
        background: #ffffff;
        box-sizing: border-box;
        transition: all 0.3s ease;
        font-family: inherit;
        height: 36px;
        }

        .form-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .button-group {
        display: flex;
        gap: 10px;
        margin-top: 16px;
        }

        .btn {
        flex: 1;
        padding: 10px 20px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        }

        .btn-primary {
        background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(0, 188, 212, 0.4);
        }

        .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 188, 212, 0.5);
        }

        .btn-secondary {
        background: #ffffff;
        color: #667eea;
        border: 2px solid #e8ecf0;
        }

        .btn-secondary:hover {
        background: #f8f9ff;
        border-color: #667eea;
        transform: translateY(-1px);
        }

        /* 响应式设计 */
        @media (max-width: 360px) {
        .mobile-container {
        padding: 10px 12px;
        }

        .result-card,
        .feedback-card {
        padding: 12px;
        }

        .card-title {
        font-size: 15px;
        margin-bottom: 10px;
        }

        .contact-grid {
        gap: 6px;
        }

        .button-group {
        flex-direction: column;
        gap: 8px;
        }

        .btn {
        padding: 8px 16px;
        font-size: 13px;
        height: 36px;
        }
        }

        @media (min-width: 361px) and (max-width: 375px) {
        .mobile-container {
        padding: 12px 14px;
        }

        .result-card,
        .feedback-card {
        padding: 14px;
        }
        }

        @media (min-width: 430px) {
        .mobile-container {
        padding: 16px 20px;
        }

        .result-card,
        .feedback-card {
        padding: 20px;
        }

        .card-title {
        font-size: 18px;
        }

        .contact-grid {
        gap: 10px;
        }
        }

        /* 横屏适配 */
        @media (orientation: landscape) and (max-height: 500px) {
        .mobile-container {
        padding: 8px 16px;
        }

        .result-card,
        .feedback-card {
        padding: 12px;
        }

        .card-title {
        font-size: 14px;
        margin-bottom: 8px;
        }

        .result-item {
        margin-bottom: 8px;
        }

        .form-group {
        margin-bottom: 8px;
        }

        .button-group {
        margin-top: 12px;
        }
        }

        /* 防止横向滚动 */
        @media (max-width: 480px) {
        .mobile-container {
        overflow-x: hidden;
        }
        }
    </style>

    <div class="mobile-container" use="com.cisdi.ui.SurveyResultDiv" id="surveyResult">
        <!-- 问卷结果卡片 -->
        <div class="result-card">
            <div class="card-title">问卷结果</div>

            <!-- 客户单位名称 -->
            <div class="result-item">
                <div class="result-label">
                    <span class="required">*</span>1.客户单位名称：
                </div>
                <div id="customerNameResult" class="result-value">xxxxx</div>
            </div>

            <!-- 项目名称 -->
            <div class="result-item">
                <div class="result-label">
                    <span class="required">*</span>2.项目名称：
                </div>
                <div id="projectNameResult" class="result-value">xxxxx</div>
            </div>

            <!-- 需反馈/投诉的问题 -->
            <div class="result-item">
                <div class="result-label">
                    <span class="required">*</span>3.需反馈/投诉的问题：
                </div>
                <div id="problemDescResult" class="result-value large">xxxxxxxx</div>
            </div>

            <!-- 联系方式 -->
            <div class="result-item">
                <div class="result-label">4.联系方式：</div>
                <div class="contact-grid">
                    <div class="contact-item">
                        <div class="contact-label">
                            <span class="required">*</span>姓名：
                        </div>
                        <div id="contactNameResult" class="contact-value">xxxxx</div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-label">
                            <span class="required">*</span>电话：
                        </div>
                        <div id="contactPhoneResult" class="contact-value">xxxxx</div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-label">
                            <span class="required">*</span>邮箱：
                        </div>
                        <div id="contactEmailResult" class="contact-value">xxxxx</div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-label">分厂：</div>
                        <div id="factoryResult" class="contact-value">xxxxx</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 反馈指派卡片 -->
        <div class="feedback-card">
            <div class="card-title">反馈指派</div>

            <!-- 项目 -->
            <div class="form-group">
                <div class="form-label">
                    <span class="required">*</span>项目：
                </div>
                <textbox id="assignProject" class="form-input" placeholder="请输入项目名称"/>
            </div>

            <!-- 责任人 -->
            <div class="form-group">
                <div class="form-label">
                    <span class="required">*</span>责任人：
                </div>
                <textbox id="assignResponsible" class="form-input" placeholder="请输入责任人"/>
            </div>

            <!-- 抄送人 -->
            <div class="form-group">
                <div class="form-label">抄送人：</div>
                <textbox id="assignCc" class="form-input" placeholder="请输入抄送人"/>
            </div>

            <!-- 按钮组 -->
            <!-- 按钮组 -->
            <h:div class="button-group">
                <h:button class="btn btn-primary" onclick="submitAssignment()">提交</h:button>
                <h:button class="btn btn-secondary" onclick="goBack()">返回</h:button>
            </h:div>//反馈
        </div>
    </div>

    <script><![CDATA[
        // 获取ZK组件值的辅助函数
        function getZKValue(componentId) {
            try {
                var comp = zk.Widget.$('$' + componentId);
                return comp ? comp.getValue() : '';
            } catch (e) {
                var element = document.getElementById(componentId);
                if (element) {
                    return element.value || element.textContent || '';
                }
                return '';
            }
        }

        // 设置结果显示值
        function setResultValue(elementId, value) {
            var element = document.getElementById(elementId);
            if (element) {
                element.textContent = value || 'xxxxx';
            }
        }

        // 验证指派表单
        function validateAssignment() {
            var project = getZKValue('assignProject').trim();
            var responsible = getZKValue('assignResponsible').trim();

            if (!project) {
                alert('请输入项目名称');
                return false;
            }

            if (!responsible) {
                alert('请输入责任人');
                return false;
            }

            return true;
        }

        // 提交指派
        function submitAssignment() {
         alert('提交');
            if (validateAssignment()) {
                var data = {
                    project: getZKValue('assignProject'),
                    responsible: getZKValue('assignResponsible'),
                    cc: getZKValue('assignCc')
                };


                // 发送到后端处理
                zAu.send(new zk.Event(zk.Widget.$('#surveyResult'), 'submitAssignment', data));
            }
        }

        // 返回功能
        function goBack() {
            if (confirm('确定要返回吗？未保存的数据将丢失��')) {
                // 发送返回事件到后端
                zAu.send(new zk.Event(zk.Widget.$('#surveyResult'), 'goBack', null));
            }
        }

        // 页面初始化
        setTimeout(function() {
            // 模拟加载数据
            console.log('页面初始化完成');
        }, 100);
    ]]></script>

</zk>