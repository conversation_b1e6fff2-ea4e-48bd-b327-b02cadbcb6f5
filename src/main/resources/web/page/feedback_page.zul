<?xml version="1.0" encoding="UTF-8"?>
<?meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"?>
<?meta name="format-detection" content="telephone=no"?>
<?meta name="apple-mobile-web-app-capable" content="yes"?>
<?meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"?>

<zk xmlns:h="http://www.w3.org/1999/xhtml" xmlns:w="http://www.zkoss.org/2005/zk/client">

    <style>
        body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        background: #f5f5f5;
        min-height: 100vh;
        }

        .mobile-container {
        max-width: 430px;
        margin: 0 auto;
        padding: 16px;
        background: transparent;
        min-height: 100vh;
        box-sizing: border-box;
        }

        .result-card {
        background: #ffffff;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .card-title {
        font-size: 18px;
        font-weight: 600;
        color: #333333;
        margin-bottom: 16px;
        text-align: left;
        }

        .result-item {
        margin-bottom: 16px;
        }

        .result-label {
        font-size: 16px;
        color: #333333;
        margin-bottom: 8px;
        font-weight: normal;
        }

        .required {
        color: #ff4444;
        margin-right: 2px;
        }

        .result-value {
        background: #f0f0f0;
        border-radius: 4px;
        padding: 12px;
        font-size: 16px;
        color: #666666;
        min-height: 20px;
        line-height: 1.4;
        }

        .result-value.large {
        min-height: 80px;
        }

        .contact-grid {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-top: 8px;
        }

        .contact-item {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 12px;
        }

        .contact-label {
        font-size: 16px;
        color: #333333;
        font-weight: normal;
        min-width: 60px;
        flex-shrink: 0;
        }

        .contact-value {
        background: #f0f0f0;
        border-radius: 4px;
        padding: 12px;
        font-size: 16px;
        color: #666666;
        flex: 1;
        }

        .feedback-card {
        background: #ffffff;
        border-radius: 8px;
        padding: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .form-group {
        margin-bottom: 20px;
        }

        .form-label {
        display: block;
        font-size: 16px;
        color: #333333;
        margin-bottom: 8px;
        font-weight: normal;
        }

        .form-textarea {
        width: 100%;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 16px;
        background: #ffffff;
        box-sizing: border-box;
        font-family: inherit;
        min-height: 120px;
        resize: vertical;
        }

        .form-textarea:focus {
        outline: none;
        border-color: #007aff;
        }

        .upload-section {
        margin-bottom: 20px;
        }

        .upload-button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border: 2px dashed #00bcd4;
        border-radius: 4px;
        background: #ffffff;
        color: #00bcd4;
        font-size: 24px;
        cursor: pointer;
        margin-right: 12px;
        }

        .upload-hint {
        font-size: 14px;
        color: #999999;
        line-height: 1.4;
        }

        .status-section {
        margin-bottom: 24px;
        }

        .status-options {
        display: flex;
        gap: 24px;
        }

        .status-option {
        display: flex;
        align-items: center;
        gap: 8px;
        }

        .status-radio {
        width: 18px;
        height: 18px;
        }

        .status-label {
        font-size: 16px;
        color: #333333;
        }

        .button-group {
        display: flex;
        gap: 12px;
        margin-top: 24px;
        }

        .btn {
        flex: 1;
        padding: 12px 24px;
        border: none;
        border-radius: 4px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        text-align: center;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        }

        .btn-primary {
        background: #00bcd4;
        color: white;
        }

        .btn-primary:hover {
        background: #00acc1;
        }

        .btn-secondary {
        background: #ffffff;
        color: #333333;
        border: 1px solid #ddd;
        }

        .btn-secondary:hover {
        background: #f5f5f5;
        }

        /* 响应式设计 */
        @media (max-width: 360px) {
        .mobile-container {
        padding: 12px;
        }

        .result-card,
        .feedback-card {
        padding: 12px;
        }

        .card-title {
        font-size: 16px;
        }

        .result-label,
        .contact-label,
        .form-label,
        .status-label {
        font-size: 14px;
        }

        .result-value,
        .contact-value,
        .form-textarea {
        font-size: 14px;
        }

        .btn {
        font-size: 14px;
        height: 44px;
        }
        }

        @media (min-width: 430px) {
        .mobile-container {
        padding: 20px;
        }

        .result-card,
        .feedback-card {
        padding: 20px;
        }
        }

        /* 横屏适配 */
        @media (orientation: landscape) and (max-height: 500px) {
        .mobile-container {
        padding: 12px;
        }

        .result-card,
        .feedback-card {
        padding: 12px;
        }

        .result-item {
        margin-bottom: 12px;
        }

        .form-group {
        margin-bottom: 16px;
        }

        .form-textarea {
        min-height: 80px;
        }
        }
    </style>

    <div class="mobile-container" use="com.cisdi.ui.FeedbackResultDiv" id="surveyResult">
        <!-- 问卷结果卡片 -->
        <div class="result-card">
            <div class="card-title">问卷结果</div>

            <!-- 客户单位名称 -->
            <div class="result-item">
                <div class="result-label">
                    <span class="required">*</span>1.客户单位名称：
                </div>
                <div id="customerNameResult" class="result-value">xxxxx</div>
            </div>

            <!-- 项目名称 -->
            <div class="result-item">
                <div class="result-label">
                    <span class="required">*</span>2.项目名称：
                </div>
                <div id="projectNameResult" class="result-value">xxxxx</div>
            </div>

            <!-- 需反馈/投诉的问题 -->
            <div class="result-item">
                <div class="result-label">
                    <span class="required">*</span>3.需反馈/投诉的问题：
                </div>
                <div id="problemDescResult" class="result-value large">xxxxxxxx</div>
            </div>

            <!-- 联系方式 -->
            <div class="result-item">
                <div class="result-label">4.联系方式：</div>
                <div class="contact-grid">
                    <div class="contact-item">
                        <div class="contact-label">
                            <span class="required">*</span>姓名：
                        </div>
                        <div id="contactNameResult" class="contact-value">xxxxx</div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-label">
                            <span class="required">*</span>电话：
                        </div>
                        <div id="contactPhoneResult" class="contact-value">xxxxx</div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-label">
                            <span class="required">*</span>邮箱：
                        </div>
                        <div id="contactEmailResult" class="contact-value">xxxxx</div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-label">分厂：</div>
                        <div id="factoryResult" class="contact-value">xxxxx</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 反馈处理卡片 -->
        <div class="feedback-card">
            <div class="card-title">反馈处理</div>

            <!-- 处理结果 -->
            <div class="form-group">
                <div class="form-label">
                    <span class="required">*</span>处理结果：
                </div>
                <textbox id="processResult" class="form-textarea" multiline="true" rows="5" placeholder="请输入处理结果"/>
            </div>

            <!-- 附件上传 -->
            <div class="form-group upload-section">
                <div class="form-label">附件：</div>
                <div style="display: flex; align-items: flex-start; gap: 12px;">
                    <div class="upload-button" onclick="uploadFile()">+</div>
                    <div class="upload-hint">提示：可上传与客户沟通记录截图</div>
                </div>
            </div>

            <!-- 问题处理状态 -->
            <div class="form-group status-section">
                <div class="status-options">
                    <div class="status-option">
                        <radio id="statusCompleted" name="processStatus" class="status-radio" checked="true"/>
                        <label for="statusCompleted" class="status-label">问题处理完成</label>
                    </div>
                    <div class="status-option">
                        <radio id="statusPending" name="processStatus" class="status-radio"/>
                        <label for="statusPending" class="status-label">问题处理未完成</label>
                    </div>
                </div>
            </div>

            <!-- 按钮组 -->
            <div class="button-group">
                <button class="btn btn-primary" onclick="submitProcess()">提交</button>
                <button class="btn btn-secondary" onclick="goBack()">返回</button>
            </div>
        </div>
    </div>

    <script><![CDATA[
        // 获取ZK组件值的辅助函数
        function getZKValue(componentId) {
            try {
                var comp = zk.Widget.$('$' + componentId);
                return comp ? comp.getValue() : '';
            } catch (e) {
                var element = document.getElementById(componentId);
                if (element) {
                    return element.value || element.textContent || '';
                }
                return '';
            }
        }

        // 设置结果显示值
        function setResultValue(elementId, value) {
            var element = document.getElementById(elementId);
            if (element) {
                element.textContent = value || 'xxxxx';
            }
        }

        // 验证处理表单
        function validateProcess() {
            var result = getZKValue('processResult').trim();

            if (!result) {
                alert('请输入处理结果');
                return false;
            }

            return true;
        }

        // 上传文件
        function uploadFile() {
            alert('上传文件功能');
            // 这里可以实现文件上传逻辑
        }

        // 提交处理结果
        function submitProcess() {
            if (validateProcess()) {
                var statusCompleted = zk.Widget.$('$statusCompleted');
                var isCompleted = statusCompleted ? statusCompleted.isChecked() : true;

                var data = {
                    processResult: getZKValue('processResult'),
                    isCompleted: isCompleted
                };

                alert('提交处理结果');
                // 发送到后端处理
                zAu.send(new zk.Event(zk.Widget.$('#surveyResult'), 'submitProcess', data));
            }
        }

        // 返回功能
        function goBack() {
            if (confirm('确定要返回吗？未保存的数据将丢失。')) {
                // 发送返回事件到后端
                zAu.send(new zk.Event(zk.Widget.$('#surveyResult'), 'goBack', null));
            }
        }

        // 页面初始化
        setTimeout(function() {
            console.log('反馈处理页面初始化完成');
        }, 100);
    ]]></script>

</zk>