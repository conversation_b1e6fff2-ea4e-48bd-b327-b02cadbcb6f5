/* 全局样式 */
body {
    padding: 0 2px;
    font-size: 13px;
    font-family: "Microsoft YaHei", serif;
}

.z-tabbox {
    border: 0;
    border-radius: unset;
}

/* 工具条样式 */
.z-toolbar.z-toolbar-tabs {
    background: transparent;
    display: grid;
    place-items: center;
}

.z-a {
    color: #0d5880;
    text-decoration: none;
}

/* 按钮和图标 */
.z-button-image {
    width: 16px;
    height: 16px;
}

.nav_toolbar, .z-toolbarbutton {
    padding: 0 !important;
}

.z-button {
    border: none;
    cursor: pointer;
    background: #3498db;
    color: white;
    border-radius: 4px;
    min-height: 26px;
    transition: transform 0.2s;
}

.z-button:hover {
    transform: scale(1.1);
}

.toolbar-btn {
    color: #3a92c8 !important;
    font-size: 13px !important;
}

/* 窗口和面板 */
.z-window-content {
    overflow: auto !important;
}

.z-window-header {
    padding: 0 0 2px 12px;
}

.z-window, .z-window-content {
    padding: 0;
    border-radius: 8px;
    gap: 2px;
}

.grid-wnd > .z-window-content {
    gap: 0;
}

.z-tabpanel {
    overflow: auto;
    padding: 3px;
}

.z-popup-content {
    padding: 4px !important;
}

/* 列表和表格 */
.z-listheader, .z-auxheader, .z-treecol, .grid-header {
    border-left: 1px solid #F2F2F2;
    border-bottom: 1px solid #F2F2F2;
    background: #00A2CF;
}

.work_info > .z-caption-content, .z-caption .z-label {
    display: block !important;
    line-height: 24px !important;
}

.z-caption-content > * {
    margin-left: 0;
}

.z-caption-label {
    font-size: 13px;
}

.z-listbox-header {
    background: #00a3cf !important;
}

.z-listbox {
    border-radius: 8px 8px 0 0;
    border-bottom: none;
    background: transparent;
}

.z-listitem.z-listitem-selected > .z-listcell {
    background: #00A2CF;
}

.z-listitem-focus > .z-listcell, .z-treerow-focus > .z-treecell {
    box-shadow: none !important;
}

.z-listcell-content {
    color: #616161 !important;
}

.z-listheader-content, .z-listcell-content,
.z-listgroup-content, .z-listgroupfoot-content,
.z-listfooter-content {
    text-overflow: ellipsis;
    white-space: nowrap;
    padding-right: 0 !important;
}

.z-listheader-button {
    box-shadow: inset 0 0 #7ac8ff;
}

/* 树形控件 */
.z-orgnode {
    padding: 6px 10px;
    border-radius: 6px;
    background-color: #7ac8ff;
    color: #fff;
}

.first_org > .z-orgnode {
    display: none;
}

.z-orgitem-selected > .z-orgnode {
    color: #fff;
    background-color: #3e7ca7 !important;
}

.z-treerow .z-treecell {
    border: none;
}

.z-treecell-content {
    line-height: 24px;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 表单元素 */
.z-hbox td, .z-vbox td {
    padding: 0;
    background-clip: padding-box;
    vertical-align: middle;
}

input[type="checkbox"]:checked {
    background-color: #00A2CF;
    color: #FFFFFF;
}

.table-coledit > .z-combobox-input {
    color: #616161 !important;
}

.z-chosenbox, .z-cascader-label {
    text-align: left !important;
}

.z-cascader-icon {
    font-size: 12px !important;
}

.z-chosenbox-item {
    margin: 2px;
}

/* 禁用状态 */
.z-combobox-disabled > input,
.z-bandbox-disabled > input,
.z-datebox-disabled > input,
.z-checkbox-disabled,
.z-combobox-disabled {
    color: rgba(0, 0, 0, 0.8) !important;
    background: transparent !important;
    cursor: default !important;
}

.z-textbox[disabled],
.z-decimalbox[disabled],
.z-intbox[disabled],
.z-longbox[disabled],
.z-doublebox[disabled] {
    background: transparent !important;
}

/* 分页 */
.z-paging {
    background: #FFFFFF !important;
    padding: 5px 16px 3px 2px;
}

.z-paging-os {
    padding-bottom: 0;
}

.paging-set {
    padding: 0 4px;
    display: flex;
    gap: 2px;
    align-items: center;
    border: 1px solid #d9d9d9;
    border-radius: 0 0 8px 8px;
}

.z-paging-info {
    font-size: 13px;
    padding: 8px 0;
    position: absolute;
    top: 0;
    color: rgba(0, 0, 0, 0.9);
    right: 16px;
}

/* 特殊样式类 */
.tool-bar {
    border-radius: 5px;
    display: flex;
    align-items: center;
    height: 34px;
}

.z-label-req {
    color: red;
    font-weight: 300;
    font-size: 15px;
    position: absolute;
    top: 9px;
}

.z-tree {
    border: none;
}

.z-auxheader-content, .aux-header > .z-listheader-content {
    padding: 1px;
}

.page-search {
    float: left;
    border-bottom: none;
}

.page-search td {
    border-left: 0 !important;
    border-bottom: 0 !important;
}

.item_add > .z-listcell,
.item_add > .z-treecell,
.item_update > .z-treecell,
.item_update > .z-cell,
.item_update > .z-listcell {
    background: #FFCC99;
}

.zscelltxt-wrap {
    max-height: fit-content !important;
}

.filter > .z-row-inner {
    text-align: center;
}

.z-nav-content,
.navitem-first > .z-navitem-content {
    padding: 6px 4px !important;
}

.z-listbox-body table th,
.z-listbox-body table td,
.z-grid-body table td {
    vertical-align: middle !important;
}

.btn-vertical {
    padding-right: 10px;
}

/* 动画效果 */
.div-center {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    transform-style: preserve-3d;
    perspective: 1000px;
    transition: transform 0.8s;
    cursor: pointer;
    height: 100%;
}

.div-center:hover {
    transform: rotateY(15deg);
}

.front {
    position: absolute;
    width: 90%;
    height: 90%;
    backface-visibility: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    border-radius: 12px;
    transform: translateZ(20px);
}

/* 工具栏 */
.listwnd-toolar {
    height: 32px;
    margin-top: -32px;
    position: relative;
    margin-left: 270px;
    background: transparent;
    float: left !important;
    display: flex;
    align-items: center;
}

.z-groupbox-3d > .z-groupbox-header {
    border-radius: 8px 8px 0 0;
}

.z-groupbox-3d > .z-groupbox-content {
    border-radius: 0 0 8px 8px;
}

.z-listgroup-inner {
    text-align: left !important;
}

.item_del > .z-treecell,
.item_del > .z-listcell {
    text-decoration: line-through;
    font-style: oblique;
}


.z-messagebox-viewport {
    max-height: 600px;
}

.grid-group {
    display: none;
    border-top: 1px solid #F2F2F2;
}

.z-grid-body .z-cell {
    border-top: 1px solid #F2F2F2;
}

.z-row .z-cell {
    border-top: 1px solid #F2F2F2;
}

.grid-item-select {
    background: #00A2CF;
}

.z-listfooter-content {
    color: red !important;
}

.z-listbox-footer .z-listfooter {
    overflow: hidden;
    background: #ffffff !important;
}

.z-tree-body {
    background: #ffffff !important;
}
