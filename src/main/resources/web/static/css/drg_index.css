/* 病案质控DRG管理平台首页样式 */

/* 导航栏样式 */
.drg-navbar {
    border: none !important;
    padding: 0;
    margin: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 60px;
    position: relative;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #2563eb 100%);
    border-radius: 12px 12px 0 0;
    box-shadow: 0 4px 20px rgba(30, 64, 175, 0.3);
}

.drg-navbar .navbar-brand {
    color: #fff;
    font-size: 24px;
    font-weight: 700;
    width: 450px;
    display: inline-flex;
    justify-content: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.drg-navbar .navbar-brand::before {
    margin-right: 10px;
    font-size: 28px;
}

.drg-navbar .navbar-function {
    color: #fff;
    margin-right: 12px;
    font-size: 16px;
    font-weight: 500;
    text-shadow: none;
    padding: 14px 16px 12px;
    display: inline-block;
    border-radius: 20px;
    transform: perspective(1px) translateZ(0);
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.drg-navbar .navbar-function:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.15);
}

.drg-navbar-function-active {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
    border-radius: 20px;
    color: #fff !important;
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4) !important;
}

/* 用户菜单样式 */
.drg-nav-user > div > a,
.drg-nav-user > div .user-menu {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    border-radius: 10px;
    color: #FFF;
    display: block;
    margin-right: 20px;
    line-height: 50px;
    text-align: center;
    padding: 0 15px;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
}

.drg-nav-user > div > a:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(30, 64, 175, 0.4);
}

.drg-nav-user > div [class*="z-icon-"] {
    font-size: 18px;
    color: #ffffff;
    display: inline-block;
    width: 22px;
    text-align: center;
    margin-right: 8px;
}

/* 侧边栏样式 */
.drg-sidebar {
    margin-top: -1px;
    border: 1px #e2e8f0 solid;
    border-radius: 0 0 12px 12px;
    border-top: none;
    width: 220px;
    float: left;
    position: relative;
    background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
    margin-bottom: 3px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.drg-sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
    z-index: 1;
}

.drg-sidebar-min {
    width: 60px;
}

.drg-sidebar-min .drg-nav-list .z-nav-content {
    text-align: center;
    padding: 14px 8px;
}

.drg-sidebar-min .drg-nav-list .z-nav [class*="z-icon-"] {
    margin-right: 0;
    font-size: 18px;
}

/* 导航列表样式 */
.drg-nav-list.z-navbar > ul > li {
    border-top: 1px solid #f8fafc;
    border-bottom: 1px solid #f1f5f9;
    position: relative;
    transition: all 0.3s ease;
    margin: 2px 0;
}

.drg-nav-list.z-navbar > ul > li:hover {
    background: linear-gradient(90deg, #f0f9ff 0%, #e0f2fe 100%);
    border-left: 3px solid #3b82f6;
    border-radius: 0 8px 8px 0;
    margin-right: 4px;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.drg-nav-list .z-nav-content {
    padding: 14px 18px;
    color: #475569;
    font-weight: 500;
    font-size: 15px;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 2px 6px;
}

.drg-nav-list .z-nav-content:hover {
    color: #1e40af;
    background: rgba(59, 130, 246, 0.05);
    transform: translateX(2px);
}

.drg-nav-list .z-nav [class*="z-icon-"] {
    color: #64748b;
    margin-right: 12px;
    font-size: 16px;
    width: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.drg-nav-list .z-nav:hover [class*="z-icon-"] {
    color: #3b82f6;
    transform: scale(1.1);
}

/* 选中状态 */
.drg-nav-list .z-nav-selected .z-nav-content {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: #fff !important;
    border-radius: 10px;
    margin: 4px 8px;
    padding: 12px 16px;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
    border-left: 4px solid #1e40af;
    font-weight: 600;
    position: relative;
    transform: translateX(2px);
    transition: all 0.3s ease;
}

.drg-nav-list .z-nav-selected .z-nav-content::before {
    content: '';
    position: absolute;
    left: -4px;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #60a5fa 0%, #3b82f6 100%);
    border-radius: 0 3px 3px 0;
}

.drg-nav-list .z-nav-selected .z-nav-content:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    transform: translateX(4px);
}

.drg-nav-list .z-nav-selected [class*="z-icon-"] {
    color: #fff !important;
    font-size: 18px;
}

/* 子菜单样式 */
.drg-nav-list .z-navitem {
    padding: 6px 16px 6px 32px;
    margin: 2px 8px;
    border-radius: 6px;
    background: transparent;
    transition: all 0.3s ease;
    position: relative;
}

.drg-nav-list .z-navitem:hover {
    background: linear-gradient(90deg, #f0f9ff 0%, #e0f2fe 100%);
    color: #1e40af;
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.drg-nav-list .z-navitem .z-navitem-content {
    color: #64748b;
    font-weight: 500;
    font-size: 14px;
    transition: color 0.3s ease;
}

.drg-nav-list .z-navitem:hover .z-navitem-content {
    color: #1e40af;
}

.drg-nav-list .z-navitem [class*="z-icon-"] {
    color: #94a3b8;
    margin-right: 8px;
    font-size: 14px;
    transition: color 0.3s ease;
}

.drg-nav-list .z-navitem:hover [class*="z-icon-"] {
    color: #3b82f6;
}

.drg-nav-list .z-navitem-selected {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: #fff !important;
    border-radius: 8px;
    margin: 3px 8px 3px 24px;
    padding: 8px 16px;
    box-shadow: 0 3px 12px rgba(59, 130, 246, 0.25);
    border-left: 3px solid #1e40af;
    position: relative;
    transform: translateX(4px);
    transition: all 0.3s ease;
}

.drg-nav-list .z-navitem-selected::before {
    content: '';
    position: absolute;
    left: -3px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(180deg, #60a5fa 0%, #3b82f6 100%);
    border-radius: 0 2px 2px 0;
}

.drg-nav-list .z-navitem-selected:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.35);
    transform: translateX(6px);
}

/* 主内容区域 */
.drg-main {
    background: #f8fafc;
    border-radius: 0 0 12px 12px;
}

/* 标签页样式 */
/*.drg-tabbox .z-tabpanels {*/
/*    background: #ffffff;*/
/*    border-radius: 8px;*/
/*    margin: 8px;*/
/*    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);*/
/*}*/

.drg-tabbox .z-tabs {
    background: linear-gradient(180deg, #f1f5f9 0%, #e2e8f0 100%);
    border-bottom: 2px solid #3b82f6;
}

.drg-tabbox .z-tab {
    background: transparent;
    border: none;
    color: #64748b;
    font-weight: 500;
    padding: 12px 20px;
    margin-right: 4px;
    border-radius: 8px 8px 0 0;
    transition: all 0.3s ease;
}

.drg-tabbox .z-tab:hover {
    background: rgba(59, 130, 246, 0.1);
    color: #1e40af;
}

.drg-tabbox .z-tab-selected {
    background: #ffffff !important;
    color: #1e40af !important;
    font-weight: 600;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

/* 侧边栏折叠按钮 */
.drg-sidebar-collapse {
    position: absolute;
    bottom: 10px;
    right: 10px;
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.drg-sidebar-collapse:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.drg-sidebar-collapse [class*="z-icon-"] {
    color: #fff;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .drg-navbar .navbar-brand {
        font-size: 20px;
        width: 300px;
    }

    .drg-sidebar {
        width: 180px;
    }
}

@media (max-width: 768px) {
    .drg-navbar .navbar-brand {
        font-size: 18px;
        width: 250px;
    }

    .drg-sidebar {
        width: 60px;
    }

    .drg-sidebar:not(.drg-sidebar-min) {
        position: absolute;
        z-index: 1000;
        width: 220px;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
    }
}

/* 加载动画 */
.drg-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #64748b;
}

.drg-loading::before {
    content: '';
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 15px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* 医疗主题装饰 */
.drg-medical-decoration {
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    opacity: 0.1;
    font-size: 120px;
    color: #3b82f6;
    pointer-events: none;
}

/* 统计卡片样式 */
.drg-stats-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 12px;
    padding: 20px;
    margin: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border-left: 4px solid #3b82f6;
    transition: all 0.3s ease;
}

.drg-stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.drg-stats-card .card-title {
    color: #1e40af;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
}

.drg-stats-card .card-value {
    color: #059669;
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 5px;
}

.drg-stats-card .card-desc {
    color: #64748b;
    font-size: 14px;
}

.z-hbox td, .z-vbox td {
    padding: 0;
    background-clip: padding-box;
    vertical-align: top !important;
}


/* 菜单项动画效果 */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulseGlow {
    0%, 100% {
        box-shadow: 0 3px 12px rgba(59, 130, 246, 0.25);
    }
    50% {
        box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 应用动画 */
.drg-nav-list .z-nav {
    animation: slideInLeft 0.3s ease-out;
    animation-fill-mode: both;
}

.drg-nav-list .z-nav:nth-child(1) {
    animation-delay: 0.1s;
}

.drg-nav-list .z-nav:nth-child(2) {
    animation-delay: 0.2s;
}

.drg-nav-list .z-nav:nth-child(3) {
    animation-delay: 0.3s;
}

.drg-nav-list .z-nav:nth-child(4) {
    animation-delay: 0.4s;
}

.drg-nav-list .z-nav:nth-child(5) {
    animation-delay: 0.5s;
}

.drg-nav-list .z-navitem {
    animation: fadeInUp 0.3s ease-out;
    animation-fill-mode: both;
}

.drg-nav-list .z-navitem-selected {
    animation: pulseGlow 3s ease-in-out infinite;
}

/* 悬停时的光效动画 */
.drg-nav-list .z-nav-content,
.drg-nav-list .z-navitem {
    position: relative;
    overflow: hidden;
}

.drg-nav-list .z-nav-content::after,
.drg-nav-list .z-navitem::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.15), transparent);
    transition: left 0.6s ease;
    pointer-events: none;
}

.drg-nav-list .z-nav-content:hover::after,
.drg-nav-list .z-navitem:hover::after {
    left: 100%;
}

/* 选中状态的特殊效果 */
.drg-nav-list .z-navitem-selected::before {
    animation: slideInLeft 0.5s ease-out;
}

/* 侧边栏折叠动画 */
.drg-sidebar {
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.drg-sidebar.drg-sidebar-min {
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 图标旋转效果 */
.drg-nav-list .z-nav [class*="z-icon-"],
.drg-nav-list .z-navitem [class*="z-icon-"] {
    transition: transform 0.3s ease, color 0.3s ease;
}

.drg-nav-list .z-nav:hover [class*="z-icon-"],
.drg-nav-list .z-navitem:hover [class*="z-icon-"] {
    transform: scale(1.1) rotate(5deg);
}

/* ==================== DRG表格样式 ==================== */

/* 表格容器 */
.drg-table-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    border: none;
    /*margin: 8px;*/
    /*border: 1px solid #e2e8f0;*/
}

/* 表格主体 */
.drg-table-container .z-listbox {
    border: none;
    border-radius: 0;
    background: transparent;
}

/* 表格头部样式 */
.drg-table-container .z-listheader,
.drg-table-container .z-auxheader,
.drg-table-container .z-treecol,
.drg-table-container .grid-header,
.drg-table-container .z-grid-header {
    background: linear-gradient(135deg, #3d7de5 0%, #3e72e6 100%, #3e72e6 0% 0%) !important;
    color: #ffffff !important;
    border: none !important;
    border-top: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-right: 1px solid rgba(255, 255, 255, 0.2) !important;
    /*padding: 2px 2px !important;*/
    font-weight: 600 !important;
    font-size: 13px !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    position: relative;
}

.drg-table-container .z-listheader:last-child,
.drg-table-container .z-auxheader:last-child,
.drg-table-container .z-treecol:last-child,
.drg-table-container .grid-header:last-child {
    border-right: none !important;
}

.drg-table-container .z-listheader-content,
.drg-table-container .z-auxheader-content {
    color: #ffffff !important;
    font-weight: 600 !important;
}

/*!* 表格头部悬停效果 *!*/
/*.drg-table-container .z-listheader:hover,*/
/*.drg-table-container .z-auxheader:hover {*/
/*    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%) !important;*/
/*    transform: translateY(-1px);*/
/*    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);*/
/*    transition: all 0.3s ease;*/
/*}*/

/* 表格行样式 */
.drg-table-container .z-listitem,
.drg-table-container .z-row {
    border-bottom: 1px solid #f1f5f9 !important;
    transition: all 0.3s ease;
}

/*.drg-table-container .z-listitem:hover,*/
/*.drg-table-container .z-row:hover {*/
/*    background: linear-gradient(90deg, #f0f9ff 0%, #e0f2fe 100%) !important;*/
/*    transform: translateX(2px);*/
/*    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);*/
/*}*/

/* 表格单元格样式 */
.drg-table-container .z-listcell,
.drg-table-container .z-cell {
    padding: 4px 4px !important;
    vertical-align: middle !important;
}

.drg-table-container .z-listcell:last-child,
.drg-table-container .z-cell:last-child {
    border-right: none !important;
}

.drg-table-container .z-listcell-content,
.drg-table-container .z-cell-content {
    color: #475569 !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
}

/* 选中行样式 */
.drg-table-container .z-listitem-selected,
.drg-table-container .z-row-selected {
    background: linear-gradient(90deg, #dbeafe 0%, #bfdbfe 100%) !important;
    border: none !important;
}

.drg-table-container .z-listitem-selected .z-listcell,
.drg-table-container .z-row-selected .z-cell {
    background: transparent !important;
}

.drg-table-container .z-listitem-selected .z-listcell-content,
.drg-table-container .z-row-selected .z-cell-content {
    color: #1e40af !important;
    font-weight: 500 !important;
}

/* 表格底部样式 */
.drg-table-container .z-listbox-footer,
.drg-table-container .z-grid-footer {
    background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%) !important;
    border-top: 2px solid #e2e8f0 !important;
}

.drg-table-container .z-listfooter,
.drg-table-container .z-footer {
    background: transparent !important;
    color: #1e40af !important;
    /*font-weight: 600 !important;*/
    /*padding: 10px 16px !important;*/
}

.drg-table-container .z-listfooter-content {
    color: #1e40af !important;
    font-weight: 600 !important;
}

/* 分页样式 */
/*.drg-table-container .z-paging {*/
/*    background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%) !important;*/
/*    border-top: 1px solid #e2e8f0 !important;*/
/*    border-radius: 0 0 12px 12px !important;*/
/*    padding: 12px 16px !important;*/
/*}*/

.drg-table-container .paging-set {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.drg-table-container .z-paging-info {
    color: #64748b !important;
    font-size: 13px !important;
}

/* 表格工具栏样式 */
.drg-table-container .tool-bar {
    border-bottom: 1px solid #e2e8f0;
    padding: 8px 16px;
    border-radius: 12px 12px 0 0;
    background: transparent !important;
}

.drg-table-container .tool-bar .z-button {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
    border: none !important;
    color: #ffffff !important;
    border-radius: 6px !important;
    padding: 6px 12px !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    margin-right: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.drg-table-container .tool-bar .z-button:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

/* 特殊状态行样式 */
.drg-table-container .drg-error {
    background: linear-gradient(90deg, #fef2f2 0%, #fee2e2 100%) !important;
    border-left: 4px solid #ef4444 !important;
}

.drg-table-container .drg-warning {
    background: linear-gradient(90deg, #fffbeb 0%, #fef3c7 100%) !important;
    border-left: 4px solid #f59e0b !important;
}

.drg-table-container .drg-serious {
    background: linear-gradient(90deg, #fdf2f8 0%, #fce7f3 100%) !important;
    border-left: 4px solid #ec4899 !important;
}

.item_add > .z-listcell,
.item_add > .z-treecell,
.item_update > .z-treecell,
.item_update > .z-cell,
.item_update > .z-listcell {
    background: #FFCC99;
}

.item_del > .z-treecell,
.item_del > .z-listcell {
    text-decoration: line-through;
    font-style: oblique;
}

.z-messagebox-viewport {
    max-height: 600px;
}

/*.drg-table-container .item_add > .z-listcell,*/
/*.drg-table-container .item_add > .z-cell {*/
/*    background: linear-gradient(90deg, #f0fdf4 0%, #dcfce7 100%) !important;*/
/*    border-left: 4px solid #22c55e !important;*/
/*}*/

/*.drg-table-container .item_update > .z-listcell,*/
/*.drg-table-container .item_update > .z-cell {*/
/*    background: linear-gradient(90deg, #fefce8 0%, #fef3c7 100%) !important;*/
/*    border-left: 4px solid #eab308 !important;*/
/*}*/

/*.drg-table-container .item_del > .z-listcell,*/
/*.drg-table-container .item_del > .z-cell {*/
/*    background: linear-gradient(90deg, #fef2f2 0%, #fee2e2 100%) !important;*/
/*    border-left: 4px solid #ef4444 !important;*/
/*    text-decoration: line-through;*/
/*    opacity: 0.7;*/
/*}*/

/* 表格复选框样式 */
.drg-table-container .z-listitem-checkbox,
.drg-table-container .z-checkbox {
    accent-color: #3b82f6;
}

.drg-table-container .z-listitem-checkbox:checked,
.drg-table-container .z-checkbox:checked {
    background-color: #3b82f6 !important;
    border-color: #3b82f6 !important;
}

/* 表格排序指示器 */
.drg-table-container .z-listheader-sort,
.drg-table-container .z-auxheader-sort {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 13px !important;
    margin-left: 6px;
    padding: 3px;
    transition: all 0.3s ease;
}

/*.drg-table-container .z-listheader-sort:hover,*/
/*.drg-table-container .z-auxheader-sort:hover {*/
/*    color: #ffffff !important;*/
/*    transform: scale(1.2);*/
/*}*/

/* 表格加载状态 */
.drg-table-loading {
    position: relative;
    overflow: hidden;
}

.drg-table-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    animation: tableLoading 1.5s infinite;
    z-index: 1;
}

@keyframes tableLoading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* 表格空状态 */
.drg-table-empty {
    text-align: center;
    padding: 60px 20px;
    color: #94a3b8;
}

.drg-table-empty .empty-icon {
    font-size: 48px;
    color: #cbd5e1;
    margin-bottom: 16px;
    display: block;
}

.drg-table-empty .empty-text {
    font-size: 16px;
    color: #64748b;
    margin-bottom: 8px;
}

.drg-table-empty .empty-desc {
    font-size: 14px;
    color: #94a3b8;
}

/* 表格筛选器样式 */
.drg-table-container .filter {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
    padding: 8px 16px;
}

.drg-table-container .filter .z-textbox,
.drg-table-container .filter .z-combobox,
.drg-table-container .filter .z-datebox {
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important;
    padding: 6px 12px !important;
    font-size: 13px !important;
    transition: all 0.3s ease;
}

.drg-table-container .filter .z-textbox:focus,
.drg-table-container .filter .z-combobox:focus,
.drg-table-container .filter .z-datebox:focus {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    outline: none !important;
}

/* 表格分组样式 */
.drg-table-container .z-listgroup,
.drg-table-container .z-group {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
    border-top: 2px solid #3b82f6 !important;
    border-bottom: 1px solid #d1d5db !important;
}

.drg-table-container .z-listgroup-content,
.drg-table-container .z-group-content {
    color: #1e40af !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    padding: 10px 16px !important;
}

.drg-table-container .z-listgroupfoot,
.drg-table-container .z-groupfoot {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    border-top: 1px solid #d1d5db !important;
    font-weight: 500 !important;
}

/* 表格响应式设计 */
@media (max-width: 1024px) {
    .drg-table-container {
        margin: 4px;
        border-radius: 8px;
    }

    .drg-table-container .z-listheader,
    .drg-table-container .z-listcell {
        padding: 8px 12px !important;
        font-size: 13px !important;
    }
}

@media (max-width: 768px) {
    .drg-table-container {
        margin: 2px;
        border-radius: 6px;
    }

    .drg-table-container .z-listheader,
    .drg-table-container .z-listcell {
        padding: 6px 8px !important;
        font-size: 12px !important;
    }

    .drg-table-container .tool-bar {
        padding: 6px 12px;
        flex-wrap: wrap;
        gap: 4px;
    }

    .drg-table-container .tool-bar .z-button {
        padding: 4px 8px !important;
        font-size: 12px !important;
        margin-right: 4px;
    }
}

/* 表格动画效果 */
.drg-table-container .z-listitem,
.drg-table-container .z-row {
    animation: fadeInUp 0.3s ease-out;
    animation-fill-mode: both;
}

.drg-table-container .z-listitem:nth-child(1) {
    animation-delay: 0.05s;
}

.drg-table-container .z-listitem:nth-child(2) {
    animation-delay: 0.1s;
}

.drg-table-container .z-listitem:nth-child(3) {
    animation-delay: 0.15s;
}

.drg-table-container .z-listitem:nth-child(4) {
    animation-delay: 0.2s;
}

.drg-table-container .z-listitem:nth-child(5) {
    animation-delay: 0.25s;
}

/* 表格滚动条样式 */
.drg-table-container .z-listbox-body::-webkit-scrollbar,
.drg-table-container .z-grid-body::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.drg-table-container .z-listbox-body::-webkit-scrollbar-track,
.drg-table-container .z-grid-body::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.drg-table-container .z-listbox-body::-webkit-scrollbar-thumb,
.drg-table-container .z-grid-body::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.drg-table-container .z-listbox-body::-webkit-scrollbar-thumb:hover,
.drg-table-container .z-grid-body::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.drg-table-container .tool-bar {
    border-radius: 5px;
    display: flex;
    align-items: center;
    height: 34px;
}

.drg-table-container .paging-set {
    padding: 0 4px;
    display: flex;
    gap: 2px;
    align-items: center;
    border: 1px solid #d9d9d9;
    border-radius: 0 0 8px 8px;
}

.z-paging {
    background: #FFFFFF !important;
    padding: 5px 16px 3px 2px;
}

.z-paging-os {
    padding-bottom: 0;
}

.z-paging-info {
    font-size: 13px;
    padding: 8px 0;
    position: absolute;
    top: 0;
    color: rgba(0, 0, 0, 0.9);
    right: 16px;
}

.z-label-req {
    color: red;
    font-weight: 300;
    font-size: 15px;
    position: absolute;
    top: 9px;
}

.z-a {
    color: #0d5880;
    text-decoration: none;
}

.z-listbox-body {
    border-left: 1px #d9d9d9 solid;
    border-right: 1px #d9d9d9 solid;
}

.z-listhead-menupopup .z-listheader-content {
    padding-right: 2px;
    text-overflow: ellipsis;
}

.z-listhead-bar {
    background: #0064ED;
}

.z-window {
    padding: 1px;
    border-radius: 8px;
}

.listwnd-toolar {
    height: 32px;
    margin-top: -32px;
    position: relative;
    margin-left: 270px;
    background: transparent;
    float: left !important;
    display: flex;
    align-items: center;
}

.toolbar-btn {
    color: #3a92c8 !important;
    font-size: 13px !important;
    padding: 0 !important;
}

.z-listitem-focus > .z-listcell {
    box-shadow: none !important;
}

.z-listheader-checkable {
    width: 16px;
    height: 16px;
    margin-right: 2px;
}

.z-listitem-checkable {
    margin-right: 2px;
}

.z-listbox-header {
    border-radius: 8px 8px 0 0;
}

.z-tabpanel {
    padding: 1px !important;
}

.z-tabbox {
    margin-top: 2px !important;
}

.z-window-content {
    padding: 2px !important;
}

.z-toolbar-button-active {
    background: #3b82f6;
    color: white !important;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
}

.z-grid, .z-tree {
    background: transparent !important;
}

.z-tree {
    border: none !important;
}

.z-window-header {
    margin-left: 15px;
}

.z-listgroup.z-listgroup-open .z-listgroup-inner {
    background: #FFFFFF;
    border-bottom: 2px solid #0093F9;
    text-align: left !important;
}

.z-listcell-content {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.z-listcell-content > .z-a {
    background: #6097f6;
    color: white;
    padding: 5px !important;
    border-radius: 5px;
    margin-right: 5px;
}

.z-listcell-content > .z-a:last-child {
    margin-right: 0 !important;
}

.html-table-action {
    display: grid;
    gap: 5px;
}

.html-table-action > a {
    background: #3a82f6;
    border-radius: 5px;
    color: white;
    font-size: 12px;
    padding: 4px;
    cursor: pointer;
    text-align: center;
}

.html-table-active {
    background: #f38c00 !important;
}

/* 🎯 最强优先级：确保覆盖任何系统生成的样式 */
html body .frozen-table > .z-listbox-body {
    overflow-x: hidden !important;
    overflow-y: auto !important;
    scrollbar-width: thin !important;
    scrollbar-color: #94a3b8 #f8fafc !important;
}

.z-messagebox-window {
    top: 30% !important;
    padding: 10px;
}
