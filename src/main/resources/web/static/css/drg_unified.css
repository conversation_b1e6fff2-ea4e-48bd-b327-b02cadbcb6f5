/* ==================== DRG管理平台统一样式文件 ==================== */
/* 整合了drg_index.css和drg_compact.css，去除重复样式 */

/* ==================== 全局变量 ==================== */
:root {
    --primary-color: #3b82f6;
    --primary-dark: #1d4ed8;
    --secondary-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --success-color: #22c55e;

    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;

    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-tertiary: #94a3b8;

    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
}

/* 紧凑版仪表板容器 */
.drg-compact-dashboard {
    background: var(--bg-secondary);
    height: 100vh;
    display: flex;
    flex-direction: column;
    font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
}

/* 紧凑型顶部区域 */
.compact-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    padding: 10px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow-md);
    position: relative;
    z-index: 10;
}

.header-left {
    display: flex;
    align-items: center;
}

.platform-info {
    display: flex;
    align-items: center;
    gap: 16px;
}

.platform-icon {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.platform-text h2 {
    color: white;
    font-size: 24px;
    font-weight: 700;
    margin: 0 0 4px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.platform-text p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
}

.quick-stats {
    display: flex;
    gap: 24px;
    align-items: center;
}

/* 旧版quick-stat-item样式已移除 - 使用高性能版本hp-stat-item */

.stat-value {
    font-size: 24px;
    font-weight: 800;
    margin-bottom: 2px;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    color: white;
    line-height: 1;
    letter-spacing: -0.5px;
}

.stat-label {
    font-size: 11px;
    opacity: 0.95;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1.2;
}

/* 主要内容区域 */
.compact-main {
    flex: 1;
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 8px;
    padding-left: 1px;
    padding-top: 1px;
    overflow: hidden;
}

/* 2级可折叠应用导航区域 */
.app-navigation {
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
    border-radius: var(--radius-lg) !important;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: all 0.3s ease;
    width: 280px;
    min-width: 280px;
}

.app-navigation.collapsed {
    width: 60px;
    min-width: 60px;
}

/* 2级导航头部 */
.nav-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    position: relative;
    z-index: 10;
    height: 60px; /* 固定高度，防止折叠时高度变化 */
    box-sizing: border-box;
    min-height: 60px;
}

.nav-title {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    transition: all 0.3s ease;
    overflow: hidden; /* 防止文本溢出 */
}

.nav-icon {
    font-size: 18px;
    transition: all 0.3s ease;
}

.nav-text {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    white-space: nowrap; /* 防止文字换行 */
    overflow: hidden;
    flex-shrink: 0; /* 防止文字被压缩 */
}

.nav-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
    transition: all 0.3s ease;
}

.nav-collapse {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: white;
    position: relative;
    overflow: hidden;
}

.nav-collapse::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.nav-collapse:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

.nav-collapse:hover::before {
    left: 100%;
}

.nav-collapse:active {
    transform: scale(0.95);
}

.nav-collapse i {
    transition: all 0.3s ease;
    font-size: 14px;
    z-index: 1;
}

.nav-collapse:hover i {
    transform: scale(1.1);
}

/* ==================== 折叠状态下的样式 ==================== */

/* 折叠状态下的头部 */
.app-navigation.collapsed .nav-header {
    padding: 12px 8px;
    justify-content: space-between; /* 恢复原来的布局 */
    position: relative;
}

.app-navigation.collapsed .nav-text {
    display: none; /* 直接隐藏，不使用opacity和width */
}

.app-navigation.collapsed .nav-title {
    justify-content: center;
    transition: all 0.3s ease;
    flex: 1; /* 占用剩余空间 */
    display: flex;
    align-items: center;
    justify-content: center;
}

.app-navigation.collapsed .nav-icon {
    margin: 0; /* 清除所有边距 */
}

.app-navigation.collapsed .nav-controls {
    display: none; /* 折叠状态下隐藏整个控制区域 */
}

.app-navigation.collapsed .nav-collapse {
    display: none; /* 折叠状态下隐藏折叠按钮 */
}

/* ==================== 折叠状态下的展开按钮 ==================== */
.nav-expand-button {
    display: none; /* 默认隐藏 */
    position: fixed; /* 使用fixed定位，相对于视口 */
    top: 100px;
    left: 19px;
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border-radius: 50%;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 9999; /* 确保在最顶层 */
    transition: all 0.3s ease;
    border: 2px solid white;
    opacity: 0.95;
    align-items: center;
    justify-content: center;
    user-select: none;
}

.app-navigation.collapsed .nav-expand-button {
    display: flex !important; /* 折叠状态下显示，使用!important确保优先级 */
    /* 移除无限循环动画以提升性能 */
}

/* 调试样式 - 可以临时启用 */
.debug-expand-button .nav-expand-button {
    display: flex !important;
    background: red !important;
    border: 3px solid yellow !important;
}

.nav-expand-button:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    opacity: 1;
}

.nav-expand-button:active {
    transform: scale(0.95);
}

/* 展开按钮的脉动动画 */
/* expandButtonPulse关键帧动画已移除 - 性能优化 */

/* 折叠状态下的导航组头部 - 禁用点击 */
.app-navigation.collapsed .nav-group-header {
    padding: 12px 0;
    justify-content: center;
    display: flex;
    align-items: center;
    position: relative;
    cursor: default !important; /* 禁用点击光标 */
    pointer-events: none; /* 完全禁用点击事件 */
}

.app-navigation.collapsed .group-title,
.app-navigation.collapsed .group-arrow {
    display: none;
}

.app-navigation.collapsed .group-icon {
    margin: 0 auto; /* 水平居中 */
}

/* 折叠状态下的二级导航内容 - 默认隐藏，但可以通过JavaScript控制显示 */
.app-navigation.collapsed .nav-group-content {
    display: none;
}

/* 折叠状态下的二级导航内容 - 完全禁用 */
.app-navigation.collapsed .nav-group-content.show {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
}

/* 折叠状态下的导航项 */
.app-navigation.collapsed .nav-item {
    padding: 8px 0;
    justify-content: center;
    display: flex;
    align-items: center;
}

.app-navigation.collapsed .nav-item i {
    margin: 0 auto; /* 水平居中 */
}

.app-navigation.collapsed .nav-item span {
    display: none;
}

/* 折叠状态下不需要二级导航展开功能 */

/* 所有折叠状态下二级导航展开相关样式已删除，因为现在禁用了这个功能 */

/* 确保折叠状态下的导航组有相对定位 */
.app-navigation.collapsed .nav-group {
    position: relative;
}

/* 折叠状态下禁用导航组头部悬停效果 */
.app-navigation.collapsed .nav-group-header:hover {
    background: transparent !important;
    cursor: default !important;
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 折叠状态下的直接应用区域 */
.app-navigation.collapsed .direct-apps-section {
    padding: 8px 0;
    border-bottom: none;
    margin-bottom: 8px;
}

.app-navigation.collapsed .section-title {
    display: none;
}

.app-navigation.collapsed .direct-apps-list {
    display: flex;
    flex-direction: column;
    gap: 0;
    padding: 0;
}

/* 折叠状态下的直接应用项继承nav-item的折叠样式 */
.app-navigation.collapsed .direct-app-item {
    /* 样式继承自 .app-navigation.collapsed .nav-item */
}

.app-navigation.collapsed .nav-separator {
    display: none;
}

/* 折叠状态下的内容区域 */
.app-navigation.collapsed .nav-content {
    padding: 8px 0;
}

/* 折叠状态下的提示功能 */
.app-navigation.collapsed .nav-group-header:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 70px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--text-primary);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    opacity: 0;
    animation: fadeInTooltip 0.3s ease forwards;
}

.app-navigation.collapsed .nav-group-header:hover::before {
    content: '';
    position: absolute;
    left: 65px;
    top: 50%;
    transform: translateY(-50%);
    border: 5px solid transparent;
    border-right-color: var(--text-primary);
    z-index: 1000;
    opacity: 0;
    animation: fadeInTooltip 0.3s ease forwards;
}

/* Tooltip动画 */
@keyframes fadeInTooltip {
    from {
        opacity: 0;
        transform: translateY(-50%) translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(-50%) translateX(0);
    }
}


/* 展开状态下的文本动画 */
.app-navigation:not(.collapsed) .nav-text {
    opacity: 1;
    width: auto;
    transition: all 0.3s ease 0.1s;
}

/* 导航容器的过渡动画 */
.app-navigation {
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 主内容区域的过渡动画 */
.compact-main {
    transition: grid-template-columns 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 2级导航搜索框 */
.nav-search {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.search-wrapper {
    position: relative;
    width: 100%;
}

.nav-search-input {
    width: 100% !important;
    padding: 10px 12px 10px 36px !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 20px !important;
    font-size: 13px !important;
    background: var(--bg-secondary) !important;
    transition: all 0.3s ease !important;
    box-sizing: border-box !important;
}

.nav-search-input:focus {
    outline: none !important;
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    background: var(--bg-primary) !important;
}

.search-icon {
    position: absolute;
    left: 12px;
    transform: translateY(-50%);
    color: var(--text-tertiary);
    font-size: 12px;
    pointer-events: none;
    justify-content: center;
    width: 16px;
    line-height: 1;
    display: inline-flex !important;
    align-items: center;
    height: 100%;
}

/* 折叠状态下隐藏搜索框 */
.app-navigation.collapsed .nav-search {
    display: none;
}

/* 2级导航内容区域 */
.nav-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
}

/* ==================== 直接应用区域样式 ==================== */
.direct-apps-section {
    padding: 0 0 16px 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 16px;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 20px 12px 20px;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.section-icon {
    font-size: 16px;
    color: var(--primary-color);
}

.section-text {
    flex: 1;
}

.direct-apps-list {
    display: flex;
    flex-direction: column;
    gap: 0;
    padding: 0;
}

/* 直接应用项使用与导航项相同的样式 */
.direct-app-item {
    /* 基础样式继承自 .nav-item */
    /* 调整左边距，使其与一级导航应用保持一致 */
    padding-left: 28px !important; /* 比二级导航项少一些缩进 */
}

/* 分隔线样式 */
.nav-separator {
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--border-color), transparent);
    margin: 16px 0;
}

/* 一级导航组 */
/*.nav-group {*/
/*    margin-bottom: 4px;*/
/*}*/

.nav-group-header {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    border-radius: 0;
    background: transparent;
    border: none;
    text-align: left;
}

.nav-group-header:hover {
    background: var(--bg-secondary);
}

.nav-group-header.active {
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.1), rgba(29, 78, 216, 0.1));
    border-left: 3px solid var(--primary-color);
}

.group-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
    margin-right: 12px;
    flex-shrink: 0;
}

.group-icon.medical {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.group-icon.quality {
    background: linear-gradient(135deg, #10b981, #059669);
}

.group-icon.drg {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.group-icon.report {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.group-icon.system {
    background: linear-gradient(135deg, #64748b, #475569);
}

.group-title {
    flex: 1;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.group-arrow {
    font-size: 12px;
    color: var(--text-tertiary);
    transition: transform 0.3s ease;
}

.nav-group-header.expanded .group-arrow {
    transform: rotate(180deg);
}

/* 二级导航内容 */
.nav-group-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out, opacity 0.2s ease-out;
    background: linear-gradient(135deg, var(--bg-secondary), #f8fafc);
    opacity: 0;
    border-radius: 8px;
    box-shadow: inset 2px 0 4px rgba(0, 0, 0, 0.05);
}

.nav-group-content.expanded {
    opacity: 1;
    max-height: 1000px !important;
}

/* 直接应用区域 */
.direct-apps {
    padding: 8px 0;
}

.direct-apps .nav-item {
    display: flex;
    align-items: center;
    padding: 12px 20px 12px 28px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    /*border-radius: 0 25px 25px 0;*/
    background: transparent;
    border: none;
    width: calc(100% - 30px);
    text-align: left;
    margin: 2px 0 2px 10px;
    box-sizing: border-box;
}

.direct-apps .nav-item:hover {
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.08), rgba(29, 78, 216, 0.05));
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.direct-apps .nav-item.active {
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.15), rgba(29, 78, 216, 0.08));
    color: var(--primary-color);
    transform: translateX(8px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-left: 3px solid var(--primary-color);
}

.direct-apps .nav-item i {
    width: 18px;
    text-align: center;
    font-size: 14px;
    margin-right: 12px;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

.direct-apps .nav-item:hover i {
    color: var(--primary-color);
    transform: scale(1.1);
}

.direct-apps .nav-item.active i {
    color: var(--primary-color);
    transform: scale(1.2);
}

.direct-apps .nav-item span {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-primary);
    transition: all 0.3s ease;
    line-height: 1.2;
    display: flex;
    align-items: center;
}

.direct-apps .nav-item:hover span {
    color: var(--primary-color);
    font-weight: 600;
}

.direct-apps .nav-item.active span {
    color: var(--primary-color);
    font-weight: 700;
}

/* 分隔线 */
.nav-separator {
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), var(--primary-color), rgba(59, 130, 246, 0.3), transparent);
    margin: 16px 24px;
    position: relative;
    border-radius: 1px;
}

.nav-separator::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    border-radius: 50%;
    box-shadow: 0 0 12px rgba(59, 130, 246, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.8);
}

.nav-separator::after {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.1), transparent);
    border-radius: 50%;
}

/* 子分组 */
.nav-subgroup {
    padding: 12px 0;
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    position: relative;
}

.nav-subgroup:first-of-type {
    border-top: 1px solid rgba(59, 130, 246, 0.15);
    margin-top: 8px;
}

/* 更具体的选择器，确保第一个二级导航分组有上边框 */
.nav-group-content .nav-subgroup:first-of-type {
    border-top: 1px solid rgba(59, 130, 246, 0.2) !important;
    margin-top: 8px !important;
    padding-top: 16px;
}

/* 强制样式 - 确保第一个二级导航有上边框 */
.nav-group-content > .nav-subgroup:first-of-type {
    border-top: 1px solid #e2e8f0 !important;
    margin-top: 12px !important;
    padding-top: 16px !important;
    position: relative;
}

/* 备用方案 - 通过伪元素添加边框 */
/*.nav-group-content > .nav-subgroup:first-of-type::before {*/
/*    content: '';*/
/*    position: absolute;*/
/*    top: 0;*/
/*    left: 0;*/
/*    right: 0;*/
/*    height: 2px;*/
/*    background: linear-gradient(90deg, transparent, #3b82f6, transparent);*/
/*    margin-top: -16px;*/
/*}*/

/* 如果上面都不生效，使用类名方式 */
.first-subgroup-border {
    border-top: 1px solid #e2e8f0 !important;
}

.nav-subgroup:last-child {
    border-bottom: none;
}

.subgroup-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px 10px 58px;
    font-size: 12px;
    font-weight: 700;
    color: var(--primary-color);
    text-transform: uppercase;
    letter-spacing: 1px;
    /*background: linear-gradient(90deg, rgba(59, 130, 246, 0.1), transparent);*/
    border-radius: 0 20px 20px 0;
    margin-right: 20px;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.subgroup-title::before {
    content: '\f0da';
    font-family: 'FontAwesome';
    position: absolute;
    left: 38px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.subgroup-title.expanded::before {
    transform: translateY(-50%) rotate(90deg);
    color: #1d4ed8;
}

.subgroup-title:hover {
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.15), rgba(29, 78, 216, 0.05));
    transform: translateX(3px);
}

.subgroup-title:hover::before {
    color: #1d4ed8;
    transform: translateY(-50%) scale(1.1);
}

.subgroup-title.expanded:hover::before {
    transform: translateY(-50%) rotate(90deg) scale(1.1);
}

.subgroup-text {
    flex: 1;
}

.subgroup-arrow {
    font-size: 10px;
    color: var(--primary-color);
    transition: transform 0.3s ease;
    margin-left: 8px;
}

.subgroup-title.expanded .subgroup-arrow {
    transform: rotate(180deg);
}

/* 子分组内容容器 */
.subgroup-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out, opacity 0.2s ease-out;
    opacity: 0;
}

.subgroup-content.expanded {
    opacity: 1;
    max-height: 500px;
}

/* 导航项 */
.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 20px 12px 68px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    border-radius: 0 25px 25px 0;
    background: transparent;
    border: none;
    width: calc(100% - 30px);
    text-align: left;
    margin: 2px 0 2px 10px;
    box-sizing: border-box;
}

.nav-item:hover {
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.08), rgba(29, 78, 216, 0.05));
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.nav-item.active {
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.15), rgba(29, 78, 216, 0.08));
    color: var(--primary-color);
    transform: translateX(8px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-left: 3px solid var(--primary-color);
}

.nav-item i {
    width: 18px;
    text-align: center;
    font-size: 14px;
    margin-right: 4px;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

.nav-item:hover i {
    color: var(--primary-color);
    transform: scale(1.1);
}

.nav-item.active i {
    color: var(--primary-color);
    transform: scale(1.2);
}

.nav-item span {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-primary);
    transition: all 0.3s ease;
    line-height: 1.2;
    display: flex;
    align-items: center;
}

.nav-item:hover span {
    color: var(--primary-color);
    font-weight: 600;
}

.nav-item.active span {
    color: var(--primary-color);
    font-weight: 700;
}

/* 折叠状态下的样式 */
.app-navigation.collapsed .nav-content {
    padding: 8px 0;
}

/* 重复样式已删除，使用前面的统一定义 */

.app-navigation.collapsed .group-title,
.app-navigation.collapsed .group-arrow {
    display: none;
}

/* 重复样式已删除，使用前面的 margin: 0 auto 定义 */

.app-navigation.collapsed .nav-group-content {
    display: none;
}

/* 重复样式已删除，position: relative 已在前面定义 */

.app-navigation.collapsed .nav-group-header:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 70px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--text-primary);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.app-navigation.collapsed .nav-group-header:hover::before {
    content: '';
    position: absolute;
    left: 65px;
    top: 50%;
    transform: translateY(-50%);
    border: 5px solid transparent;
    border-right-color: var(--text-primary);
    z-index: 1000;
}

/* 重复的样式已在前面定义，此处删除 */

/* 2级导航搜索框 */
.nav-search {
    padding: 8px 10px;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.search-wrapper {
    position: relative;
    width: 100%;
}

.nav-search input {
    width: 100%;
    padding: 10px 12px 10px 36px;
    border: 1px solid var(--border-color);
    border-radius: 10px;
    font-size: 13px;
    background: var(--bg-secondary);
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.nav-search input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: var(--bg-primary);
}


/* 折叠状态下隐藏搜索框 */
.app-navigation.collapsed .nav-search {
    display: none;
}

/* 2级导航内容区域 */
.nav-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
}

/* 一级导航组 */
/*.nav-group {*/
/*    margin-bottom: 4px;*/
/*}*/

.nav-group-header {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    border-radius: 0;
}

.nav-group-header:hover {
    background: var(--bg-secondary);
}

.nav-group-header.active {
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.1), rgba(29, 78, 216, 0.1));
    border-left: 3px solid var(--primary-color);
}

.group-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
    margin-right: 12px;
    flex-shrink: 0;
}

.group-icon.medical {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.group-icon.quality {
    background: linear-gradient(135deg, #10b981, #059669);
}

.group-icon.drg {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.group-icon.report {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.group-icon.system {
    background: linear-gradient(135deg, #64748b, #475569);
}

.group-title {
    flex: 1;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.group-arrow {
    font-size: 12px;
    color: var(--text-tertiary);
    transition: transform 0.3s ease;
}

.nav-group-header.expanded .group-arrow {
    transform: rotate(180deg);
}


/* 子分组 */
.nav-subgroup {
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
}

.nav-subgroup:last-child {
    border-bottom: none;
}

.subgroup-title {
    padding: 8px 20px 8px 55px;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 重复的nav-item样式已删除，使用前面定义的统一样式 */

/* 重复的折叠样式已在前面定义，此处删除 */

.app-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
    flex-shrink: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.app-icon.medical {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.app-icon.quality {
    background: linear-gradient(135deg, #10b981, #059669);
}

.app-icon.drg {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.app-icon.report {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.app-icon.system {
    background: linear-gradient(135deg, #64748b, #475569);
}

.app-content {
    flex: 1;
    min-width: 0;
}

.app-name {
    font-size: 13px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.app-desc {
    font-size: 11px;
    color: var(--text-tertiary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.app-badge {
    background: var(--primary-color);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
}

/* 加载状态 */
.app-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: var(--text-tertiary);
}

.app-loading i {
    font-size: 24px;
    margin-bottom: 12px;
    color: var(--primary-color);
}

.app-loading span {
    font-size: 13px;
}

/* 导航折叠状态 */
.app-navigation.collapsed {
    width: 60px;
    min-width: 60px;
}

.app-navigation.collapsed .nav-header h3,
.app-navigation.collapsed .nav-search,
.app-navigation.collapsed .nav-categories .nav-category span,
.app-navigation.collapsed .app-content {
    display: none;
}

.app-navigation.collapsed .nav-header {
    justify-content: center;
    padding: 16px 10px;
}

.app-navigation.collapsed .nav-category {
    justify-content: center;
    padding: 8px;
}

.app-navigation.collapsed .app-item {
    justify-content: center;
    padding: 10px;
}

.app-navigation.collapsed .app-icon {
    margin: 0;
}

/* 折叠状态下的提示 */
.app-navigation.collapsed .nav-category:hover::after,
.app-navigation.collapsed .app-item:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 70px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--text-primary);
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.app-navigation.collapsed .nav-category:hover::before,
.app-navigation.collapsed .app-item:hover::before {
    content: '';
    position: absolute;
    left: 65px;
    top: 50%;
    transform: translateY(-50%);
    border: 5px solid transparent;
    border-right-color: var(--text-primary);
    z-index: 1000;
}

/* 中间区域 */
.main-center {
    display: flex;
    flex-direction: column;
    gap: 20px;
    overflow-y: auto;
}

/* 指标卡片 */
.compact-metrics {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
}

.metric-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: 16px 18px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 14px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-height: 85px;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-color);
}

.metric-card.success::before {
    background: var(--success-color);
}

.metric-card.warning::before {
    background: var(--warning-color);
}

.metric-card.info::before {
    background: var(--info-color);
}

.metric-card:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: var(--shadow-lg);
}

.metric-icon {
    width: 44px;
    height: 44px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: white;
    flex-shrink: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.metric-card.success .metric-icon {
    background: linear-gradient(135deg, var(--success-color), #16a34a);
}

.metric-card.warning .metric-icon {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
}

.metric-card.info .metric-icon {
    background: linear-gradient(135deg, var(--info-color), #0891b2);
}

.metric-content {
    flex: 1;
}

.metric-value {
    font-size: 22px;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 2px;
    line-height: 1;
    letter-spacing: -0.3px;
}

.metric-label {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 4px;
    font-weight: 600;
    line-height: 1.1;
}

.metric-trend {
    display: flex;
    align-items: center;
    gap: 3px;
    font-size: 12px;
    font-weight: 700;
    color: var(--success-color);
    background: rgba(34, 197, 94, 0.1);
    padding: 2px 6px;
    border-radius: 10px;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

/* 快速操作 */
.quick-actions {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: 20px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.action-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 14px;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-height: 65px;
}

.action-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.08), transparent);
    transition: left 0.5s ease;
}

.action-item:hover::before {
    left: 100%;
}

.action-item:hover {
    background: var(--bg-tertiary);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-color: rgba(59, 130, 246, 0.3);
}

.action-icon {
    width: 36px;
    height: 36px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
    flex-shrink: 0;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 1;
}

.action-icon.medical {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.action-icon.quality {
    background: linear-gradient(135deg, #10b981, #059669);
}

.action-icon.drg {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.action-icon.stats {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.action-text {
    flex: 1;
    min-width: 0;
}

.action-text span {
    display: block;
    font-size: 13px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1px;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.action-text small {
    font-size: 10px;
    color: var(--text-tertiary);
    font-weight: 600;
    background: rgba(100, 116, 139, 0.1);
    padding: 1px 6px;
    border-radius: 8px;
    display: inline-block;
    line-height: 1.3;
}

/* 右侧区域 */
.main-right {
    display: flex;
    flex-direction: column;
    gap: 16px;
    overflow-y: auto;
}

/* 图表区域 */
.chart-section {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: 20px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.chart-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.chart-controls {
    display: flex;
    gap: 6px;
}

.chart-btn {
    padding: 4px 12px;
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
    font-size: 11px;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.chart-btn.active,
.chart-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.chart-content {
    height: 120px;
}

.mini-chart {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: end;
    justify-content: center;
    padding: 10px;
}

.chart-bars {
    display: flex;
    align-items: end;
    gap: 8px;
    height: 100%;
    width: 100%;
}

.bar {
    flex: 1;
    background: linear-gradient(180deg, var(--primary-color), var(--primary-dark));
    border-radius: var(--radius-sm) var(--radius-sm) 0 0;
    min-height: 10px;
    transition: all 0.3s ease;
    transform: scaleY(0);
    transform-origin: bottom;
}

.bar:hover {
    background: linear-gradient(180deg, var(--primary-dark), var(--primary-color));
}

/* 进度区域 */
.progress-section {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: 16px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.progress-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-info span:first-child {
    font-size: 13px;
    color: var(--text-secondary);
    font-weight: 500;
}

.progress-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.progress-bar {
    height: 6px;
    background: var(--bg-tertiary);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
    border-radius: 3px;
    transition: width 1s ease;
}

.progress-fill.warning {
    background: linear-gradient(90deg, var(--warning-color), #d97706);
}

/* 活动区域 */
.activity-section {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: 16px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    flex: 1;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.activity-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.live-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    color: var(--success-color);
    font-weight: 500;
}

.pulse-dot {
    width: 6px;
    height: 6px;
    background: var(--success-color);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
    }
    70% {
        transform: scale(1);
        box-shadow: 0 0 0 6px rgba(34, 197, 94, 0);
    }
    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
    }
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 10px;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
    min-height: 50px;
}

.activity-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 2px;
    height: 60%;
    background: var(--primary-color);
    border-radius: 1px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.activity-item:hover {
    background: var(--bg-tertiary);
    transform: translateX(3px);
    border-color: rgba(59, 130, 246, 0.2);
}

.activity-item:hover::before {
    opacity: 1;
}

.activity-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border: 2px solid rgba(255, 255, 255, 0.9);
}

.activity-avatar.medical {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.activity-avatar.system {
    background: linear-gradient(135deg, #64748b, #475569);
}

.activity-avatar.admin {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-text {
    font-size: 11px;
    color: var(--text-primary);
    margin-bottom: 1px;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
}

.activity-text strong {
    font-weight: 700;
    color: var(--primary-color);
}

.activity-time {
    font-size: 9px;
    color: var(--text-tertiary);
    font-weight: 600;
    background: rgba(148, 163, 184, 0.1);
    padding: 1px 4px;
    border-radius: 6px;
    display: inline-block;
    line-height: 1.2;
}

/* 数字动画效果 */
.stat-value,
.metric-value {
    position: relative;
    overflow: hidden;
}

.stat-value::after,
    /* shimmer动画效果已移除 - 性能优化，避免无限循环动画 */

    /* shimmer关键帧动画已移除 - 性能优化 */

    /* 卡片组合悬停效果 */
.compact-metrics:hover .metric-card:not(:hover) {
    opacity: 0.7;
    transform: scale(0.98);
}

.action-grid:hover .action-item:not(:hover) {
    opacity: 0.8;
    transform: scale(0.98);
}

/* 性能优化：移除gentlePulse无限循环动画 - 主要性能杀手已清理 */

.gradient-text {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 重复的hero-title样式已清理 - 未在代码中使用 */

/* 动画延迟样式已移除 - 配合gentlePulse动画清理 */

/* gentlePulse关键帧动画已移除 - 性能优化 */

/* 趋势标签动画已移除 - 性能优化，避免无限循环动画 */

/* 响应式设计 */
@media (max-width: 1400px) {
    .compact-main {
        grid-template-columns: 260px 1fr 350px;
    }
}

@media (max-width: 1200px) {
    .compact-main {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto 1fr;
    }

    .app-navigation {
        order: 1;
        max-height: 200px;
    }

    .main-right {
        order: 2;
        max-height: 300px;
    }

    .main-center {
        order: 3;
    }

    .quick-stats {
        gap: 16px;
    }

    .quick-stat-item {
        min-width: 80px;
        padding: 10px 12px;
    }

    .stat-value {
        font-size: 20px;
    }
}

@media (max-width: 768px) {
    .compact-header {
        flex-direction: column;
        gap: 16px;
        padding: 12px 16px;
    }

    .quick-stats {
        gap: 20px;
        flex-wrap: wrap;
    }

    .compact-main {
        padding: 12px;
        gap: 12px;
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
    }

    .app-navigation {
        order: 1;
        max-height: 150px;
    }

    .app-navigation .nav-categories {
        display: flex;
        overflow-x: auto;
        padding: 8px;
        gap: 8px;
    }

    .app-navigation .nav-category {
        flex-shrink: 0;
        margin-bottom: 0;
    }

    .app-navigation .nav-apps {
        max-height: 100px;
    }

    .main-right {
        order: 2;
        max-height: 250px;
    }

    .main-center {
        order: 3;
    }

    .compact-metrics {
        grid-template-columns: 1fr;
    }

    .action-grid {
        grid-template-columns: 1fr;
    }

    .data-visualization-panel {
        grid-template-columns: 1fr;
    }
}

/* ==================== 用户管理样式 ==================== */
.user-management {
    position: relative;
    margin-left: 24px;
}

.user-info {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.user-info:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.user-avatar {
    margin-right: 12px;
}

.user-avatar i {
    font-size: 32px;
    color: #fff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.user-details {
    margin-right: 12px;
    text-align: left;
}

.user-name {
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.2;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.user-role {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    line-height: 1.2;
    margin-top: 2px;
}

.user-dropdown-toggle {
    color: #fff;
    font-size: 14px;
    transition: transform 0.3s ease;
}

.user-dropdown-toggle.active {
    transform: rotate(180deg);
}

/* 用户下拉菜单 */
.user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    background: #fff;
    border-radius: var(--radius-md);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--border-color);
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.user-dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: var(--text-primary);
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 6px;
    margin: 4px 8px;
}

.dropdown-item:hover {
    background: var(--bg-secondary);
    color: var(--primary-color);
}

.dropdown-item.logout:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.dropdown-item i {
    margin-right: 12px;
    width: 16px;
    text-align: center;
    font-size: 14px;
}

.dropdown-item span {
    font-size: 14px;
    font-weight: 500;
}

.dropdown-divider {
    height: 1px;
    background: var(--border-color);
    margin: 8px 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .user-management {
        margin-left: 12px;
    }

    .user-info {
        padding: 6px 12px;
    }

    .user-details {
        display: none;
    }

    .user-dropdown-menu {
        right: -20px;
        min-width: 160px;
    }
}

/* ==================== 搜索功能样式 ==================== */
.nav-search {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
}

.search-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-wrapper.focused {
    transform: scale(1.02);
}

.search-wrapper input {
    width: 100%;
    padding: 10px 40px 10px 36px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: 14px;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
    outline: none;
}

.search-wrapper input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: #fff;
}

.search-wrapper input::placeholder {
    color: var(--text-secondary);
    font-style: italic;
}

.search-icon {
    position: absolute;
    right: 16px;
    color: var(--text-secondary);
    font-size: 16px;
    pointer-events: none;
    transition: color 0.3s ease;
}

.search-wrapper.focused .search-icon {
    color: var(--primary-color);
}

/* 搜索结果信息 */
.search-result-info {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: 8px;
    padding: 8px 12px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 12px;
    text-align: center;
    z-index: 100;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: none;
}

.search-result-info.success {
    background: rgba(34, 197, 94, 0.1);
    border-color: #22c55e;
    color: #15803d;
}

.search-result-info.no-results {
    background: rgba(239, 68, 68, 0.1);
    border-color: #ef4444;
    color: #dc2626;
}

/* 搜索高亮样式 */
.search-highlight {
    background: rgba(59, 130, 246, 0.1) !important;
    border-left: 3px solid var(--primary-color) !important;
    animation: searchPulse 0.5s ease-in-out;
}

@keyframes searchPulse {
    0% {
        transform: scale(1);
        background: rgba(59, 130, 246, 0.2);
    }
    50% {
        transform: scale(1.02);
        background: rgba(59, 130, 246, 0.15);
    }
    100% {
        transform: scale(1);
        background: rgba(59, 130, 246, 0.1);
    }
}

/* 搜索状态下的导航项样式 */
.nav-item.search-highlight:hover {
    background: rgba(59, 130, 246, 0.15) !important;
    transform: translateX(4px);
}

.direct-app-item.search-highlight:hover {
    background: rgba(59, 130, 246, 0.15) !important;
    transform: translateX(4px);
}

/* 搜索框快捷键提示 */
.search-wrapper::after {
    content: 'Ctrl+F';
    position: absolute;
    right: 45px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 10px;
    color: var(--text-tertiary);
    background: var(--bg-secondary);
    padding: 2px 6px;
    border-radius: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.search-wrapper:hover::after {
    opacity: 1;
}

.search-wrapper.focused::after {
    opacity: 0;
}

/* 响应式搜索样式 */
@media (max-width: 768px) {
    .nav-search {
        padding: 12px;
    }

    .search-wrapper input {
        padding: 8px 35px 8px 12px;
        font-size: 13px;
    }

    .search-icon {
        right: 12px;
        font-size: 14px;
    }

    .search-wrapper::after {
        display: none;
    }
}

/* ==================== 活动列表空状态样式 ==================== */
.activity-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    min-height: 120px;
}

.activity-empty-state i {
    margin-bottom: 16px;
    opacity: 0.5;
}

.activity-empty-state p {
    font-size: 14px;
    margin: 0;
    opacity: 0.7;
}

/* 活动错误状态样式 */
.activity-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    text-align: center;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: var(--radius-md);
    margin: 16px;
    color: #dc2626;
}

.activity-error i {
    font-size: 24px;
    margin-bottom: 8px;
    opacity: 0.8;
}

.activity-error p {
    font-size: 13px;
    margin: 0;
    line-height: 1.4;
}

/* ==================== DRG工具页面样式 ==================== */
.drg-tool-container {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f7fa;
    min-height: 100vh;
}

.page-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: var(--radius-lg);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 10px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.page-title i {
    margin-right: 15px;
    color: #ffd700;
}

.page-subtitle {
    font-size: 1.1rem;
    margin: 0;
    opacity: 0.9;
    font-weight: 300;
}

/* 选项卡样式 */
.tab-container {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.tab-header {
    display: flex;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.tab-item {
    flex: 1;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border-right: 1px solid #e2e8f0;
    font-weight: 500;
    color: #64748b;
}

.tab-item:last-child {
    border-right: none;
}

.tab-item:hover {
    background: #e2e8f0;
    color: var(--primary-color);
}

.tab-item.active {
    background: var(--primary-color);
    color: white;
    box-shadow: inset 0 -3px 0 #1e40af;
}

.tab-item i {
    display: block;
    font-size: 1.5rem;
    margin-bottom: 8px;
}

.tab-content {
    display: none;
    padding: 30px;
    min-height: 600px;
}

.tab-content.active {
    display: block;
}

/* 表单样式 */
.form-container {
    max-width: 1000px;
    margin: 0 auto;
}

.form-section {
    background: white;
    padding: 25px;
    margin-bottom: 25px;
    border-radius: var(--radius-lg);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-left: 4px solid var(--primary-color);
}

.form-section h3 {
    margin: 0 0 20px 0;
    color: var(--primary-color);
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.form-section h3::before {
    content: '';
    width: 4px;
    height: 20px;
    background: var(--primary-color);
    margin-right: 10px;
    border-radius: 2px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: var(--radius-md);
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* 按钮样式 */
.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e5e7eb;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
    background: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background: #4b5563;
    transform: translateY(-2px);
}

.btn-info {
    background: #0ea5e9;
    color: white;
}

.btn-info:hover {
    background: #0284c7;
    transform: translateY(-2px);
}

.btn-success {
    background: #10b981;
    color: white;
}

.btn-success:hover {
    background: #059669;
    transform: translateY(-2px);
}

/* 结果显示样式 */
.result-container {
    margin-top: 30px;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.result-container h3 {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    margin: 0;
    padding: 20px 30px;
    font-size: 1.3rem;
    font-weight: 600;
}

.result-content {
    padding: 30px;
}

.result-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.result-card {
    background: #f8fafc;
    padding: 20px;
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.result-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.result-card.full-width {
    grid-column: 1 / -1;
}

.result-card h4 {
    margin: 0 0 10px 0;
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.result-value {
    font-size: 1.2rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
    word-break: break-all;
}

/* 文本颜色类 */
.text-success {
    color: #10b981 !important;
}

.text-danger {
    color: #ef4444 !important;
}

.text-warning {
    color: #f59e0b !important;
}

.text-info {
    color: #3b82f6 !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: #6b7280 !important;
}

.text-muted {
    color: #9ca3af !important;
}

.text-purple {
    color: #8b5cf6 !important;
}

/* 警告框样式 */
.alert {
    padding: 16px 20px;
    border-radius: var(--radius-md);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    color: #dc2626;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    color: #059669;
}

.alert i {
    font-size: 1.2rem;
}

/* 文件上传样式 */
.upload-container {
    max-width: 800px;
    margin: 0 auto;
}

.upload-area {
    border: 3px dashed #d1d5db;
    border-radius: var(--radius-lg);
    padding: 60px 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafbfc;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(59, 130, 246, 0.05);
}

.upload-area i {
    font-size: 3rem;
    color: #9ca3af;
    margin-bottom: 20px;
    display: block;
}

.upload-area p {
    font-size: 1.1rem;
    color: #6b7280;
    margin: 10px 0;
}

.upload-hint {
    font-size: 0.9rem !important;
    color: #9ca3af !important;
}

.batch-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

/* 批量结果样式 */
.batch-result-container {
    margin-top: 30px;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.batch-result-content {
    padding: 30px;
}

/* 统计分析样式 */
.statistics-container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 30px;
}

.statistics-content {
    color: #6b7280;
    text-align: center;
    padding: 40px;
}

/* 帮助说明样式 */
.help-container {
    max-width: 1000px;
    margin: 0 auto;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 40px;
}

.help-content h4 {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin: 30px 0 15px 0;
    font-weight: 600;
}

.z-treerow .z-treecell {
    border: none;
}

.help-content h4:first-child {
    margin-top: 0;
}

.help-content ul {
    margin: 15px 0;
    padding-left: 25px;
}

.help-content li {
    margin: 8px 0;
    line-height: 1.6;
    color: #4b5563;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .drg-tool-container {
        padding: 10px !important;
    }

    .page-header {
        padding: 15px;
        margin-bottom: 20px;
    }

    .page-title {
        font-size: 1.8rem;
    }

    .tab-header {
        flex-direction: column;
    }

    .tab-item {
        border-right: none;
        border-bottom: 1px solid #e2e8f0;
    }

    .tab-item:last-child {
        border-bottom: none;
    }

    .tab-content {
        padding: 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .form-actions {
        flex-direction: column;
        align-items: center;
    }

    .result-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .upload-area {
        padding: 40px 20px;
    }

    .batch-actions {
        flex-direction: column;
        align-items: center;
    }
}

/* ==================== 高性能QuickStats样式 ==================== */
/* 🚀 高性能版本QuickStats - 保持原版外观，移除性能杀手 */

.hp-quick-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    padding: 0;
    margin: 0;
}

.hp-stat-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgb(41, 99, 226);
    border-radius: 12px;
    padding: 10px 13px;
    min-width: 80px;
    flex: 1;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.2s ease;
    position: relative;
    overflow: hidden;
}

.hp-stat-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

/* 🎯 可选中状态样式 */
.hp-stat-selectable {
    cursor: pointer !important;
    border: 2px solid transparent;
    /*transition: all 0.3s ease;*/
}

.hp-stat-selectable:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.25);
    border-color: rgba(255, 255, 255, 0.3);
}

/* 🎯 选中状态样式 */
.hp-stat-selected {
    border: 3px solid #fbbf24 !important;
    box-shadow: 0 0 0 2px rgba(251, 191, 36, 0.3), 0 8px 20px rgba(0, 0, 0, 0.3) !important;
    transform: translateY(-4px) scale(1.05) !important;
    background: linear-gradient(135deg, #059669, #047857) !important;
    z-index: 10;
}

.hp-stat-selected::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(251, 191, 36, 0.2), transparent) !important;
    pointer-events: none;
    z-index: 1;
}

.hp-stat-selected::after {
    content: '✓';
    position: absolute;
    top: 8px;
    right: 8px;
    background: #fbbf24;
    color: #1f2937;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    z-index: 3;
}

/* 🎯 选中状态下的文字样式 */
.hp-stat-selected .hp-stat-value {
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
    z-index: 2;
    position: relative;
}

.hp-stat-selected .hp-stat-label {
    opacity: 1;
    font-weight: 600;
    z-index: 2;
    position: relative;
}

/* 🎯 选中状态动画 */
@keyframes selectPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(251, 191, 36, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(251, 191, 36, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(251, 191, 36, 0);
    }
}

.hp-stat-selected {
    animation: selectPulse 1.5s ease-out;
}

.hp-stat-icon {
    color: white;
    margin-right: 12px;
    font-size: 18px !important;
    flex-shrink: 0;
    opacity: 0.8;
}

.hp-stat-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    text-align: center;
}

.hp-stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #FFFFFF;
    line-height: 1.2;
    margin-bottom: 4px;
}

.hp-stat-label {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    text-transform: none;
    letter-spacing: 0.3px;
}

/* 🎯 快速统计描述信息样式 - 带动态左箭头 */
.hp-stat-description {
    background: rgba(255, 255, 255, 0.15);
    padding: 6px 12px 6px 20px;
    border-radius: 12px;
    font-size: 11px;
    opacity: 0.95;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.4;
    margin-top: 4px;
    text-align: center;
    max-width: 100%;
    word-wrap: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
}

/* 🎯 动态左箭头图标 */
.hp-stat-description::before {
    content: '→';
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 15px;
    color: rgb(73 255 0);
    font-weight: 900;
    transition: all 0.3s ease;
    animation: arrowPulseLeft 2s infinite;
}

/* 🎯 左箭头脉动动画 */
@keyframes arrowPulseLeft {
    0%, 100% {
        opacity: 0.7;
        transform: translateY(-50%) translateX(0);
    }
    50% {
        opacity: 1;
        transform: translateY(-50%) translateX(2px);
    }
}

/* 🎯 描述信息悬停效果 - 显示完整内容 + 左箭头动画 */
.hp-stat-description:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    white-space: normal;
    height: auto;
    z-index: 10;
    position: relative;
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    padding-left: 28px; /* 为左侧箭头留出更多空间 */
}

/* 🎯 悬停时左箭头效果增强 */
.hp-stat-description:hover::before {
    color: rgba(255, 255, 255, 1);
    font-size: 14px;
    animation: arrowSlideLeft 0.6s ease-in-out infinite alternate;
    left: 4px;
}

/* 🎯 悬停时左箭头滑动动画 */
@keyframes arrowSlideLeft {
    0% {
        transform: translateY(-50%) translateX(0);
    }
    100% {
        transform: translateY(-50%) translateX(3px);
    }
}

/* 🎯 点击时的反馈效果 */
.hp-stat-description:active {
    transform: scale(0.98);
    background: rgba(255, 255, 255, 0.3);
}

/* 🎯 为不同类型的描述信息提供不同颜色的左箭头 */
.hp-stat-description.info-type::before {
    color: rgba(59, 130, 246, 0.8); /* 蓝色箭头 */
}

.hp-stat-description.warning-type::before {
    color: rgba(245, 158, 11, 0.8); /* 黄色箭头 */
}

.hp-stat-description.success-type::before {
    color: rgba(34, 197, 94, 0.8); /* 绿色箭头 */
}

/* 🎯 方案2: 徽章样式描述 */
.hp-stat-description.badge-style {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.8), rgba(16, 185, 129, 0.8));
    color: white;
    font-weight: 600;
    font-size: 10px;
    padding: 3px 8px;
    border-radius: 15px;
    border: none;
    box-shadow: 0 2px 6px rgba(34, 197, 94, 0.3);
}

/* 🎯 方案3: 简约线条样式 */
.hp-stat-description.minimal-style {
    background: transparent;
    border: none;
    border-top: 1px solid rgba(255, 255, 255, 0.3);
    padding: 4px 0;
    margin-top: 6px;
    font-size: 10px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
}

/* 🎯 方案4: 气泡样式 */
.hp-stat-description.bubble-style {
    background: rgba(59, 130, 246, 0.2);
    border: 1px solid rgba(59, 130, 246, 0.4);
    border-radius: 16px;
    padding: 4px 8px;
    font-size: 10px;
    color: rgba(255, 255, 255, 0.95);
    position: relative;
}

.hp-stat-description.bubble-style::before {
    content: '';
    position: absolute;
    top: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid rgba(59, 130, 246, 0.4);
}

/* 高性能QuickStats响应式设计 */
@media (max-width: 768px) {
    .hp-quick-stats {
        flex-direction: column;
        gap: 12px;
    }

    .hp-stat-item {
        min-width: auto;
        padding: 10px 14px;
    }

    .hp-stat-value {
        font-size: 20px;
    }

    .hp-stat-icon {
        font-size: 18px;
        margin-right: 10px;
    }
}

@media (max-width: 480px) {
    .hp-stat-item {
        padding: 8px 12px;
    }

    .hp-stat-value {
        font-size: 18px;
    }

    .hp-stat-label {
        font-size: 11px;
    }
}

/* Treemap 选中状态 - 强化样式 */
.highcharts-point-select,
.highcharts-treemap-series .highcharts-point-select,
.highcharts-treemap-series .highcharts-point.highcharts-point-select,
.highcharts-series .highcharts-point-select {
    stroke: #fbbf24 !important;
    stroke-width: 5 !important;
    fill: #059669 !important; /* 🎯 绿色背景 */
    filter: brightness(1.2) drop-shadow(0 0 10px rgba(251, 191, 36, 0.8)) !important;
    z-index: 999 !important;
    opacity: 1 !important;
}

.z-listbox-frozen::-webkit-scrollbar,
.z-frozen::-webkit-scrollbar,
.z-frozen-body::-webkit-scrollbar,
.z-frozen-inner::-webkit-scrollbar,
.z-frozen-right::-webkit-scrollbar {
    height: 14px !important;
    width: 14px !important;
    background-color: #f8fafc !important;
    border-radius: 7px !important;
    display: block !important;
}

.z-listbox-frozen::-webkit-scrollbar-thumb,
.z-frozen::-webkit-scrollbar-thumb,
.z-frozen-body::-webkit-scrollbar-thumb,
.z-frozen-inner::-webkit-scrollbar-thumb,
.z-frozen-right::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #94a3b8, #64748b) !important;
    border-radius: 7px !important;
    border: 2px solid #f8fafc !important;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    display: block !important;
    min-height: 20px !important;
    min-width: 20px !important;
}

