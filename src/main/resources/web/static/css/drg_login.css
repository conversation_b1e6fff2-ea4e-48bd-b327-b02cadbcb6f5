/* 病案质控DRG管理平台登录页面样式 */

/* 全局重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body, html {
    height: 100%;
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    overflow: hidden;
}

/* 登录容器 */
.drg-login-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3a8a 50%, #1e40af 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

/* 医疗背景动画 */
.medical-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.medical-shapes {
    position: relative;
    width: 100%;
    height: 100%;
}

.medical-shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 50%;
    animation: medicalFloat 8s ease-in-out infinite;
}

.shape-1 {
    width: 120px;
    height: 120px;
    top: 15%;
    left: 8%;
    animation-delay: 0s;
}

.shape-2 {
    width: 80px;
    height: 80px;
    top: 70%;
    left: 85%;
    animation-delay: 2s;
}

.shape-3 {
    width: 100px;
    height: 100px;
    top: 85%;
    left: 15%;
    animation-delay: 4s;
}

.shape-4 {
    width: 60px;
    height: 60px;
    top: 25%;
    left: 75%;
    animation-delay: 1s;
}

.shape-5 {
    width: 140px;
    height: 140px;
    top: 45%;
    left: 3%;
    animation-delay: 3s;
}

@keyframes medicalFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-30px) rotate(180deg);
        opacity: 1;
    }
}

/* 主登录区域 */
.drg-login-main {
    position: relative;
    z-index: 10;
    display: flex;
    width: 1300px;
    height: 750px;
    background: rgba(255, 255, 255, 0.98);
    border-radius: 24px;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(15px);
    overflow: hidden;
}

/* 左侧信息区域 */
.drg-login-info {
    flex: 1.2;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
    padding: 60px 50px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
}

.drg-login-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="medical-grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.08)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23medical-grid)"/></svg>');
    opacity: 0.4;
}

.drg-info-content {
    position: relative;
    z-index: 2;
    color: white;
    text-align: center;
}

.drg-logo-section {
    margin-bottom: 10px;
}

.drg-logo-icon {
    font-size: 80px;
    margin-bottom: 20px;
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.drg-platform-title {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
    line-height: 1.2;
}

.drg-platform-subtitle {
    font-size: 16px;
    opacity: 0.85;
    font-weight: 300;
    letter-spacing: 2px;
    margin-bottom: 40px;
}

.drg-features-section {
    margin-top: 30px;
}

.drg-feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
    padding: 20px 25px;
    background: rgba(255, 255, 255, 0.12);
    border-radius: 15px;
    backdrop-filter: blur(8px);
    transition: all 0.3s ease;
    text-align: left;
}

.drg-feature-item:hover {
    background: rgba(255, 255, 255, 0.18);
    transform: translateX(8px);
}

.drg-feature-icon {
    font-size: 28px;
    margin-right: 20px;
    color: rgba(255, 255, 255, 0.9);
    min-width: 40px;
}

.drg-feature-text h3 {
    font-size: 18px;
    margin-bottom: 5px;
    font-weight: 600;
}

.drg-feature-text p {
    font-size: 14px;
    opacity: 0.8;
    line-height: 1.5;
}

/* 右侧登录表单容器 */
.drg-login-form-container {
    flex: 1;
    padding: 60px 50px 40px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* 登录表单 */
.drg-login-form {
    flex: 1;
}

.drg-form-header {
    text-align: center;
    margin-bottom: 45px;
}

.drg-form-title {
    font-size: 32px;
    color: #1e40af;
    margin-bottom: 12px;
    font-weight: 700;
}

.drg-form-subtitle {
    color: #64748b;
    font-size: 15px;
    line-height: 1.5;
}

/* 表单内容 */
.drg-form-content {
    max-width: 380px;
    margin: 0 auto;
}

.drg-input-group {
    margin-bottom: 28px;
}

.drg-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.drg-input-icon {
    position: absolute;
    left: 18px;
    color: #94a3b8;
    font-size: 18px;
    z-index: 2;
}

.drg-form-input {
    width: 100% !important;
    height: 55px !important;
    padding: 0 18px 0 50px !important;
    border: 2px solid #e2e8f0 !important;
    border-radius: 12px !important;
    font-size: 15px !important;
    transition: all 0.3s ease !important;
    background: #fff !important;
    color: #334155 !important;
}

.drg-form-input:focus {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1) !important;
    outline: none !important;
}

/* 登录按钮 */
.drg-login-button {
    width: 100% !important;
    height: 55px !important;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
    border: none !important;
    border-radius: 12px !important;
    color: white !important;
    font-size: 17px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3) !important;
    margin-top: 20px !important;
}

.drg-login-button:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
}

.drg-login-button:active {
    transform: translateY(-1px) !important;
}

/* 系统信息 */
.drg-system-info {
    margin-top: 30px;
    padding-top: 25px;
    border-top: 1px solid #e2e8f0;
}

.drg-info-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    color: #64748b;
    font-size: 13px;
}

.drg-info-item i {
    margin-right: 10px;
    width: 16px;
    color: #94a3b8;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .drg-login-main {
        width: 90%;
        height: 85%;
        flex-direction: column;
    }

    .drg-login-info {
        padding: 40px 30px;
    }

    .drg-platform-title {
        font-size: 28px;
    }

    .drg-login-form-container {
        padding: 40px 30px;
    }
}

@media (max-width: 768px) {
    .drg-login-main {
        width: 95%;
        height: 90%;
        margin: 20px;
    }

    .drg-login-info {
        display: none;
    }

    .drg-form-content {
        max-width: 100%;
    }
}
