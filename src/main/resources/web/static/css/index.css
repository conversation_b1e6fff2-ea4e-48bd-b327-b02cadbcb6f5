.navbar {
    border: none !important;
    padding: 0;
    margin: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 50px;
    position: relative;
    background: linear-gradient(135deg, #bd8e7d, #00a3cf, #b3816f);
    border-radius: 8px 8px 0 0;
}

.z-navbar {
    border-top: none !important;
    border-bottom: none !important;
}

.navbar .navbar-brand {
    color: #fff;
    font-size: 20px;
    width: 330px;
    display: inline-flex;
    justify-content: center;
}

.navbar .navbar-function {
    color: #fff;
    margin-right: 10px;
    font-size: 15px;
    text-shadow: none;
    padding: 12px 10px 10px;
    display: inline-block;
    border-radius: 15px;
    transform: perspective(1px) translateZ(0);
    transition: transform 0.3s, box-shadow 0.3s;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2); /* Shadow for 3D effect */
}

.navbar-function:hover {
    transform: scale(1.05); /* Hover effect: enlarge and float */
}


.navbar-function-active {
    background: linear-gradient(30deg, #e31e1e, #4ecdc4);
    border-radius: 15px;
    color: #e7524d;
}

.pull-right {
    top: 0;
    right: 0;
}

.nav-user > div > a,
.nav-user > div .user-menu {
    background: linear-gradient(135deg, #bd8e7d, #00a3cf, #b3816f);
    border-radius: 5px;
    color: #FFF;
    display: block;
    margin-right: 35px;
    line-height: 45px;
    border-left: 1px solid #DDD;
    text-align: center;
    padding: 0 8px;
    position: relative;
}

.nav-user > div [class*="z-icon-"] {
    font-size: 16px;
    color: #ffffff;
    display: inline-block;
    width: 20px;
    text-align: center;
}

.nav-user > div:first-child > a {
    border-left: none;
    color: #ffffff !important;
}

.sidebar {
    margin-top: -1px;
    border: 1px #e5e5e5 solid;
    border-radius: 0 0 8px 8px;
    border-top: none;
    width: 200px;
    float: left;
    position: relative;
    background-color: #ffffff;
    margin-bottom: 3px;
}


.nav-list.z-navbar > ul > li {
    border-top: 1px solid #FCFCFC;
    border-bottom: 1px solid #E5E5E5;
    position: relative;
}

.nav-list.z-navbar > ul > li:first-child,
.nav-list.z-navbar > ul > li:first-child > a {
    border-top: none;
}

.nav-list.z-navbar > ul > .z-nav > ul {
    border-top: 1px solid #e5e5e5;
}


.nav-list.z-navbar > ul .z-nav-selected > .z-nav-content:after,
.nav-list.z-navbar > ul .z-navitem-selected > .z-navitem-content:after {
    content: "";
    position: absolute !important;
    right: 0;
    top: 7px;
    border: 8px solid transparent;
    border-right-color: #2b7dbc;
}

.nav-list.z-navbar > ul .z-nav-selected.z-nav-open > .z-nav-content:after {
    display: none;
}

.nav-list.z-navbar > ul .z-nav-info {
    text-shadow: none;
    box-shadow: none;
    font-size: 13px;
    padding: 1px 6px 3px 6px;
    position: absolute;
    top: 9px;
    right: 28px;
    opacity: 0.88;
    background-color: #428bca;
    color: #fff;
    text-align: center;
    vertical-align: baseline;
    line-height: 15px;
    min-width: 10px;
    height: 19px;
    white-space: nowrap;
}

.nav-list.z-navbar > ul .z-nav:hover > .z-nav-content .z-nav-info {
    opacity: 1;
}

.sidebar-collapse {
    text-align: center;
    padding: 3px 0;
    position: relative;
}

.sidebar-collapse > a [class*=z-icon] {
    display: inline-block;
    font-size: 13px;
    color: #aaa;
    border: 1px solid #bbb;
    padding: 0 5px;
    line-height: 18px;
    border-radius: 16px;
    background-color: #fff;
    position: relative;
    margin-right: 0;
}

.sidebar-collapse:before {
    content: "";
    display: inline-block;
    height: 0;
    border-top: 1px solid #e0e0e0;
    position: absolute;
    left: 15px;
    right: 15px;
    top: 13px;
}

.sidebar-min {
    width: 43px;
}

.sidebar-min .shortcuts-collapsed {
    display: block;
}

.sidebar-min .shortcuts {
    position: relative;
}

.sidebar-min .shortcuts-expanded {
    display: none;
    position: absolute;
    z-index: 20;
    top: -1px;
    left: 42px;
    width: 210px;
    padding: 0 2px 1px;
    background-color: #fff;
    box-shadow: 2px 1px 2px 0 rgba(0, 0, 0, 0.2);
    border: 1px solid #ccc;
}

.sidebar-min .shortcuts:hover .shortcuts-expanded {
    display: block;
}

.sidebar-min .nav-list.z-navbar > ul {
    width: 42px;
}

.nav-list.z-navbar > ul > .z-nav > ul {
    position: relative;
}

.nav-list.z-navbar > ul > .z-nav > ul > li,
.z-nav-popup > li {
    position: relative;
    margin: 0 !important;
}

.nav-list.z-navbar > ul > .z-nav > ul > li a, .z-nav-popup > li a {
    padding: 7px 0 9px 37px;
}

.nav-list.z-navbar > ul > .z-nav > ul > li:before,
.z-nav-popup > li:before {
    content: "";
    display: inline-block;
    position: absolute;
    width: 7px;
    left: 20px;
    top: 17px;
    border-top: 1px dotted #9dbdd6;
    z-index: 1;
}

.nav-list.z-navbar > ul > .z-nav > ul > li > a > i,
.z-nav-popup > li > a > i {
    display: none;
    width: 18px;
    height: auto;
    font-size: 13px;
    font-weight: 400;
    line-height: 13px;
    text-align: center;
    position: absolute;
    left: 10px;
    top: 11px;
    z-index: 1;
    background-color: #FFF;
}

.nav-list.z-navbar > ul > .z-nav > ul > .z-navitem-selected > a > i,
.nav-list.z-navbar > ul > .z-nav > ul > li:hover > a > i {
    display: inline-block;
}

.nav-list.z-navbar > ul > .z-nav > ul:before,
.z-nav-popup:before {
    content: "";
    display: block;
    position: absolute;
    z-index: 1;
    left: 18px;
    top: 0;
    bottom: 0;
    border: 1px dotted #9dbdd6;
}

.z-nav-text:after {
    font-family: ZK85Icons, FontAwesome, serif;
    color: #666;
    content: "\f107";
    position: absolute;
    display: inline-block;
    width: 13px;
    height: 13px;
    line-height: 13px;
    right: 11px;
    top: 11px;
    padding: 0;
}
