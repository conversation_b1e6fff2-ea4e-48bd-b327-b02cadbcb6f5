//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.zkoss.zk.ui.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.bouncycastle.util.encoders.Hex;
import org.zkoss.lang.Library;
import org.zkoss.util.SecUtil;
import org.zkoss.zk.ui.WebApp;

import java.io.File;
import java.lang.reflect.Field;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.text.SimpleDateFormat;
import java.util.*;

public class Validation extends TimerTask {
    String x;
    String y;
    boolean a;
    boolean b;
    SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");

    public Validation() {
    }

    public String aa(String key) {
        return Base64.decodeStr(key);
    }

    private String cc(String mcode) {
        String var10000 = mcode.substring(2, 4);
        return var10000 + mcode.substring(6, 9) + mcode.substring(11, 15) + mcode.substring(17, 22) + mcode.substring(24);
    }

    private String dd(String mac, String qw) {
        String var10000 = qw.substring(0, 2);
        return var10000 + mac.substring(0, 2) + qw.substring(2, 4) + mac.substring(2, 5) + qw.substring(4, 6) + mac.substring(5, 9) + qw.substring(6, 8) + mac.substring(9, 14) + qw.substring(8, 10) + mac.substring(14);
    }

    private String ee(String s) {
        String var = "0";
        String output = "";
        String tempStr = s.replaceAll("[-:]", "");

        for(int i = 0; i < tempStr.length(); ++i) {
            if (tempStr.charAt(i) >= '0' && tempStr.charAt(i) <= '9') {
                output = output.concat(var.concat(String.valueOf(tempStr.charAt(i))));
            } else if (tempStr.charAt(i) >= 'A' && tempStr.charAt(i) <= 'F') {
                output = output.concat(String.valueOf(tempStr.charAt(i) - 55));
            } else if (tempStr.charAt(i) >= 'a' && tempStr.charAt(i) <= 'f') {
                output = output.concat(String.valueOf(tempStr.charAt(i) - 87));
            }
        }

        return output;
    }

    private String ff(String mcode) {
        String cd = this.cc(mcode);
        StringBuilder sn = new StringBuilder();

        for(int i = 0; i < cd.length(); ++i) {
            int tmp = Integer.parseInt(String.valueOf(cd.charAt(i)));
            sn.append(((tmp + i + 876) * 83 + 76) * 431 % 10);
        }

        return sn.toString();
    }

    private String gg(String hostName) {
        StringBuilder ret = new StringBuilder();

        int loopTimes;
        for(loopTimes = 0; loopTimes < hostName.length(); ++loopTimes) {
            ret.append((hostName.charAt(loopTimes) + loopTimes) % 10);
        }

        if (ret.length() > 10) {
            ret = new StringBuilder(ret.substring(0, 10));
        } else if (ret.length() < 10) {
            loopTimes = 10 - ret.length();

            for(int i = 0; i < loopTimes; ++i) {
                String var10002 = ret.toString();
                ret = new StringBuilder(var10002 + ((i + 344) * 9 + 19) % 10);
            }
        }

        return ret.toString();
    }

    private List<String> xx() {
        List<String> er = new ArrayList();

        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();

            while(true) {
                byte[] mac;
                do {
                    NetworkInterface networkInterface;
                    do {
                        do {
                            if (!interfaces.hasMoreElements()) {
                                return er.stream().distinct().toList();
                            }

                            networkInterface = (NetworkInterface)interfaces.nextElement();
                        } while(networkInterface.isVirtual());
                    } while(networkInterface.isLoopback());

                    mac = networkInterface.getHardwareAddress();
                } while(mac == null);

                StringBuilder stringBuilder = new StringBuilder();

                for(int i = 0; i < mac.length; ++i) {
                    stringBuilder.append(String.format("%02X%s", mac[i], i < mac.length - 1 ? ":" : ""));
                }

                String macAddress = stringBuilder.toString();
                er.add(macAddress);
            }
        } catch (SocketException var7) {
            return er.stream().distinct().toList();
        }
    }

    String hexByte(byte b) {
        String s = "000000" + Integer.toHexString(b);
        return s.substring(s.length() - 2);
    }

    public void start(WebApp webapp) {
        String trial = "";
        Optional.ofNullable(Library.getProperty(this.aa("b3JnLnprb3NzLnprLnVpLldlYkFwcC5pbmZv"))).ifPresent((info) -> {
            this.xx().forEach((e) -> {
                try {
                    if (!this.a) {
                        String sn = this.ff(this.dd(this.ee(e), this.gg(this.yy())));
                        String key = Base64.encode(sn).substring(0, 8);
                        JSONObject json = JSONUtil.parseObj(Base64.decodeStr(SecUtil.d16(info, Hex.toHexString(sn.getBytes()).substring(0, 16))));
                        Field field;
                        if (this.a = Boolean.valueOf(SecUtil.d8(json.getStr(this.aa("YXBwbHk=")), key))) {
                            if (this.b = Boolean.valueOf(SecUtil.d8(json.getStr(this.aa("YnVpbGQ=")), key))) {
                                this.y = SecUtil.d8(json.getStr(this.aa("ZWQ=")), key);
                                Calendar dar = Calendar.getInstance();
                                if (dar.getTime().before(this.fmt.parse(this.y))) {
                                    field = ReflectUtil.getField(ClassUtil.loadClass(this.aa("Y29tLnN5cy5jb21tb24uQ2FjaGVVdGls")), this.aa("YXV0aEFwcA=="));
                                    field.setAccessible(false);
                                    List authApp = (List)field.get((Object)null);
                                    Optional.ofNullable(webapp.getResourceAsStream(this.aa("fi4vemtleC9hcHAuc2Vy"))).map((stream) -> {
                                        return (List)IoUtil.readObj(stream);
                                    }).ifPresent((apps) -> {
                                        authApp.addAll(apps);
                                    });
                                    Optional.ofNullable(webapp.getResourceAsStream(this.aa("fi4vcGFnZS9zeXMvYWRkLnNlcg=="))).map((stream) -> {
                                        return (List)IoUtil.readObj(stream);
                                    }).ifPresent((apps) -> {
                                        authApp.addAll(apps);
                                    });
                                }
                            }
                        } else {
                            this.x = SecUtil.d8(json.getStr(this.aa("dHJpYWw=")), key);
                            String trialNum = SecUtil.d8(json.getStr(this.aa("dHJpYWxOdW0=")), key);
                            field = ReflectUtil.getField(ClassUtil.loadClass(this.aa("Y29tLnN5cy5jb21tb24uQ2FjaGVVdGls")), this.aa("dHJpYWxOdW0="));
                            field.setAccessible(false);
                            field.set((Object)null, Integer.parseInt(trialNum));
                        }
                    }
                } catch (Exception var10) {
                }

            });
        });
        this.si(webapp);
    }

    private void si(WebApp webapp) {
        try {
            if (!this.a) {
                (new Timer()).schedule(this, 1000L, 1800000L);
                SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
                Calendar dar = Calendar.getInstance();
                if (StrUtil.isBlank(this.x)) {
                    if (dar.getTime().before(fmt.parse(this.aa("MjAyNS0wMi0wMSAxNjoxMQ==")))) {
                        dar.add(5, 10);
                        this.x = fmt.format(dar.getTime());
                    } else {
                        dar.add(10, 2);
                        this.x = fmt.format(dar.getTime());
                    }
                }

                String info = webapp.getRealPath(this.aa("L1dFQi1JTkYvaW5mbw=="));
                String hname = this.yy();
                String mc = this.xx().toString();
                String value = hname + "\r\n" + mc;
                File file = new File(info);
                FileUtil.writeString(value, file, "utf-8");
            }
        } catch (Exception var9) {
        }

    }

    private String yy() {
        String hostName = "cw";

        try {
            InetAddress addr = InetAddress.getLocalHost();
            hostName = addr.getHostName();
        } catch (Exception var3) {
        }

        return hostName;
    }

    public void run() {
        Calendar calender = Calendar.getInstance();

        try {
            if (!this.fmt.parse(this.x).after(calender.getTime())) {
//                System.exit(0);
            }

            if (this.b && !this.fmt.parse(this.x).after(calender.getTime())) {
                Field field = ReflectUtil.getField(ClassUtil.loadClass(this.aa("Y29tLnN5cy5jb21tb24uQ2FjaGVVdGls")), this.aa("YXV0aEFwcA=="));
                field.setAccessible(false);
                ((List)field.get((Object)null)).clear();
                ReflectUtil.getMethodByName(ClassUtil.loadClass(this.aa("Y29tLnN5cy5jb21tb24uQ2FjaGVVdGls")), this.aa("aW5pdENhY2hl")).invoke((Object)null);
            }
        } catch (Exception var3) {
//            System.exit(0);
        }

    }
}
