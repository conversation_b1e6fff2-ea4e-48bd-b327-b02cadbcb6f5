
package org.zkoss.zkex.zul;

import cn.hutool.core.bean.BeanUtil;
import org.zkoss.lang.Objects;
import org.zkoss.zk.au.AuRequest;
import org.zkoss.zk.ui.UiException;
import org.zkoss.zk.ui.event.Events;
import org.zkoss.zk.ui.event.InputEvent;
import org.zkoss.zk.ui.sys.ContentRenderer;
import org.zkoss.zkex.rt.Runtime;
import org.zkoss.zul.impl.XulElement;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class Colorbox extends XulElement {
    private String _color = "#000000";
    private int _rgb = 0;
    private boolean _disabled;

    public Colorbox() {
    }


    public void setColor(String color) {
        if (!Objects.equals(color, this._color)) {
            this._color = color;
            this._rgb = this._color == null ? 0 : decode(this._color);
            this.smartUpdate("color", this._color);
        }
    }

    public String getColor() {
        return this._color;
    }

    public void setValue(String value) {
        this.setColor(value);
    }

    public String getValue() {
        return this.getColor();
    }

    public int getRGB() {
        return this._rgb;
    }

    public boolean isDisabled() {
        return this._disabled;
    }

    public void setDisabled(boolean disabled) {
        if (this._disabled != disabled) {
            this._disabled = disabled;
            this.smartUpdate("disabled", this._disabled);
        }

    }

    public void service(AuRequest request, boolean everError) {
        String cmd = request.getCommand();
        if ("onChange".equals(cmd)) {
            Map<String, Object> data = request.getData();
            InputEvent inputEvent = InputEvent.getInputEvent(request, this._color);
            BeanUtil.setFieldValue(inputEvent, "_val", data.get("color"));
            this.disableClientUpdate(true);
            try {
                this.setColor(inputEvent.getValue());
            } finally {
                this.disableClientUpdate(false);
            }

            Events.postEvent(inputEvent);
        } else {
            super.service(request, everError);
        }

    }

    public String getZclass() {
        return this._zclass == null ? "z-colorbox" : this._zclass;
    }

    protected boolean isChildable() {
        return false;
    }

    protected void renderProperties(ContentRenderer renderer) throws IOException {
        super.renderProperties(renderer);
        Runtime.init(this);
        renderer.render("color", this.getColor());
        this.render(renderer, "disabled", this._disabled);
    }

    public void setBind(String bind) {
        HashMap map = new HashMap();
        map.put("value", bind);
        addAnnotation(null, "bind", map);
    }

    private static int decode(String color) {
        if (color == null) {
            return 0;
        } else if (color.length() == 7 && color.charAt(0) == '#') {
            return Integer.parseInt(color.substring(1), 16);
        } else {
            throw new UiException("Incorrect color format (#RRGGBB) : " + color);
        }
    }

    static {
        addClientEvent(Colorbox.class, "onChange", 1);
    }
}
