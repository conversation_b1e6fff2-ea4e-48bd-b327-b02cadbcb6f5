package com.sys.core.server;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.sys.common.UserInfo;
import com.sys.core.api.BaseDao;
import com.sys.core.cache.CacheServer;
import com.sys.entity.SiteGroup;
import com.sys.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class UserServer {
    @Autowired
    public BaseDao baseDao;
    @Autowired
    CacheServer cacheServer;

    public UserInfo getUserInfo(String userid, String password) {
        UserInfo userInfo = null;
        User user = getUser(userid, password);
        if (null != user) {
            userInfo = new UserInfo(user);
            userInfo.setGrpList(getAppGroup(user.getUserId()));
            userInfo.setSiteAuth(getUserSite(user.getUserId()));
            userInfo.setAuthAll(authAll(user.getUserId()));
        }
        return userInfo;
    }

    public List<String> getEntrust(String userid) {
        JSONObject param = new JSONObject();
        param.put("userid", userid);
        String hql = "from User where entrust=:userid  and effective=true";
        List<User> list = baseDao.findHql(hql, param);
        return list.stream().map(User::getUserId).collect(Collectors.toList());
    }


    /**
     * 获取用户授权的站点组
     *
     * @param userid 用户ID
     * @return 站点组列表
     */
    public List<SiteGroup> getAuthSite(String userid) {
        JSONObject param = new JSONObject();
        param.put("userid", userid);
        String hql = " from SiteGroup t where exists(select 1 from SiteUser t1 where t1.userId=:userid and t1.grpNum=t.grpNum) ";

        // 使用 Convert.toList 确保返回的是 Sitegroup 对象而不是 LinkedHashMap
        List<?> result = baseDao.findHql(hql, param);
        return Convert.toList(SiteGroup.class, result);
    }

    private List<String> getAppGroup(String userid) {
        JSONObject param = new JSONObject();
        param.put("userid", userid);
        String hql = "select t.grpNum from AppGroup t where exists(select 1 from GroupUser t1 where t1.userId=:userid and t1.grpNum=t.grpNum) ";
        return baseDao.findHql(hql, param);
    }

    public boolean authAll(String userid) {
        List<SiteGroup> sites = getAuthSite(userid);
        return sites.stream().anyMatch(SiteGroup::getAuthAll);
    }

    /**
     * 获取用户的授权站点
     *
     * @param userid 用户ID
     * @return 授权站点ID列表
     */
    public List<Long> getUserSite(String userid) {
        // 获取用户的站点组
        List<SiteGroup> sites = getAuthSite(userid);

        // 查询用户信息
        JSONObject param = new JSONObject();
        param.put("userid", userid);
        String hql = "from User t where t.staffUsername=:userid";
        User user = baseDao.findOne(hql, param);

        // 初始化授权站点列表
        List<Long> auths = new ArrayList<>();

        if (sites.isEmpty()) {
            // 如果没有站点组，只添加用户自己的部门ID
            auths.add(user.getDeptId());
        } else {
            // 提取站点组编号
            List<String> grpnums = new ArrayList<>();
            for (SiteGroup site : sites) {
                if (site != null && site.getGrpNum() != null) {
                    grpnums.add(site.getGrpNum());
                }
            }

            // 获取授权站点
            auths = cacheServer.getAuthSite(grpnums);

            // 确保用户自己的部门ID在列表中
            if (!auths.contains(user.getDeptId())) {
                auths.add(user.getDeptId());
            }
        }

        return auths;
    }


    private User getUser(String userid, String password) {
        JSONObject param = new JSONObject();
        param.put("userid", userid);
        param.put("password", password);
        String hql = "from User t where t.staffUsername=:userid and t.password=:password";
        return baseDao.findOne(hql, param);
    }
}
