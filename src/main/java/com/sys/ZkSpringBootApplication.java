package com.sys;


import com.sys.core.api.BaseDao;
import com.sys.core.server.LogServer;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@SpringBootApplication
@Controller
@RequiredArgsConstructor
@ComponentScan("com")
@EnableScheduling
@Slf4j
@EntityScan(basePackages = "com")
@RequestMapping("${base.path}")
public class ZkSpringBootApplication {
    @Autowired
    LogServer logServer;
    @Autowired
    BaseDao baseDao;

    public static void main(String[] args) {
        SpringApplication.run(ZkSpringBootApplication.class, args);
    }


    @GetMapping("/problem")
    public Object login() {
        return "sub_problem";
    }

    @GetMapping("/index")
    public Object index() {
        return "index";
    }

    @GetMapping("/icon")
    public Object icon(HttpServletRequest request) {
        return "sys/icon";
    }

}
