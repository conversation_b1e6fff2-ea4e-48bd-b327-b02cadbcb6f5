package com.sys;


import com.sys.core.api.BaseDao;
import com.sys.core.server.LogServer;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@SpringBootApplication
@Controller
@RequiredArgsConstructor
@ComponentScan("com")
@EnableScheduling
@Slf4j
@EntityScan(basePackages = "com")
@RequestMapping("${base.path}")
public class ZkSpringBootApplication {
    @Autowired
    LogServer logServer;
    @Autowired
    BaseDao baseDao;

    public static void main(String[] args) {
        SpringApplication.run(ZkSpringBootApplication.class, args);
    }


    @GetMapping("/problem")
    public Object problem() {
        return "sub_problem";
    }

    @GetMapping("/input")
    public Object index() {
        return "index";
    }

    @GetMapping("/icon")
    public Object icon(HttpServletRequest request) {
        return "sys/icon";
    }
    @GetMapping("/app")
    public Object app() {
        return "app_sub";
    }

    @GetMapping("/feedback")
    public Object feedback() {
        return "feedback_page";
    }


    @GetMapping("/login")
    public Object login() {
        return "drg_login";
    }

    @GetMapping("/index")
    public Object drgIndex() {
        return "sys/drg_index";
    }

}
