package com.cisdi.entity;

import com.sys.core.config.SnowflakeId;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.Comment;

@Entity
@Data
@Table(name = "CISDI_PROJECT")
public class CisdiProject implements java.io.Serializable {
    @Id
    @SnowflakeId
    private Long id;
    @Comment("项目编码")
    private String proCode;
    @Comment("项目名称")
    private String proName;
    @Comment("套图编码")
    private String plotNum;
    @Comment("操作箱套图编码")
    private String opxPlotNum;
    @Comment("套图名称")
    private String plotName;
    @Comment("操作箱套图名称")
    private String opxPlotName;
    @Comment("PBS代码")
    private String pbsCode;
    @Comment("PBS名称")
    private String pbsName;
    @Comment("设计专业代码")
    private String designSpec;
    @Comment("文档类型")
    private String codeType;
    @Comment("专业类型")
    private String specType;
    @Comment("设计人工号")
    private String charge;
    @Comment("设计人部门")
    private String department;
    @Comment("项目类别")
    private String proType;
    @Comment("设计人名称")
    private String chargeName;
    @Comment("版次")
    private String proVersion;

    public boolean equals(Object obj) {
        return obj instanceof CisdiProject && id != null ? id.equals(((CisdiProject) obj).id) : super.equals(obj);
    }

    public int hashCode() {
        return id != null ? id.hashCode() : super.hashCode();
    }
}
