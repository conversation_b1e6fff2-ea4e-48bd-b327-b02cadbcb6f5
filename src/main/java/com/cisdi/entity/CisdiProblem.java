package com.cisdi.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Comment;
@Entity
@Data
@Table(name = "CISDI_PROBLEM")
public class CisdiProblem implements java.io.Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Comment("客户单位名称")
    private String customerName;
    @Comment("项目名称")
    private String projectName;
    @Comment("反馈内容")
    private String problemDesc;
    @Comment("上传文件")
    private String uploadFile;
    @Comment("联系人")
    private String contactName;
    @Comment("联系电话")
    private String contactPhone;
    @Comment("联系邮箱")
    private String contactEmail;
    @Comment("工厂")
    private String factory;
    @Comment("创建时间")
    private java.util.Date createTime;
    @Comment("状态")
    private String status="待指派";
    @Comment("指派人")
    private String assignee;
    @Comment("指派人工号")
    private String assigneeJobNum;
    @Comment("责任人")
    private String responsiblePerson;
    @Comment("责任人工号")
    private String responsiblePersonJobNum;
    @Comment("抄送人")
    private String ccUser;
    @Comment("抄送人工号")
    private String ccUserJobNum;
    @Comment("关联项目")
    private String relaProName;
    @Comment("关联项目编号")
    private Integer relaProNo;
    @Comment("首次处理时间")
    private java.util.Date firstHandleTime;
    @Comment("是否预警")
    private Boolean isWarning = false;
    private String processResult;
    private String processFiles;

    public boolean equals(Object obj) {
        return obj instanceof CisdiProblem && id != null ? id.equals(((CisdiProblem) obj).id) : super.equals(obj);
    }

    public int hashCode() {
        return id != null ? id.hashCode() : super.hashCode();
    }
}
