package com.cisdi.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Comment;
@Entity
@Data
@Table(name = "CISDI_PROCESS_RECORD")
public class CisdiProcessRecord implements java.io.Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)//指派
    private Long id;
    @Comment("问题ID")
    private Integer problemId;
    @Comment("操作人姓名")
    private String opterName;
    @Comment("操作人工号")
    private String opterNum;
    @Comment("意见")
    private String opinion;
    @Comment("提交时间")
    private java.util.Date submitDate;
    
public boolean equals(Object obj) {
        return obj instanceof CisdiProcessRecord && id != null ? id.equals(((CisdiProcessRecord) obj).id) : super.equals(obj);
    }
    
public int hashCode() {
        return id != null ? id.hashCode() : super.hashCode();
    }
}
