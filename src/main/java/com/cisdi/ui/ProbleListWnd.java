package com.cisdi.ui;

import cn.hutool.core.util.StrUtil;
import com.cisdi.entity.CisdiProblem;
import com.sys.core.zul.ListWnd;
import com.sys.entity.AppConfig;
import com.sys.entity.ButtonCfg;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zk.ui.event.Events;

public class ProbleListWnd extends ListWnd {
    public void detail(Event event) {
        Events.postEvent("onDetail", getParentWnd(), event.getData());
    }

    public void onSelectItem(Event event) {
        super.onSelectItem(event);
        MainDiv mainDiv = (MainDiv) getParentWnd();
        if (mainDiv.isDetailOpen()) {
            Events.postEvent("onDetail", getParentWnd(), event.getData());
        }
    }

    @Override
    public boolean checkBtncfg(Object data, ButtonCfg cfg) {
        return super.checkBtncfg(data, cfg);
    }

    @Override
    public boolean getColEdit(Object data, AppConfig cfg) {
        CisdiProblem problem = (CisdiProblem) data;
        if (StrUtil.equals(cfg.getDispName(), "relaProName")) {
            return StrUtil.isBlank(problem.getRelaProName());
        }
        if (StrUtil.equals(cfg.getDispName(), "responsiblePerson")) {
            return StrUtil.isBlank(problem.getResponsiblePerson());
        }
        if (StrUtil.equals(cfg.getDispName(), "ccUser")) {
            return StrUtil.isBlank(problem.getCcUser());
        }
        return super.getColEdit(data, cfg);
    }

    @Override
    public void change(Object data, String field, Object orgVal, Object value) {
        if (StrUtil.equals(field, "relaProName")) {
            CisdiProblem problem = (CisdiProblem) data;
            problem.setAssignee(getUser().getUserId());
        }
        super.change(data, field, orgVal, value);
    }
}
