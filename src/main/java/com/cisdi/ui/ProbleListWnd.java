package com.cisdi.ui;

import cn.hutool.core.util.StrUtil;
import com.cisdi.entity.CisdiProblem;
import com.sys.common.Message;
import com.sys.common.UserInfo;
import com.sys.common.Utils;
import com.sys.core.zul.Header;
import com.sys.core.zul.ListWnd;
import com.sys.core.zul.api.HeaderRenderer;
import com.sys.entity.AppConfig;
import com.sys.entity.ButtonCfg;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zk.ui.event.Events;
import org.zkoss.zul.Label;
import org.zkoss.zul.Listcell;
import org.zkoss.zul.Listitem;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ProbleListWnd extends ListWnd {
    public void detail(Event event) {
        Events.postEvent("onDetail", getParentWnd(), event.getData());
    }

    public void onSelectItem(Event event) {
        super.onSelectItem(event);
        MainDiv mainDiv = (MainDiv) getParentWnd();
        if (mainDiv.isDetailOpen()) {
            Events.postEvent("onDetail", getParentWnd(), event.getData());
        }
    }

    @Override
    public void onCreate() {
        List<CisdiProblem> allTableData = getAllTableData();
        Date date = new Date();

        //处理预警
        List<CisdiProblem> problemList = allTableData.stream().filter(x -> x.getFirstHandleTime() != null && isOver10Days(x.getFirstHandleTime(), date)).collect(Collectors.toList());
        problemList.forEach(x -> x.setIsWarning(true));
        baseDao.update(allTableData);
        super.onCreate();
    }

    @Override
    public boolean checkBtncfg(Object data, ButtonCfg cfg) {
        UserInfo userInfo = Utils.getUserInfo();
        CisdiProblem problem = (CisdiProblem) data;
//        if (StrUtil.equals(cfg.getMethodDesc(),"删除")){
//            return userInfo.getGrpList().contains("1001")
//                    && (StrUtil.equals(problem.getAssigneeJobNum(),userInfo.getUser().getItemNo()) || problem.getAssignee() == null);
//        }
//        if (StrUtil.equals(cfg.getMethodDesc(),"指派")){
//            return userInfo.getGrpList().contains("1001") && problem.getAssignee() == null && StrUtil.equals(problem.getStatus(),"待指派");
//        }
//        if (StrUtil.equals(cfg.getMethodDesc(),"处理")){
//            return StrUtil.equals(problem.getResponsiblePersonJobNum(),userInfo.getUser().getItemNo()) && StrUtil.equals(problem.getStatus(),"待处理");
//        }
        return super.checkBtncfg(data, cfg);
    }

    @Override
    public boolean getColEdit(Object data, AppConfig cfg) {
        CisdiProblem problem = (CisdiProblem) data;
        if (StrUtil.equals(cfg.getDispName(), "relaProName")) {
            return StrUtil.isBlank(problem.getRelaProName());
        }
        if (StrUtil.equals(cfg.getDispName(), "responsiblePerson")) {
            return StrUtil.isBlank(problem.getResponsiblePerson());
        }
        if (StrUtil.equals(cfg.getDispName(), "ccUser")) {
            return StrUtil.isBlank(problem.getCcUser());
        }
        return super.getColEdit(data, cfg);
    }

    @Override
    public void change(Object data, String field, Object orgVal, Object value) {
        if (StrUtil.equals(field, "relaProName")) {
            CisdiProblem problem = (CisdiProblem) data;
            problem.setAssignee(getUser().getUserId());
        }
        super.change(data, field, orgVal, value);
    }


    @Override
    public Map<String, Object> addCondition(Map<String, Object> cond) {

       /* if (!Utils.getUserInfo().getGrpList().contains("1001")){//非管理员
            String itemNo = Utils.getCurUser().getItemNo();
            String sql = "responsible_person_job_num ='"+itemNo+"' or cc_user_job_num='"+itemNo+"'";
            Utils.addCondition(cond, "sql", null, sql);
        }*/
        return super.addCondition(cond);
    }

    public static boolean isOver10Days(Date startDate, Date endDate) {
        LocalDate startLocalDate = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endLocalDate = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        long diffInDays = java.time.temporal.ChronoUnit.DAYS.between(startLocalDate, endLocalDate);
        return diffInDays > 10;
    }

    @Override
    public void initHeader(Event event) {
        super.initHeader(event);
        Header header = getTableHeader("status");
        if (header != null) {
            header.setHeaderRenderer(createStatus("status"));
        }
        header = getTableHeader("problemDesc");
        if (null != header) {
            header.setHeaderRenderer(createProblemDesc("problemDesc"));
        }
    }

    public HeaderRenderer createProblemDesc(String type) {
        return new HeaderRenderer() {
            public Component render(Component tr, Object value, Object rowData, int rowIndex) {
                Listcell listcell = new Listcell();
                Label label = new Label(String.valueOf(value));
                listcell.appendChild(label);
                label.addEventListener("onClick", event -> {
                    Events.postEvent("onDetail", getParentWnd(), tr);
                });
                label.setStyle("color: #0000EE;text-decoration: underline;");
                return listcell;
            }
        };
    }

    public HeaderRenderer createStatus(String type) {
        return new HeaderRenderer() {
            public Component render(Component tr, Object value, Object rowData, int rowIndex) {
                Listcell listcell = new Listcell();
                Label label = new Label(String.valueOf(value));
                label.setStyle("background: aqua;padding: 4px;border-radius: 10px;");
                listcell.appendChild(label);
                label = new Label("超时预警");
                label.setStyle("margin-left:5px;background: red;padding: 4px;border-radius: 10px;");
                listcell.appendChild(label);
                return listcell;
            }
        };
    }

    public void urge(Event event) {
        Message.showQuestion("确定发送催促消息给责任人？", data -> {
            if (data) {

            }
        });
    }

    public void processList(Event event) {
        Map data = (Map) event.getData();
        Listitem listitem = (Listitem) data.get("target");
        Utils.openData("~./page/cisdi/process_record.zul", listitem.getValue(), this);
    }

}
