package com.cisdi.ui;

import cn.hutool.json.JSONUtil;
import com.sys.common.Utils;
import com.sys.core.api.BaseDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zk.ui.util.Clients;
import org.zkoss.zul.Div;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Test1Div extends Div {

    @Autowired
    BaseDao baseDao;

    public Test1Div() {
        Utils.autowireServer(this);
    }

    /**
     * 获取项目列表数据
     */
    public void onGetProjectList(Event event) {
        List<Map<String, Object>> projectList = new ArrayList<>();

        // 模拟从数据库获取项目数据
        // 实际项目中应该从数据库查询
        Map<String, Object> project1 = new HashMap<>();
        project1.put("id", 1);
        project1.put("name", "智慧城市建设项目");
        project1.put("code", "ZHCS2024001");
        projectList.add(project1);

        Map<String, Object> project2 = new HashMap<>();
        project2.put("id", 2);
        project2.put("name", "数字化转型项目");
        project2.put("code", "SZHX2024002");
        projectList.add(project2);

        Map<String, Object> project3 = new HashMap<>();
        project3.put("id", 3);
        project3.put("name", "云平台建设项目");
        project3.put("code", "YPTJ2024003");
        projectList.add(project3);

        Map<String, Object> project4 = new HashMap<>();
        project4.put("id", 4);
        project4.put("name", "大数据分析项目");
        project4.put("code", "DSJFX2024004");
        projectList.add(project4);

        // 如果有搜索关键词，进行过滤
        String keyword = (String) event.getData();
        if (keyword != null && !keyword.trim().isEmpty()) {
            projectList = projectList.stream()
                .filter(p -> p.get("name").toString().toLowerCase().contains(keyword.toLowerCase()) ||
                           p.get("code").toString().toLowerCase().contains(keyword.toLowerCase()))
                .collect(java.util.stream.Collectors.toList());
        }

        // 返回JSON数据给前端
        Clients.evalJavaScript("renderProjectList(" + JSONUtil.toJsonStr(projectList) + ")");
    }

    /**
     * 获取责任人列表数据
     */
    public void onGetResponsibleList(Event event) {
        List<Map<String, Object>> responsibleList = new ArrayList<>();

        // 模拟从数据库获取责任人数据
        Map<String, Object> person1 = new HashMap<>();
        person1.put("id", 1);
        person1.put("name", "张三");
        person1.put("department", "技术部");
        responsibleList.add(person1);

        Map<String, Object> person2 = new HashMap<>();
        person2.put("id", 2);
        person2.put("name", "李四");
        person2.put("department", "产品部");
        responsibleList.add(person2);

        Map<String, Object> person3 = new HashMap<>();
        person3.put("id", 3);
        person3.put("name", "王五");
        person3.put("department", "运营部");
        responsibleList.add(person3);

        Map<String, Object> person4 = new HashMap<>();
        person4.put("id", 4);
        person4.put("name", "赵六");
        person4.put("department", "市场部");
        responsibleList.add(person4);

        // 如果有搜索关键词，进行过滤
        String keyword = (String) event.getData();
        if (keyword != null && !keyword.trim().isEmpty()) {
            responsibleList = responsibleList.stream()
                .filter(p -> p.get("name").toString().toLowerCase().contains(keyword.toLowerCase()) ||
                           p.get("department").toString().toLowerCase().contains(keyword.toLowerCase()))
                .collect(java.util.stream.Collectors.toList());
        }

        // 返回JSON数据给前端
        Clients.evalJavaScript("renderResponsibleList(" + JSONUtil.toJsonStr(responsibleList) + ")");
    }

    /**
     * 获取抄送人列表数据
     */
    public void onGetCcList(Event event) {
        List<Map<String, Object>> ccList = new ArrayList<>();

        // 模拟从数据库获取抄送人数据
        Map<String, Object> person1 = new HashMap<>();
        person1.put("id", 1);
        person1.put("name", "陈七");
        person1.put("department", "技术部");
        ccList.add(person1);

        Map<String, Object> person2 = new HashMap<>();
        person2.put("id", 2);
        person2.put("name", "周八");
        person2.put("department", "产品部");
        ccList.add(person2);

        Map<String, Object> person3 = new HashMap<>();
        person3.put("id", 3);
        person3.put("name", "吴九");
        person3.put("department", "运营部");
        ccList.add(person3);

        Map<String, Object> person4 = new HashMap<>();
        person4.put("id", 4);
        person4.put("name", "郑十");
        person4.put("department", "市场部");
        ccList.add(person4);

        Map<String, Object> person5 = new HashMap<>();
        person5.put("id", 5);
        person5.put("name", "孙十一");
        person5.put("department", "财务部");
        ccList.add(person5);

        // 如果有搜索关键词，进行过滤
        String keyword = (String) event.getData();
        if (keyword != null && !keyword.trim().isEmpty()) {
            ccList = ccList.stream()
                .filter(p -> p.get("name").toString().toLowerCase().contains(keyword.toLowerCase()) ||
                           p.get("department").toString().toLowerCase().contains(keyword.toLowerCase()))
                .collect(java.util.stream.Collectors.toList());
        }

        // 返回JSON数据给前端
        Clients.evalJavaScript("renderCcList(" + JSONUtil.toJsonStr(ccList) + ")");
    }

    /**
     * 提交表单数据
     */
    public void submitForm(Event event) {
        Map<String, Object> formData = (Map<String, Object>) event.getData();

        // 获取表单数据
        String projectId = (String) formData.get("projectId");
        String projectCode = (String) formData.get("projectCode");
        String responsibleId = (String) formData.get("responsibleId");
        String ccIds = (String) formData.get("ccIds");

        // 验证必填项
        if (projectId == null || projectId.trim().isEmpty()) {
            Clients.showNotification("请选择项目", "error", null, "top_center", 3000);
            return;
        }

        if (responsibleId == null || responsibleId.trim().isEmpty()) {
            Clients.showNotification("请选择责任人", "error", null, "top_center", 3000);
            return;
        }

        // 这里可以保存到数据库
        // baseDao.save(...);

        System.out.println("提交的数据:");
        System.out.println("项目ID: " + projectId);
        System.out.println("项目编号: " + projectCode);
        System.out.println("责任人ID: " + responsibleId);
        System.out.println("抄送人IDs: " + ccIds);

        Clients.showNotification("提交成功！", "info", null, "top_center", 3000);
    }
}
