package com.cisdi.ui;

import cn.hutool.core.util.StrUtil;
import com.cisdi.entity.CisdiProblem;
import com.sys.core.zul.RecWnd;
import org.zkoss.zul.CompTarget;
import org.zkoss.zul.Radiogroup;

public class ProcUserRecWnd extends RecWnd {
    @Override
    public void binderTarget(CompTarget target) {
        super.binderTarget(target);
        Radiogroup radiogroup = (Radiogroup) getFellow("procResult");
        CisdiProblem problem = getMainData();
        if (StrUtil.isBlank(problem.getProcessResult())) {
            radiogroup.setSelectedIndex(1);
        } else if (StrUtil.equals(problem.getProcessResult(), "问题处理完成")) {
            radiogroup.setSelectedIndex(0);
        } else {
            radiogroup.setSelectedIndex(1);
        }
        radiogroup.addEventListener("onCheck", event -> {
            int index = radiogroup.getSelectedIndex();
            String oldValue = problem.getProcessResult();
            if (index == 0) {
                problem.setProcessResult("问题处理完成");
            } else {
                problem.setProcessResult("问题未处理完成");
            }
            change(problem, "processResult", oldValue, problem.getProcessResult());
        });
    }

}
