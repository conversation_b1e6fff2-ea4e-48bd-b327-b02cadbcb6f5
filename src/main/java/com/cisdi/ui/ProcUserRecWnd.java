package com.cisdi.ui;

import cn.hutool.core.util.StrUtil;
import com.cisdi.entity.CisdiProblem;
import com.cisdi.entity.CisdiProcessRecord;
import com.sys.common.Utils;
import com.sys.core.zul.RecWnd;
import com.sys.entity.User;
import org.zkoss.zul.CompTarget;
import org.zkoss.zul.Radiogroup;

import java.util.Date;

public class ProcUserRecWnd extends RecWnd {
    @Override
    public void binderTarget(CompTarget target) {
        super.binderTarget(target);
        Radiogroup radiogroup = (Radiogroup) getFellow("procResult");
        CisdiProblem problem = getMainData();
        if (StrUtil.isBlank(problem.getProcessResult())) {
            radiogroup.setSelectedIndex(1);
        } else if (StrUtil.equals(problem.getProcessResult(), "问题处理完成")) {
            radiogroup.setSelectedIndex(0);
        } else {//流程记录
            radiogroup.setSelectedIndex(1);
        }
        radiogroup.addEventListener("onCheck", event -> {
            int index = radiogroup.getSelectedIndex();
            String oldValue = problem.getProcessResult();
            if (index == 0) {
                problem.setProcessResult("问题处理完成");
            } else {
                problem.setProcessResult("问题未处理完成");
            }
            change(problem, "processResult", oldValue, problem.getProcessResult());
        });
    }

    @Override
    public boolean save() {
       CisdiProblem problem = getMainData();
       if (problem.getFirstHandleTime() == null){
           problem.setFirstHandleTime(new Date());
       }
       if (StrUtil.equals(problem.getProcessResult(),"问题处理完成")){
           problem.setStatus("已完成");
           //添加流程记录
           User curUser = Utils.getCurUser();
           CisdiProcessRecord record = new CisdiProcessRecord();
           record.setProblemId(Math.toIntExact(problem.getId()));
           record.setOpterName(curUser.getUserName());
           record.setOpterNum(curUser.getItemNo());
           record.setOpinion("处理");
           record.setSubmitDate(new Date());
           baseDao.saveOrUpdate(record);
       }
       if (StrUtil.equals(problem.getProcessResult(),"问题未处理完成")){
           problem.setStatus("处理中");
       }
        return super.save();
    }




}
