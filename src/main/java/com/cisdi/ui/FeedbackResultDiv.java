package com.cisdi.ui;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zk.ui.event.EventListener;
import org.zkoss.zul.Div;
import org.zkoss.zul.Messagebox;

import java.util.Map;

/**
 * 问卷结果处理页面控制器
 */
public class FeedbackResultDiv extends Div {

    private static final Logger logger = LoggerFactory.getLogger(FeedbackResultDiv.class);

    // 问卷数据
    private SurveyData surveyData;


    public  FeedbackResultDiv() {
        super();

        // 初始化页面数据
        initPageData();

        // ���载问卷结果数据
        loadSurveyData();

        // 注册事件监听器
        addEventListener("onSubmitProcess", new EventListener<Event>() {
            @Override
            public void onEvent(Event event) throws Exception {
                onSubmitProcess(event);
            }
        });

        addEventListener("onGoBack", new EventListener<Event>() {
            @Override
            public void onEvent(Event event) throws Exception {
                onGoBack(event);
            }
        });
    }

    /**
     * 初始化页面数据
     */
    private void initPageData() {
        try {
            logger.info("页面初始化完成");
        } catch (Exception e) {
            logger.error("页面初始化失败", e);
        }
    }

    /**
     * 加载问卷数据
     */
    private void loadSurveyData() {
        try {
            // 从session或数据库获取问卷数据
            surveyData = getSurveyDataFromSession();

            if (surveyData != null) {
                // 通过JavaScript设置显示值
                String script = String.format(
                        "setResultValue('customerNameResult', '%s');" +
                                "setResultValue('projectNameResult', '%s');" +
                                "setResultValue('problemDescResult', '%s');" +
                                "setResultValue('contactNameResult', '%s');" +
                                "setResultValue('contactPhoneResult', '%s');" +
                                "setResultValue('contactEmailResult', '%s');" +
                                "setResultValue('factoryResult', '%s');",
                        escapeJs(surveyData.getCustomerName()),
                        escapeJs(surveyData.getProjectName()),
                        escapeJs(surveyData.getProblemDesc()),
                        escapeJs(surveyData.getContactName()),
                        escapeJs(surveyData.getContactPhone()),
                        escapeJs(surveyData.getContactEmail()),
                        escapeJs(surveyData.getFactory())
                );

                // 执行JavaScript
                org.zkoss.zk.ui.util.Clients.evalJavaScript(script);
            }

        } catch (Exception e) {
            logger.error("加载问卷数据失败", e);
            Messagebox.show("加载数据失败，请刷新页面重试", "错误", Messagebox.OK, Messagebox.ERROR);
        }
    }

    /**
     * 从session获取问卷数据
     */
    private SurveyData getSurveyDataFromSession() {
        try {
            // 这里应该从实际的session或数据库中获取数据
            // 示例数据
            SurveyData data = new SurveyData();
            data.setCustomerName("示例客户单位");
            data.setProjectName("示例项目");
            data.setProblemDesc("这是一个示例问题描述，用于展示问卷结果的显示效果。");
            data.setContactName("张三");
            data.setContactPhone("13800138000");
            data.setContactEmail("<EMAIL>");
            data.setFactory("第一分厂");

            return data;
        } catch (Exception e) {
            logger.error("获取问卷数据失败", e);
            return null;
        }
    }

    /**
     * 处理提交处理结果事件
     */
    public void onSubmitProcess(Event event) {
        try {
            Map<String, Object> data = (Map<String, Object>) event.getData();

            String processResultText = (String) data.get("processResult");
            Boolean isCompleted = (Boolean) data.get("isCompleted");

            // 验证数据
            if (processResultText == null || processResultText.trim().isEmpty()) {
                Messagebox.show("请输入处理结果", "提示", Messagebox.OK, Messagebox.INFORMATION);
                return;
            }

            // 保存处理结果
            boolean success = saveProcessResult(processResultText, isCompleted);

            if (success) {
                Messagebox.show("处理结果提交成功", "成功", Messagebox.OK, Messagebox.INFORMATION,
                        new EventListener<Event>() {
                            @Override
                            public void onEvent(Event event) throws Exception {
                                // 提交成功后跳转到列表页面
                                redirectToList();
                            }
                        });
            } else {
                Messagebox.show("提交失败，请重试", "错误", Messagebox.OK, Messagebox.ERROR);
            }

        } catch (Exception e) {
            logger.error("提交处理结果失败", e);
            Messagebox.show("系统错误，请联系管理员", "错误", Messagebox.OK, Messagebox.ERROR);
        }
    }

    /**
     * 处理返���事件
     */
    public void onGoBack(Event event) {
        try {
            // 返回到上一页面
            redirectToList();
        } catch (Exception e) {
            logger.error("返回操作失败", e);
        }
    }

    /**
     * 保存处理结果
     */
    private boolean saveProcessResult(String processResult, Boolean isCompleted) {
        try {
            // 这里应该调用实际的业务服务保存数据
            logger.info("保存处理结果: {}, 是否完成: {}", processResult, isCompleted);

            // 模拟保存操作
            ProcessResult result = new ProcessResult();
            result.setSurveyId(surveyData != null ? surveyData.getId() : null);
            result.setProcessResult(processResult);
            result.setIsCompleted(isCompleted);
            result.setProcessTime(new java.util.Date());
            result.setProcessUser(getCurrentUser());

            // 调用服务保存
            // processResultService.save(result);

            return true;
        } catch (Exception e) {
            logger.error("保存处理结果失败", e);
            return false;
        }
    }

    /**
     * 获取当前用户
     */
    private String getCurrentUser() {
        try {
            // 从session获取当前用户
            return "当前用户"; // 示例
        } catch (Exception e) {
            return "系统";
        }
    }

    /**
     * 跳转到列表页面
     */
    private void redirectToList() {
        try {
            // 跳转到问卷列表页面
            org.zkoss.zk.ui.Executions.sendRedirect("/survey/list.zul");
        } catch (Exception e) {
            logger.error("页面跳转失败", e);
        }
    }

    /**
     * JavaScript字符串转义
     */
    private String escapeJs(String str) {
        if (str == null) {
            return "";
        }
        return str.replace("'", "\\'")
                .replace("\"", "\\\"")
                .replace("\n", "\\n")
                .replace("\r", "\\r");
    }

    /**
     * 问卷数据实体类
     */
    public static class SurveyData {
        private Long id;
        private String customerName;
        private String projectName;
        private String problemDesc;
        private String contactName;
        private String contactPhone;
        private String contactEmail;
        private String factory;

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getCustomerName() { return customerName; }
        public void setCustomerName(String customerName) { this.customerName = customerName; }

        public String getProjectName() { return projectName; }
        public void setProjectName(String projectName) { this.projectName = projectName; }

        public String getProblemDesc() { return problemDesc; }
        public void setProblemDesc(String problemDesc) { this.problemDesc = problemDesc; }

        public String getContactName() { return contactName; }
        public void setContactName(String contactName) { this.contactName = contactName; }

        public String getContactPhone() { return contactPhone; }
        public void setContactPhone(String contactPhone) { this.contactPhone = contactPhone; }

        public String getContactEmail() { return contactEmail; }
        public void setContactEmail(String contactEmail) { this.contactEmail = contactEmail; }

        public String getFactory() { return factory; }
        public void setFactory(String factory) { this.factory = factory; }
    }

    /**
     * 处理结果实体类
     */
    public static class ProcessResult {
        private Long id;
        private Long surveyId;
        private String processResult;
        private Boolean isCompleted;
        private java.util.Date processTime;
        private String processUser;

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public Long getSurveyId() { return surveyId; }
        public void setSurveyId(Long surveyId) { this.surveyId = surveyId; }

        public String getProcessResult() { return processResult; }
        public void setProcessResult(String processResult) { this.processResult = processResult; }

        public Boolean getIsCompleted() { return isCompleted; }
        public void setIsCompleted(Boolean isCompleted) { this.isCompleted = isCompleted; }

        public java.util.Date getProcessTime() { return processTime; }
        public void setProcessTime(java.util.Date processTime) { this.processTime = processTime; }

        public String getProcessUser() { return processUser; }
        public void setProcessUser(String processUser) { this.processUser = processUser; }
    }
}