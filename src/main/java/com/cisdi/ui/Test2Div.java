package com.cisdi.ui;

import cn.hutool.json.JSONUtil;
import com.sys.common.Utils;
import com.sys.core.api.BaseDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.zkoss.util.media.Media;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zk.ui.event.UploadEvent;
import org.zkoss.zk.ui.util.Clients;
import org.zkoss.zul.Div;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Test2Div extends Div {

    @Autowired
    BaseDao baseDao;

    private List<Map<String, Object>> uploadedFiles = new ArrayList<>();

    public Test2Div() {
        Utils.autowireServer(this);
    }

    /**
     * 页面初始化时加载表单数据
     */
    public void onCreate() {
        loadFormData();
    }

    /**
     * 加载表单数据
     */
    public void loadFormData(Event event) {
        loadFormData();
    }

    private void loadFormData() {
        // 模拟从数据库加载表单数据
        Map<String, Object> formData = new HashMap<>();
        formData.put("customerName", "北京科技有限公司");
        formData.put("projectName", "智慧城市管理平台");
        formData.put("problemDesc", "系统登录模块存在响应缓慢的问题，用户反馈登录时间过长，影响正常使用。");
        formData.put("contactName", "张经理");
        formData.put("contactPhone", "13800138000");
        formData.put("contactEmail", "<EMAIL>");
        formData.put("contactQQ", "123456789");

        // 将数据传递给前端
        Clients.evalJavaScript("loadFormData(" + JSONUtil.toJsonStr(formData) + ")");
    }

    /**
     * 处理文件上传
     */
    public void onFileUpload(Event event) {
        if (event instanceof UploadEvent) {
            UploadEvent uploadEvent = (UploadEvent) event;
            Media[] medias = uploadEvent.getMedias();

            for (Media media : medias) {
                if (media != null) {
                    Map<String, Object> fileInfo = new HashMap<>();
                    fileInfo.put("fileName", media.getName());
                    fileInfo.put("fileSize", media.getByteData().length);
                    fileInfo.put("contentType", media.getContentType());

                    // 这里可以保存文件到服务器或数据库
                    // saveFileToServer(media);

                    uploadedFiles.add(fileInfo);

                    System.out.println("上传文件: " + media.getName() +
                                     ", 大小: " + media.getByteData().length + " bytes");
                }
            }

            // 通知前端更新文件列表
            Clients.evalJavaScript("updateFileList(" + JSONUtil.toJsonStr(uploadedFiles) + ")");
            Clients.showNotification("文件上传成功", "info", null, "top_center", 3000);
        }
    }

    /**
     * 删除上传的文件
     */
    public void removeFile(Event event) {
        String fileName = (String) event.getData();
        uploadedFiles.removeIf(file -> fileName.equals(file.get("fileName")));

        // 通知前端更新文件列表
        Clients.evalJavaScript("updateFileList(" + JSONUtil.toJsonStr(uploadedFiles) + ")");
        Clients.showNotification("文件已删除", "info", null, "top_center", 3000);
    }

    /**
     * 提交表单数据
     */
    public void submitForm(Event event) {
        Map<String, Object> formData = (Map<String, Object>) event.getData();

        // 获取表单数据
        String customerName = (String) formData.get("customerName");
        String projectName = (String) formData.get("projectName");
        String problemDesc = (String) formData.get("problemDesc");
        String contactName = (String) formData.get("contactName");
        String contactPhone = (String) formData.get("contactPhone");
        String contactEmail = (String) formData.get("contactEmail");
        String contactQQ = (String) formData.get("contactQQ");
        String processResult = (String) formData.get("processResult");
        String processStatus = (String) formData.get("processStatus");

        // 验证必填项
        if (processResult == null || processResult.trim().isEmpty()) {
            Clients.showNotification("请填写处理结果", "error", null, "top_center", 3000);
            return;
        }

        // 构建完整的提交数据
        Map<String, Object> submitData = new HashMap<>();
        submitData.put("customerName", customerName);
        submitData.put("projectName", projectName);
        submitData.put("problemDesc", problemDesc);
        submitData.put("contactName", contactName);
        submitData.put("contactPhone", contactPhone);
        submitData.put("contactEmail", contactEmail);
        submitData.put("contactQQ", contactQQ);
        submitData.put("processResult", processResult);
        submitData.put("processStatus", processStatus);
        submitData.put("uploadedFiles", uploadedFiles);

        // 这里可以保存到数据库
        // baseDao.save(submitData);

        // 打印提交的数据
        System.out.println("=== 表单提交数据 ===");
        System.out.println("客户单位名称: " + customerName);
        System.out.println("项目名称: " + projectName);
        System.out.println("问题描述: " + problemDesc);
        System.out.println("联系人姓名: " + contactName);
        System.out.println("联系电话: " + contactPhone);
        System.out.println("联系邮箱: " + contactEmail);
        System.out.println("QQ号码: " + contactQQ);
        System.out.println("处理结果: " + processResult);
        System.out.println("处理状态: " + processStatus);
        System.out.println("上传文件数量: " + uploadedFiles.size());
        for (Map<String, Object> file : uploadedFiles) {
            System.out.println("  - " + file.get("fileName") + " (" + file.get("fileSize") + " bytes)");
        }
        System.out.println("==================");

        Clients.showNotification("提交成功！", "info", null, "top_center", 3000);
    }

    /**
     * 获取上传的文件列表
     */
    public void getUploadedFiles(Event event) {
        Clients.evalJavaScript("updateFileList(" + JSONUtil.toJsonStr(uploadedFiles) + ")");
    }
}
