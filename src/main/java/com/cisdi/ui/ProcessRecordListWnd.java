package com.cisdi.ui;

import com.cisdi.entity.CisdiProcessRecord;
import com.sys.core.zul.ListWnd;

import java.util.List;

/**
 * <AUTHOR>
 * @version 0.0.1
 * @description ProcessRecordListWnd
 * @since 2025/8/12 09:49
 */
public class ProcessRecordListWnd extends ListWnd {

    @Override
    public void processData(List<Object> data) {
        for (Object datum : data) {
            CisdiProcessRecord record = (CisdiProcessRecord) datum;
            record.setOpterName(record.getOpterName()+record.getOpterNum());
        }
        super.processData(data);
    }
}
