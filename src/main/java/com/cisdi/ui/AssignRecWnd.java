package com.cisdi.ui;

import com.cisdi.entity.CisdiProblem;
import com.cisdi.entity.CisdiProcessRecord;
import com.sys.common.Utils;
import com.sys.core.zul.RecWnd;
import com.sys.entity.User;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 0.0.1
 * @description AssignRecWnd
 * @since 2025/8/11 13:30
 */
public class AssignRecWnd extends RecWnd {

    @Override
    public boolean save() {
        CisdiProblem problem = getMainData();
        boolean save = super.save();
        if (save){
            User curUser = Utils.getCurUser();
            //保存流程记录
            CisdiProcessRecord record = new CisdiProcessRecord();
            record.setProblemId(Math.toIntExact(problem.getId()));
            record.setOpterName(curUser.getUserName());
            record.setOpterNum(curUser.getItemNo());
            record.setOpinion("指派");
            record.setSubmitDate(new Date());
        }
        return save;
    }
}
