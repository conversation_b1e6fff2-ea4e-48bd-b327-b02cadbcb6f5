package com.cisdi.ui;


import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.cisdi.entity.CisdiProblem;
import com.sys.common.Constants;
import com.sys.common.Utils;
import com.sys.core.api.BaseDao;
import com.sys.core.cache.CacheServer;
import com.sys.entity.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.zkoss.image.AImage;
import org.zkoss.io.Files;
import org.zkoss.json.JSONArray;
import org.zkoss.json.JSONObject;
import org.zkoss.zhtml.Div;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zk.ui.util.Clients;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class ProbleDiv extends Div {
    @Autowired
    BaseDao baseDao;
    @Autowired
    CacheServer cacheServer;

    public ProbleDiv() {
        Utils.autowireServer(this);
    }

    public void sendPhoneCode(Event event) {
        String phone = (String) event.getData();
        String code = RandomUtil.randomNumbers(4);
        code = "8888";
        cacheServer.putTimedCache(phone, code, 60000);
    }

    public void verification(Event event) {
        JSONObject data = (JSONObject) event.getData();
        String phone = (String) data.get("phone");
        String code = (String) data.get("code");
        if (ObjUtil.equal(cacheServer.getTimedCache(phone), code)) {//验证码验证成功
            CisdiProblem problem = new CisdiProblem();
            problem.setContactName((String) data.get("contactName"));
            problem.setProjectName((String) data.get("projectName"));
            problem.setProblemDesc((String) data.get("problemDesc"));
            problem.setCustomerName((String) data.get("customerName"));
            problem.setContactPhone((String) data.get("contactPhone"));
            problem.setContactEmail((String) data.get("contactEmail"));
            problem.setFactory((String) data.get("factory"));
            JSONArray array = (JSONArray) data.get("uploadFile");
            if (!array.isEmpty()) {
                problem.setUploadFile(uploadFiles(array));
            }
            baseDao.save(problem);
            Clients.evalJavaScript("closeVerification();showSuccessPage();");
        } else {
            Clients.evalJavaScript("alert('验证码错误')");
        }
    }

    private String uploadFiles(JSONArray array) {
        List<String> files = new ArrayList<>();
        for (int i = 0; i < array.size(); i++) {
            try {
                Document doc = new Document();
                AImage media = (AImage) array.get(i);
                String savename = StrUtil.uuid();
                doc.setSaveName(savename);
                String filePath = Constants.resourceLocation + "/images/" + savename + "." + media.getFormat();
                Files.copy(new File(filePath), media.getStreamData());
                doc.setUrl("/%s/files/images/" + savename + "." + media.getFormat());
                doc.setUrl("%s/images/" + savename + "." + media.getFormat());
                doc.setFileName(media.getName());
                if (StrUtil.containsAny(media.getFormat(), "jpg", "jpeg", "png", "gif", "bmp", "tiff", "webp", "svg")) {
                    byte[] resize = resizeImage(media.getByteData(), 80, 80, media.getFormat());
                    if (null != resize) {
                        doc.setPreviewData(resize);
                    }
                    doc.setWidth(media.getWidth());
                    doc.setHeight(media.getHeight());
                }
                files.add(savename);
                baseDao.save(doc);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return String.join(",", files);
    }

    public static byte[] resizeImage(byte[] imageBytes, int targetWidth, int targetHeight, String type) {
        try {
            ByteArrayInputStream bais = new ByteArrayInputStream(imageBytes);
            BufferedImage originalImage = ImageIO.read(bais);
            BufferedImage resizedImage = new BufferedImage(targetWidth, targetHeight, originalImage.getType());
            Graphics2D g2d = resizedImage.createGraphics();
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.drawImage(originalImage, 0, 0, targetWidth, targetHeight, null);
            g2d.dispose();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(resizedImage, type, baos); // You can change the format (e.g., "png", "gif") as needed
            baos.flush();
            return baos.toByteArray();
        } catch (Exception e) {
            return null;
        }
    }

}
