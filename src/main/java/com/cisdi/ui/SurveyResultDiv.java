package com.cisdi.ui;

import org.zkoss.zhtml.Div;
import org.zkoss.zk.ui.Page;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zk.ui.util.Clients;
import org.zkoss.zul.Messagebox;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 问卷结果展示组件
 * 用于显示问卷数据并处理反馈指派
 */
public class SurveyResultDiv extends Div {

    private static final long serialVersionUID = 1L;

    // 问卷数据
    private SurveyData surveyData;

    public SurveyResultDiv() {
        super();
        // 初始化固定数据
        initSurveyData();
        // 注册事件监听器
        registerEventListeners();
    }


    @Override
    public void onPageAttached(Page newpage, Page oldpage) {
        super.onPageAttached(newpage, oldpage);
        // 页面加载完成后设置数据
        loadSurveyDataToPage();
    }

    /**
     * 初始化固定的问卷数据
     */
    private void initSurveyData() {
        surveyData = new SurveyData();
        surveyData.setCustomerName("重庆钢铁集团有限公司");
        surveyData.setProjectName("智能制造数字化转型项目");
        surveyData.setProblemDesc("系统在高并发访问时响应速度较慢，特别是在数据查询和报表生成环节。" +
                "建议优化数据库查询性能，增加缓存机制，并考虑采用分布式架构来提升系统整体性能。" +
                "另外，用户界面在移动端的适配还需要进一步完善。");
        surveyData.setContactName("李明");
        surveyData.setContactPhone("138****8888");
        surveyData.setContactEmail("<EMAIL>");
        surveyData.setFactory("第二炼钢厂");
    }

    /**
     * 注册事件监听器
     */
    private void registerEventListeners() {
        // 监听提交指派事件
//        addEventListener("submitAssignment", new EventListener<Event>() {
//            @Override
//            public void onEvent(Event event) throws Exception {
//                handleSubmitAssignment(event);
//            }
//        });

//        // 监听返回事件
//        addEventListener("goBack", new EventListener<Event>() {
//            @Override
//            public void onEvent(Event event) throws Exception {
//                handleGoBack(event);
//            }
//        });
    }

    /**
     * 将问卷数据加载到页面
     */
    private void loadSurveyDataToPage() {
        // 通过JavaScript设置页面数据
        StringBuilder script = new StringBuilder();
        script.append("setTimeout(function() {");
        script.append("if (typeof handleSurveyData === 'function') {");
        script.append("handleSurveyData({");
        script.append("customerName: '").append(escapeJs(surveyData.getCustomerName())).append("',");
        script.append("projectName: '").append(escapeJs(surveyData.getProjectName())).append("',");
        script.append("problemDesc: '").append(escapeJs(surveyData.getProblemDesc())).append("',");
        script.append("contactName: '").append(escapeJs(surveyData.getContactName())).append("',");
        script.append("contactPhone: '").append(escapeJs(surveyData.getContactPhone())).append("',");
        script.append("contactEmail: '").append(escapeJs(surveyData.getContactEmail())).append("',");
        script.append("factory: '").append(escapeJs(surveyData.getFactory())).append("'");
        script.append("});");
        script.append("}");
        script.append("}, 100);");

        Clients.evalJavaScript(script.toString());
    }

    /**
     * 处理提交指派事件
     */
    @SuppressWarnings("unchecked")
    private void handleSubmitAssignment(Event event) {
        try {
            // 获取前端传递的数据
            Map<String, Object> data = (Map<String, Object>) event.getData();

            if (data == null) {
                showError("数据为空");
                return;
            }

            String project = (String) data.get("project");
            String responsible = (String) data.get("responsible");
            String cc = (String) data.get("cc");

            // 验证数据
            if (project == null || project.trim().isEmpty()) {
                showError("项目名称不能为空");
                return;
            }

            if (responsible == null || responsible.trim().isEmpty()) {
                showError("责任人不能为空");
                return;
            }

            // 创建指派数据
            AssignmentData assignment = new AssignmentData();
            assignment.setProject(project.trim());
            assignment.setResponsible(responsible.trim());
            assignment.setCc(cc != null ? cc.trim() : "");
            assignment.setSurveyId(surveyData.getId());

            // 模拟保存到数据库
            boolean success = saveAssignment(assignment);

            if (success) {
                // 通知前端提交成功
                Clients.evalJavaScript("if (typeof onAssignmentSuccess === 'function') { onAssignmentSuccess(); }");

                // 显示成功消息
                Messagebox.show("指派成功！", "提示", Messagebox.OK, Messagebox.INFORMATION);

            } else {
                showError("保存失败，请重试");
            }

        } catch (Exception e) {
            e.printStackTrace();
            showError("系统错误：" + e.getMessage());
        }
    }

    /**
     * 处理返回事件
     */
    private void handleGoBack(Event event) {
        try {
            System.out.println("用户点击返回按钮");

        } catch (Exception e) {
            e.printStackTrace();
            showError("返回操作失败：" + e.getMessage());
        }
    }

    /**
     * 模拟保存指派数据到数据库
     */
    private boolean saveAssignment(AssignmentData assignment) {
        try {
            // 模拟保存过程
            System.out.println("保存指派数据：");
            System.out.println("项目：" + assignment.getProject());
            System.out.println("责任人：" + assignment.getResponsible());
            System.out.println("抄送人：" + assignment.getCc());
            System.out.println("问卷ID：" + assignment.getSurveyId());

            // 模拟保存成功
            return true;

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 显示错误消息
     */
    private void showError(String message) {
        Clients.evalJavaScript("if (typeof onAssignmentError === 'function') { onAssignmentError('" + escapeJs(message) + "'); }");
    }

    public void submitAssignment(Event event){
        System.out.println("11");
    }

    public void goBack(){
        System.out.println("222");
    }

    /**
     * JavaScript字符串转义
     */
    private String escapeJs(String str) {
        if (str == null) return "";
        return str.replace("\\", "\\\\")
                .replace("'", "\\'")
                .replace("\"", "\\\"")
                .replace("\n", "\\n")
                .replace("\r", "\\r")
                .replace("\t", "\\t");
    }

    // Getter和Setter方法
    public SurveyData getSurveyData() {
        return surveyData;
    }

    public void setSurveyData(SurveyData surveyData) {
        this.surveyData = surveyData;
        if (getPage() != null) {
            loadSurveyDataToPage();
        }
    }

    /**
     * 问卷数据实体类
     */
    public static class SurveyData implements Serializable {
        private static final long serialVersionUID = 1L;

        private String id = "SURVEY_001";
        private String customerName;
        private String projectName;
        private String problemDesc;
        private String contactName;
        private String contactPhone;
        private String contactEmail;
        private String factory;

        // Getter和Setter方法
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }

        public String getCustomerName() { return customerName; }
        public void setCustomerName(String customerName) { this.customerName = customerName; }

        public String getProjectName() { return projectName; }
        public void setProjectName(String projectName) { this.projectName = projectName; }

        public String getProblemDesc() { return problemDesc; }
        public void setProblemDesc(String problemDesc) { this.problemDesc = problemDesc; }

        public String getContactName() { return contactName; }
        public void setContactName(String contactName) { this.contactName = contactName; }

        public String getContactPhone() { return contactPhone; }
        public void setContactPhone(String contactPhone) { this.contactPhone = contactPhone; }

        public String getContactEmail() { return contactEmail; }
        public void setContactEmail(String contactEmail) { this.contactEmail = contactEmail; }

        public String getFactory() { return factory; }
        public void setFactory(String factory) { this.factory = factory; }
    }

    /**
     * 指派数据实体类
     */
    public static class AssignmentData implements Serializable {
        private static final long serialVersionUID = 1L;

        private String surveyId;
        private String project;
        private String responsible;
        private String cc;
        private Date createTime = new Date();

        // Getter和Setter方法
        public String getSurveyId() { return surveyId; }
        public void setSurveyId(String surveyId) { this.surveyId = surveyId; }

        public String getProject() { return project; }
        public void setProject(String project) { this.project = project; }

        public String getResponsible() { return responsible; }
        public void setResponsible(String responsible) { this.responsible = responsible; }

        public String getCc() { return cc; }
        public void setCc(String cc) { this.cc = cc; }

        public Date getCreateTime() { return createTime; }
        public void setCreateTime(Date createTime) { this.createTime = createTime; }
    }
}