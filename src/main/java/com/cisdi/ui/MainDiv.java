package com.cisdi.ui;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.sys.common.UserInfo;
import com.sys.common.Utils;
import com.sys.core.api.BaseDao;
import com.sys.core.cache.CacheServer;
import com.sys.core.server.UserServer;
import com.sys.core.zul.CardData;
import com.sys.core.zul.RecWnd;
import org.springframework.beans.factory.annotation.Autowired;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zk.ui.event.Events;
import org.zkoss.zk.ui.util.Clients;
import org.zkoss.zul.CompTarget;
import org.zkoss.zul.Div;
import org.zkoss.zul.Listitem;

import java.util.HashMap;
import java.util.Map;

public class MainDiv extends RecWnd {
    @Autowired
    BaseDao baseDao;
    @Autowired
    CacheServer cacheServer;
    Map<String, CardData> cardDataMap = new HashMap<>();
    Div mainDiv, detailPanel;
    org.zkoss.zul.A detailClose;
    private boolean detailOpen = false;
    @Autowired
    UserServer userServer;

    public MainDiv() {
        String userid = Utils.getHttpServletRequest().getParameter("userid");
        if (StrUtil.isNotBlank(userid)) {
            UserInfo userInfo = userServer.getUserInfo(userid, "a753c776ff3ed4fefa2af948af87448910153281");
            Utils.addSession("userInfo", userInfo);
        }
        Utils.autowireServer(this);
    }

    public boolean isDetailOpen() {
        return detailOpen;
    }

    public void onCreate() {
        super.onCreate();
        detailClose = (org.zkoss.zul.A) getFellow("detailClose");
        detailPanel = (Div) getFellow("detail-panel");

        Div topDiv = (Div) getFellow("cardDiv");
        topDiv.setStyle("display: grid; grid-template-columns: repeat(5, 1fr);");

        CardData cardData = new CardData("待指派", "28", "人", "fa fa-user-plus", "#4CAF50", "up", "+10", "较昨日");
        topDiv.appendChild(cardData);
        cardData = new CardData("待处理", "24", "人", "fa fa-sign-out", "#2196F3");

        cardData.setTrendValue("+5");
        cardData.setTrendType("up");
        cardDataMap.put("a1",cardData);
        cardDataMap.get("a1").setCount("222");
        topDiv.appendChild(cardData);
        cardData = new CardData("处理中", "10", "人", "fa fa-bed", "#FF9800");

        cardData.setTrendValue("+5");
        cardData.setTrendType("up");
        topDiv.appendChild(cardData);
        cardData = new CardData("已完成", "88", "个", "fa fa-ambulance", "#52c41a");
        cardData.setTrendValue("+5");
        cardData.setTrendType("up");

        topDiv.appendChild(cardData);
        cardData = new CardData("超时预警", "88", "个", "fa fa-ambulance", "#F44336");
        cardData.setTrendValue("+5");
        cardData.setTrendType("up");

        topDiv.appendChild(cardData);

        mainDiv = (Div) getFellow("mainDiv");
        detailClose.addEventListener("onClick", evt -> {
            detailOpen = false;
            mainDiv.setStyle("display: grid; grid-template-columns: 1fr 0px;");
            detailPanel.setStyle("display: none;");
        });

        Events.postEvent("onClick", detailClose, true);
    }

    public void onDetail(Event event) {
        detailOpen = true;
        detailPanel.setStyle(null);
        CompTarget target;
        if (event.getData() instanceof Listitem) {
            target = (CompTarget) event.getData();
        } else {
            Map<String, Object> map = (Map<String, Object>) event.getData();
            target = (CompTarget) map.get("target");
        }
        Object data = target.getValue();
        mainDiv.setStyle("display: grid; grid-template-columns: 1fr 330px;");
        Clients.evalJavaScript("updateDetailPanel(" + JSONUtil.toJsonStr(data) + ")");
    }


}
