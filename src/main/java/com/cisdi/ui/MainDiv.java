package com.cisdi.ui;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.cisdi.entity.CisdiProblem;
import com.sys.common.UserInfo;
import com.sys.common.Utils;
import com.sys.core.api.BaseDao;
import com.sys.core.cache.CacheServer;
import com.sys.core.server.UserServer;
import com.sys.core.zul.CardData;
import com.sys.core.zul.RecWnd;
import com.sys.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zk.ui.event.Events;
import org.zkoss.zk.ui.util.Clients;
import org.zkoss.zul.CompTarget;
import org.zkoss.zul.Div;
import org.zkoss.zul.Listitem;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MainDiv extends RecWnd {
    @Autowired
    BaseDao baseDao;
    @Autowired
    CacheServer cacheServer;
    Map<String, CardData> cardDataMap = new HashMap<>();
    Div mainDiv, detailPanel;
    org.zkoss.zul.A detailClose;
    private boolean detailOpen = false;
    @Autowired
    UserServer userServer;

    public MainDiv() {
        String userid = Utils.getHttpServletRequest().getParameter("userid");
        if (StrUtil.isNotBlank(userid)) {
            UserInfo userInfo = userServer.getUserInfo(userid, "a753c776ff3ed4fefa2af948af87448910153281");
            Utils.addSession("userInfo", userInfo);
        }
        Utils.autowireServer(this);
    }

    public boolean isDetailOpen() {
        return detailOpen;
    }

    public void onCreate() {
        super.onCreate();
        detailClose = (org.zkoss.zul.A) getFellow("detailClose");
        detailPanel = (Div) getFellow("detail-panel");

        Div topDiv = (Div) getFellow("cardDiv");
        topDiv.setStyle("display: grid; grid-template-columns: 230px 1fr;");

        //基础数据查询
        User curUser = Utils.getCurUser();
        Map param = new HashMap();
        param.put("itemNo", curUser.getItemNo());
        String hql = "from CisdiProblem where 1=1";
        if (!Utils.getUserInfo().getGrpList().contains("1001")){
            hql += " and responsiblePersonJobNum=:itemNo or ccUserJobNum=:itemNo";
        }
        List<CisdiProblem> problemList = baseDao.findHql(hql, param);

        long assignedCount = problemList.stream().filter(x -> StrUtil.equals(x.getStatus(), "待指派")).count();
        long treatProcessingCount = problemList.stream().filter(x -> StrUtil.equals(x.getStatus(), "待处理")).count();
        long processingCount = problemList.stream().filter(x -> StrUtil.equals(x.getStatus(), "处理中")).count();
        long completedCount = problemList.stream().filter(x -> StrUtil.equals(x.getStatus(), "已完成")).count();
        //预警超时
        long timeoutCount = problemList.stream().filter(x -> !StrUtil.equals(x.getStatus(), "已完成") && x.getIsWarning()).count();

        // 创建圆形图表组件
        PieChart pieChart = new PieChart();
        pieChart.setHeight("120px");
       // pieChart.setStyle("margin-top:10px");
        topDiv.appendChild(pieChart);
        pieChart.updateData((int) processingCount, (int) treatProcessingCount, (int) completedCount, (int) assignedCount);

        System.out.println();
        // 创建卡片容器
        Div cardsContainer = new Div();
        cardsContainer.setStyle("display: grid; grid-template-columns: repeat(5, 1fr);");
        CardData cardData = new CardData("待指派", String.valueOf(assignedCount), "", "fa fa-user-plus", "#FF9800");
        cardDataMap.put("待指派",cardData);
        cardData.setShowTrend(false);
        cardsContainer.appendChild(cardData);
        cardData = new CardData("待处理", String.valueOf(treatProcessingCount), "", "fa fa-sign-out", "#2196F3");
        cardData.setShowTrend(false);

//        cardData.setTrendValue("+5");
//        cardData.setTrendType("up");
        cardDataMap.put("待处理",cardData);
        cardsContainer.appendChild(cardData);
        cardData = new CardData("处理中", String.valueOf(processingCount), "", "fa fa-bed", "#9C27B0");
        cardData.setShowTrend(false);
        cardDataMap.put("处理中",cardData);
//        cardData.setTrendValue("+5");
//        cardData.setTrendType("up");
        cardsContainer.appendChild(cardData);
        cardData = new CardData("已完成", String.valueOf(completedCount), "", "fa fa-ambulance", "#52c41a");
        cardData.setShowTrend(false);
        cardDataMap.put("已完成",cardData);
//        cardData.setTrendValue("+5");
//        cardData.setTrendType("up");

        cardsContainer.appendChild(cardData);
        cardData = new CardData("超时预警", String.valueOf(timeoutCount), "", "fa fa-ambulance", "#F44336");
        cardDataMap.put("超时预警",cardData);
        cardData.setShowTrend(false);
//        cardData.setTrendValue("+5");
//        cardData.setTrendType("up");

        cardsContainer.appendChild(cardData);

        topDiv.appendChild(cardsContainer);

        mainDiv = (Div) getFellow("mainDiv");
        detailClose.addEventListener("onClick", evt -> {
            detailOpen = false;
            mainDiv.setStyle("display: grid; grid-template-columns: 1fr 0px;");
            detailPanel.setStyle("display: none;");
        });

        Events.postEvent("onClick", detailClose, true);
    }



    public void onDetail(Event event) {
        detailOpen = true;
        detailPanel.setStyle(null);
        CompTarget target;
        if (event.getData() instanceof Listitem) {
            target = (CompTarget) event.getData();
        } else {
            Map<String, Object> map = (Map<String, Object>) event.getData();
            target = (CompTarget) map.get("target");
        }
        Object data = target.getValue();
        mainDiv.setStyle("display: grid; grid-template-columns: 1fr 330px;");
        Clients.evalJavaScript("updateDetailPanel(" + JSONUtil.toJsonStr(data) + ")");
    }



}
