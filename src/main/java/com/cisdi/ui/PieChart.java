package com.cisdi.ui;

import org.zkoss.zhtml.Div;
import org.zkoss.zhtml.Span;
import org.zkoss.zhtml.Text;

public class PieChart extends org.zkoss.zul.Div {

    public PieChart() {
        // 使用与页面主题匹配的样式
        setStyle("background: rgba(255, 255, 255, 0.95); border-radius: 12px;margin-top:5px; padding:10px " +
                "box-shadow: 0 4px 20px rgba(102, 126, 234, 0.15); height: 100%; " +
                "border: 1px solid rgba(255, 255, 255, 0.3);");
        //createChart();
    }

    private void createConicPie(Div container, int processing, int myProcessing, int completed, int pending) {
        // 计算总数和比例
        int total = processing + myProcessing + completed + pending;
        if (total == 0) return;

        // 计算每个扇形的真实百分比（基于总和）
        double processingPercent = (processing * 100.0) / total;
        double myProcessingPercent = (myProcessing * 100.0) / total;
        double completedPercent = (completed * 100.0) / total;
        double pendingPercent = (pending * 100.0) / total;

        // 计算累积百分比
        double angle1 = processingPercent;
        double angle2 = angle1 + myProcessingPercent;
        double angle3 = angle2 + completedPercent;

        // 调试信息
        System.out.println("=== 饼图比例计算 ===");
        System.out.println("原始数据: 处理中=" + processing + ", 待处理=" + myProcessing + ", 已处理=" + completed + ", 待指派=" + pending);
        System.out.println("总和: " + total);
        System.out.println("处理中: " + String.format("%.2f", processingPercent) + "% (0% - " + String.format("%.2f", angle1) + "%)");
        System.out.println("待处理: " + String.format("%.2f", myProcessingPercent) + "% (" + String.format("%.2f", angle1) + "% - " + String.format("%.2f", angle2) + "%)");
        System.out.println("已处理: " + String.format("%.2f", completedPercent) + "% (" + String.format("%.2f", angle2) + "% - " + String.format("%.2f", angle3) + "%)");
        System.out.println("待指派: " + String.format("%.2f", pendingPercent) + "% (" + String.format("%.2f", angle3) + "% - 100%)");

        // 创建饼图外圈，使用精确的百分比
        Div pieChart = new Div();
        String gradientStyle = "conic-gradient(" +
                "#4CAF50 0% " + String.format("%.2f", angle1) + "%, " +
                "#2196F3 " + String.format("%.2f", angle1) + "% " + String.format("%.2f", angle2) + "%, " +
                "#9C27B0 " + String.format("%.2f", angle2) + "% " + String.format("%.2f", angle3) + "%, " +
                "#FF9800 " + String.format("%.2f", angle3) + "% 100%" +
                ")";

        System.out.println("CSS Gradient: " + gradientStyle);

        pieChart.setStyle("width: 100px; height: 100px; border-radius: 50%; position: relative; background: " + gradientStyle + ";");

        // 创建中心圆形
        Div centerCircle = new Div();
        centerCircle.setStyle("position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); " +
                "width: 40px; height: 40px; background: rgba(255, 255, 255, 0.95); border-radius: 50%; " +
                "border: 1px solid rgba(102, 126, 234, 0.2);");

        pieChart.appendChild(centerCircle);
        container.appendChild(pieChart);
    }

    private void createLegendItem(Div container, String color, String text) {
        Div item = new Div();
        item.setStyle("margin-bottom: 8px; display: flex; align-items: center;");

        Span colorBox = new Span();
        colorBox.setStyle("display: inline-block; width: 10px; height: 10px; background: " + color +
                "; border-radius: 2px; margin-right: 6px;");

        Span textSpan = new Span();
        textSpan.setStyle("font-size: 12px; color: #333; font-weight: 500;");
        textSpan.appendChild(new Text(text));

        item.appendChild(colorBox);
        item.appendChild(textSpan);
        container.appendChild(item);
    }

    /**
     * 更新图表数据
     */
    public void updateData(int processing, int myProcessing, int completed, int pending) {
        // 清除现有内容
        getChildren().clear();

        // 重新创建图表
        createUpdatedChart(processing, myProcessing, completed, pending);
    }

    private void createUpdatedChart(int processing, int myProcessing, int completed, int pending) {
        // 创建主容器
        Div mainContainer = new Div();
        mainContainer.setStyle("display: flex; align-items: center; gap: 15px; height: 100%;");

        // 创建图表容器
        Div chartContainer = new Div();
        chartContainer.setStyle("position: relative; width: 100px; height: 100px; flex-shrink: 0;");

        // 创建更新的conic-gradient饼图
        createConicPie(chartContainer, processing, myProcessing, completed, pending);

        // 创建图例容器
        Div legendContainer = new Div();
        legendContainer.setStyle("flex: 1; min-width: 120px;");

        // 计算实际百分比
        int total = processing + myProcessing + completed + pending;
        double processingPercent = (processing * 100.0) / total;
        double myProcessingPercent = (myProcessing * 100.0) / total;
        double completedPercent = (completed * 100.0) / total;
        double pendingPercent = (pending * 100.0) / total;

        // 创建更新的图例项
        createLegendItem(legendContainer, "#4CAF50", "处理中 " + String.format("%.1f", processingPercent) + "%");
        createLegendItem(legendContainer, "#2196F3", "待处理 " + String.format("%.1f", myProcessingPercent) + "%");
        createLegendItem(legendContainer, "#9C27B0", "已处理 " + String.format("%.1f", completedPercent) + "%");
        createLegendItem(legendContainer, "#FF9800", "待指派 " + String.format("%.1f", pendingPercent) + "%");

        mainContainer.appendChild(chartContainer);
        mainContainer.appendChild(legendContainer);
        appendChild(mainContainer);
    }
}
